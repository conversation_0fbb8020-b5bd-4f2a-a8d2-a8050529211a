import os
import json
import faiss
import numpy as np
from tqdm import tqdm
from utils.cwe_find import CWEProcessor
from models.embedding_helper import CodeEmbedder


class VectorDatabaseBuilder:
    def __init__(self, output_dir="vectorbase/indices", embedding_model="bge-code-v1"):
        """
        初始化向量数据库构建器
        
        参数:
        output_dir (str): 向量索引保存的目录
        embedding_model (str): 使用的嵌入模型类型
        """
        self.output_dir = output_dir
        self.cwe_processor = CWEProcessor()
        self.embedder = CodeEmbedder(model_type=embedding_model)
        
        self.main_cwe_types = [
            "CWE-189", "CWE-254", "CWE-264", "CWE-284", "CWE-310",
            "CWE-399", "CWE-664", "CWE-682", "CWE-691", "CWE-703", "CWE-707"
        ]
        
        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)
    
    def load_strategy_analysis_data(self, filepaths):
        """
        加载策略分析输出数据，支持多个文件路径
        
        参数:
        filepaths (str or list): 数据文件路径或路径列表
        
        返回:
        list: 加载的数据
        """
        # 如果传入的是单个文件路径字符串，转换为列表
        if isinstance(filepaths, str):
            filepaths = [filepaths]
        
        combined_data = []
        
        for filepath in filepaths:
            if os.path.exists(filepath):
                print(f"正在加载数据文件: {filepath}")
                try:
                    with open(filepath, 'r', encoding='utf-8') as file:
                        data = json.load(file)
                        if isinstance(data, list):
                            combined_data.extend(data)
                        else:
                            combined_data.append(data)
                    print(f"从 {filepath} 加载了 {len(data) if isinstance(data, list) else 1} 条数据")
                except Exception as e:
                    print(f"加载文件 {filepath} 时出错: {e}")
            else:
                print(f"数据文件不存在: {filepath}")
        
        return combined_data
    
    def get_top_parent_cwe(self, cwe_id):
        """
        获取给定 CWE ID 的顶层父类型
        
        参数:
        cwe_id (str): CWE ID，例如 "CWE-476"
        
        返回:
        str: 顶层父 CWE ID
        """
        if cwe_id is None or not cwe_id.startswith("CWE-"):
            return "CWE-0"
        
        try:
            _, top_parent = self.cwe_processor.process_cwe(cwe_id)
            
            # 如果顶层父类型不在指定的主要 CWE 类型列表中，则归类为 CWE-0
            if top_parent not in self.main_cwe_types:
                return "CWE-0"
            
            return top_parent
        except Exception as e:
            print(f"处理 CWE ID {cwe_id} 时出错: {e}")
            return "CWE-0"
    
    def process_data(self, data):
        """
        处理数据，按顶层父 CWE 类型进行分组
        
        参数:
        data (list): 原始数据列表
        
        返回:
        dict: 按顶层父 CWE 类型分组的数据
        """
        grouped_data = {}
        
        for item in tqdm(data, desc="按 CWE 类型处理数据"):
            cwe_id = item.get("cwe_type")
            top_parent = self.get_top_parent_cwe(cwe_id)
            
            if top_parent not in grouped_data:
                grouped_data[top_parent] = []
            
            grouped_data[top_parent].append(item)
        
        return grouped_data
    
    def vectorize_text(self, text):
        """
        将文本向量化
        
        参数:
        text (str): 要向量化的文本
        
        返回:
        numpy.ndarray: 文本的向量表示
        """
        if not text or len(text.strip()) == 0:
            # 如果文本为空，返回零向量
            return np.zeros(self.embedder.dim)
        
        # 使用embedder获取嵌入向量
        vectors = self.embedder.get_embeddings([text])
        return vectors[0]
    
    def build_indices(self, grouped_data):
        """
        为每个顶层父 CWE 类型构建 FAISS 索引
        
        参数:
        grouped_data (dict): 按顶层父 CWE 类型分组的数据
        """
        for cwe_type, items in tqdm(grouped_data.items(), desc="构建 FAISS 索引"):
            # 如果该类型没有数据，则跳过
            if not items:
                continue
            
            # 提取向量和元数据
            vectors = []
            metadata = []
            
            for item in items:
                # 使用 final_llm1_pre_repair_state 进行向量化
                pre_repair_state = item.get("final_llm1_pre_repair_state", "")
                if not pre_repair_state:
                    continue
                
                vector = self.vectorize_text(pre_repair_state)
                
                # 如果向量化失败，则跳过
                if vector is None or len(vector) == 0:
                    continue
                
                vectors.append(vector)
                
                # 提取元数据
                metadata.append({
                    "original_row_index": item.get("original_row_index"),
                    "cwe_type": item.get("cwe_type"),
                    "cve_id": item.get("cve_id"),
                    "top_parent_cwe": cwe_type,
                    "code_before": item.get("code_before"),
                    "code_after_ground_truth": item.get("code_after_ground_truth"),
                    "final_llm1_cwe_specific_strategy": item.get("final_llm1_cwe_specific_strategy"),
                    "final_llm1_pre_repair_state": pre_repair_state,
                    "final_llm1_abstract_strategy": item.get("final_llm1_abstract_strategy"),
                    "final_llm1_concrete_strategy": item.get("final_llm1_concrete_strategy"),
                    "final_llm1_post_repair_state": item.get("final_llm1_post_repair_state")
                })
            
            # 如果没有有效向量，则跳过
            if not vectors:
                print(f"CWE 类型 {cwe_type} 没有有效向量，跳过")
                continue
            
            # 将向量转换为 numpy 数组
            vectors = np.array(vectors).astype('float32')
            
            # 创建 FAISS 索引
            dimension = vectors.shape[1]
            index = faiss.IndexFlatL2(dimension)
            index.add(vectors)
            
            # 保存索引和元数据
            index_path = os.path.join(self.output_dir, f"{cwe_type}.index")
            metadata_path = os.path.join(self.output_dir, f"{cwe_type}_metadata.json")
            
            faiss.write_index(index, index_path)
            with open(metadata_path, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, ensure_ascii=False, indent=2)
            
            print(f"已为 CWE 类型 {cwe_type} 创建索引，包含 {len(vectors)} 个向量")
    
    def build(self, strategy_analysis_output_paths):
        """
        构建所有向量索引
        
        参数:
        strategy_analysis_output_paths (str or list): 策略分析输出文件路径或路径列表
        """
        print(f"开始加载数据...")
        data = self.load_strategy_analysis_data(strategy_analysis_output_paths)
        
        print(f"总共加载了 {len(data)} 条数据")
        grouped_data = self.process_data(data)
        
        print(f"找到以下顶层父 CWE 类型: {list(grouped_data.keys())}")
        self.build_indices(grouped_data)
        print(f"所有索引已保存到 {self.output_dir}")


if __name__ == "__main__":
    # 支持多个数据集
    dataset_names = ["ICVul", "manual_example"]  # 可以添加更多数据集名称
    
    # 为所有数据集创建统一的输出目录
    combined_dataset_name = "_".join(dataset_names)
    output_dir = f"/mnt/projects/unnamed/datasets/{combined_dataset_name}/vectorbase/indices"
    
    # 索引构建器
    builder = VectorDatabaseBuilder(output_dir=output_dir, embedding_model="bge-code-v1")
    
    # 构建所有数据集的输入文件路径列表
    strategy_analysis_output_paths = []
    for dataset_name in dataset_names:
        path = f"/mnt/projects/unnamed/datasets/{dataset_name}/outputs/strategy_analysis_verbose/strategy_analysis_output.json"
        strategy_analysis_output_paths.append(path)
    
    print(f"将处理以下数据集: {dataset_names}")
    print(f"对应的文件路径:")
    for i, path in enumerate(strategy_analysis_output_paths):
        print(f"  {dataset_names[i]}: {path}")
    
    # 构建索引
    builder.build(strategy_analysis_output_paths)
