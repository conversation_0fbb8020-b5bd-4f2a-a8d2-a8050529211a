import faiss
import numpy as np
import os

def load_faiss_index(index_path: str):
    """
    加载 FAISS 索引。

    参数:
    index_path (str): FAISS 索引文件的路径。

    返回:
    faiss.Index: 加载的 FAISS 索引。
    """
    if not os.path.exists(index_path):
        raise FileNotFoundError(f"索引文件未找到: {index_path}")
    try:
        index = faiss.read_index(index_path)
        return index
    except Exception as e:
        print(f"加载 FAISS 索引时出错: {e}")
        raise

def search_faiss_index(index: faiss.Index, query_vector: np.ndarray, k: int = 5):
    """
    在 FAISS 索引中搜索相似向量。

    参数:
    index (faiss.Index): 加载的 FAISS 索引。
    query_vector (np.ndarray): 用于查询的向量 (形状应为 (1, D)，其中 D 是向量维度)。
    k (int): 要检索的最相似向量的数量。

    返回:
    tuple: 包含距离和索引的元组 (distances, indices)。
           distances: 形状为 (1, k) 的数组，包含到 k 个最近邻的距离。
           indices: 形状为 (1, k) 的数组，包含 k 个最近邻的索引。
    """
    if query_vector.ndim == 1:
        query_vector = np.expand_dims(query_vector, axis=0)
    
    if query_vector.shape[1] != index.d:
        raise ValueError(f"查询向量的维度 ({query_vector.shape[1]}) 与索引的维度 ({index.d}) 不匹配。")

    try:
        distances, indices = index.search(query_vector.astype('float32'), k)
        return distances, indices
    except Exception as e:
        print(f"在 FAISS 索引中搜索时出错: {e}")
        raise

if __name__ == '__main__':
    # 假设索引文件和可能的嵌入文件与此脚本位于同一目录或指定路径
    INDEX_FILE_PATH = "faiss_index.bin" 
    # 假设嵌入向量的维度，例如 768。这应该与构建索引时使用的维度一致。
    DIMENSION = 768 

    print(f"正在尝试从 '{INDEX_FILE_PATH}' 加载 FAISS 索引...")
    
    # 检查索引文件是否存在，如果不存在则创建一个虚拟索引用于测试
    if not os.path.exists(INDEX_FILE_PATH):
        print(f"警告: 索引文件 '{INDEX_FILE_PATH}' 未找到。")
        print("将创建一个虚拟索引进行演示。请确保您已使用 build_index.py 生成了实际的索引。")
        # 创建一个虚拟索引
        dummy_index = faiss.IndexFlatL2(DIMENSION)
        dummy_data = np.random.rand(100, DIMENSION).astype('float32') # 100 个 D 维向量
        dummy_index.add(dummy_data)
        faiss.write_index(dummy_index, INDEX_FILE_PATH)
        print(f"已创建并保存虚拟索引到 '{INDEX_FILE_PATH}'。")

    try:
        faiss_index = load_faiss_index(INDEX_FILE_PATH)
        print(f"FAISS 索引已成功加载。索引中的向量总数: {faiss_index.ntotal}, 维度: {faiss_index.d}")

        # 创建一个示例查询向量 (确保维度与索引匹配)
        # 在实际应用中，这个向量将来自您的数据或用户输入
        if faiss_index.d > 0: # 仅当维度有效时才创建查询向量
            example_query_vector = np.random.rand(1, faiss_index.d).astype('float32')
            print(f"\n使用示例查询向量进行搜索 (取前 5 个相似结果):")
            print(f"查询向量 (前10个元素): {example_query_vector[0, :10]}...")

            k_neighbors = 5
            distances, indices = search_faiss_index(faiss_index, example_query_vector, k=k_neighbors)

            print(f"\n查询结果:")
            print(f"  最相似的 {k_neighbors} 个向量的索引: {indices}")
            print(f"  对应的距离: {distances}")

            if faiss_index.ntotal == 0:
                print("\n注意: 索引当前为空。搜索结果可能没有意义。")
            elif k_neighbors > faiss_index.ntotal:
                print(f"\n注意: 请求的邻居数量 ({k_neighbors}) 大于索引中的向量总数 ({faiss_index.ntotal})。")

        else:
            print("\n错误: 索引维度为 0，无法执行查询。请检查索引文件。")

    except FileNotFoundError as fnf_error:
        print(f"错误: {fnf_error}")
        print("请确保您已经运行了 build_index.py 来创建 FAISS 索引文件 (默认为 'faiss_index.bin')，")
        print("或者将 INDEX_FILE_PATH 更新为正确的路径。")
    except ValueError as val_error:
        print(f"值错误: {val_error}")
    except Exception as e:
        print(f"发生意外错误: {e}")

    # 清理：如果创建了虚拟索引，可以选择删除它
    # if os.path.exists(INDEX_FILE_PATH) and "dummy_index" in locals():
    #     os.remove(INDEX_FILE_PATH)
    #     print(f"\n已删除虚拟索引文件 '{INDEX_FILE_PATH}'。")