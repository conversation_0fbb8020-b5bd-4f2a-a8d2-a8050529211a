import os
import json
import faiss
import numpy as np
from models.embedding_helper import CodeEmbedder # 新增导入


class VectorDatabaseQuerier:
    def __init__(self, indices_dir="vectorbase/indices"):
        """
        初始化向量数据库查询器
        
        参数:
        indices_dir (str): 存储向量索引的目录
        """
        self.indices_dir = indices_dir
        self.embedder = CodeEmbedder(model_type="bge-code-v1") # 新增 Gemini Embedder
        self.indices = {}
        self.metadata = {}
        
        # 加载所有可用的索引
        self.load_all_indices()
    
    def load_all_indices(self):
        """加载所有可用的索引和元数据"""
        if not os.path.exists(self.indices_dir):
            print(f"索引目录 {self.indices_dir} 不存在，请先构建索引。")
            return
        
        # 查找所有 .index 文件
        index_files = [f for f in os.listdir(self.indices_dir) if f.endswith('.index')]
        
        if not index_files:
            print(f"索引目录 {self.indices_dir} 中没有找到索引文件。")
            return
        
        for index_file in index_files:
            cwe_type = index_file.replace('.index', '')
            metadata_file = f"{cwe_type}_metadata.json"
            
            index_path = os.path.join(self.indices_dir, index_file)
            metadata_path = os.path.join(self.indices_dir, metadata_file)
            
            # 加载索引
            try:
                self.indices[cwe_type] = faiss.read_index(index_path)
                # print(f"已加载索引: {cwe_type}，包含 {self.indices[cwe_type].ntotal} 个向量")
            except Exception as e:
                print(f"加载索引 {index_path} 时出错: {e}")
                continue
            
            # 加载元数据
            try:
                if os.path.exists(metadata_path):
                    with open(metadata_path, 'r', encoding='utf-8') as f:
                        self.metadata[cwe_type] = json.load(f)
                else:
                    print(f"元数据文件 {metadata_path} 不存在")
                    self.metadata[cwe_type] = []
            except Exception as e:
                print(f"加载元数据 {metadata_path} 时出错: {e}")
                self.metadata[cwe_type] = []
    
    def vectorize_text(self, text):
        """
        将文本向量化
        
        参数:
        text (str): 要向量化的文本
        
        返回:
        numpy.ndarray: 文本的向量表示
        """
        # return self.model.encode(text) # 旧的向量化方式
        embeddings_array = self.embedder.get_embeddings([text]) # 使用 CodeEmbedder
        if embeddings_array is not None and embeddings_array.shape[0] > 0:
            return embeddings_array[0] # get_embeddings 返回一个二维数组，取第一个（也是唯一一个）嵌入
        else:
            # 处理向量化失败的情况，例如返回一个零向量或抛出异常
            # 维度需要与 Gemini Embedder 的输出维度一致 (3072)
            print(f"警告: 文本 '{text[:50]}...' 向量化失败或返回空。返回零向量。")
            return np.zeros(self.embedder.dim if hasattr(self.embedder, 'dim') else 3072)
    
    def search(self, query_text, top_k=5, cwe_type=None):
        """
        搜索与查询文本最相似的项
        
        参数:
        query_text (str): 查询文本
        top_k (int): 返回的最匹配结果数量
        cwe_type (str, optional): 指定的 CWE 类型，如果为 None，则搜索所有类型
        
        返回:
        list: 包含最匹配结果及其元数据的列表
        """
        if not self.indices:
            print("没有加载索引，无法执行搜索。")
            return []
        
        # 向量化查询文本
        query_vector = self.vectorize_text(query_text).reshape(1, -1).astype('float32')
        
        results = []
        
        # 要搜索的 CWE 类型
        cwe_types_to_search = [cwe_type] if cwe_type and cwe_type in self.indices else self.indices.keys()
        
        for cwe in cwe_types_to_search:
            if cwe not in self.indices or cwe not in self.metadata:
                continue
            
            index = self.indices[cwe]
            metadata_list = self.metadata[cwe]
            
            # 搜索
            if top_k > index.ntotal:
                k = index.ntotal
            else:
                k = top_k
            
            if k == 0:
                continue
            
            distances, indices = index.search(query_vector, k)
            
            # 组合结果
            for i, idx in enumerate(indices[0]):
                if idx < len(metadata_list):
                    result = {
                        "distance": float(distances[0][i]),
                        "cwe_type": cwe,
                        "metadata": metadata_list[idx]
                    }
                    results.append(result)
        
        # 按距离排序
        results.sort(key=lambda x: x["distance"])
        
        return results[:top_k]


if __name__ == "__main__":
    # 测试查询
    querier = VectorDatabaseQuerier()
    
    # 示例查询
    query_text = "空指针解引用导致的漏洞，需要添加空指针检查来修复"
    results = querier.search(query_text, top_k=3)
    
    print(f"查询: '{query_text}'")
    print("搜索结果:")
    
    for i, result in enumerate(results):
        print(f"\n结果 #{i+1} (距离: {result['distance']:.4f}, CWE 类型: {result['metadata']['cwe_type']}, 顶层父类: {result['cwe_type']})")
        print(f"CVE: {result['metadata']['cve_id']}")
        print(f"漏洞状态: {result['metadata']['final_llm1_pre_repair_state'][:150]}...")
        print(f"修复策略: {result['metadata']['final_llm1_abstract_strategy'][:150]}...") 