import faiss
import numpy as np
import os

def build_and_save_faiss_index(data, index_path="vectorbase/faiss.index"):
    """
    构建 FAISS 索引并将其保存到磁盘。

    参数:
    data (numpy.ndarray): 用于构建索引的向量数据，形状为 (n_vectors, dimension)。
    index_path (str): 保存索引的文件路径。
    """
    if data is None or data.shape[0] == 0:
        print("错误：输入数据为空，无法构建索引。")
        return

    dimension = data.shape[1]
    
    # 创建一个简单的 L2 距离索引
    # 对于更大数据集或更高性能需求，可以考虑更复杂的索引类型，
    # 例如 IndexIVFFlat, IndexHNSWFlat 等。
    index = faiss.IndexFlatL2(dimension)
    
    print(f"正在使用 {data.shape[0]} 个维度为 {dimension} 的向量构建索引...")
    
    # 将向量添加到索引中
    index.add(data)
    
    print(f"索引构建完成，包含 {index.ntotal} 个向量。")
    
    # 创建保存索引的目录（如果不存在）
    index_dir = os.path.dirname(index_path)
    if index_dir and not os.path.exists(index_dir):
        os.makedirs(index_dir)
        print(f"已创建目录：{index_dir}")

    # 保存索引到磁盘
    faiss.write_index(index, index_path)
    print(f"FAISS 索引已成功保存到: {index_path}")

def generate_sample_data(num_vectors=1000, dimension=128):
    """
    生成一些随机的示例向量数据。

    参数:
    num_vectors (int): 要生成的向量数量。
    dimension (int): 每个向量的维度。

    返回:
    numpy.ndarray: 生成的随机向量数据。
    """
    print(f"正在生成 {num_vectors} 个维度为 {dimension} 的示例向量...")
    # 生成随机数据，确保数据类型为 float32，FAISS 通常需要这种类型
    data = np.random.rand(num_vectors, dimension).astype('float32')
    return data

if __name__ == "__main__":
    # 示例用法
    
    # 1. 生成或加载你的向量数据
    # 在实际应用中，您会从您的数据源加载向量
    # 例如：从文本嵌入、图像特征等。
    sample_vectors = generate_sample_data(num_vectors=5000, dimension=768)
    
    # 2. 定义索引保存的路径
    # 确保 vectorbase 目录存在，或者代码会尝试创建它
    output_index_path = "vectorbase/my_faiss_index.index"
    
    # 3. 构建并保存索引
    build_and_save_faiss_index(sample_vectors, output_index_path)

    # 验证索引是否可以加载（可选步骤）
    if os.path.exists(output_index_path):
        try:
            print(f"\n正在尝试加载已保存的索引从 {output_index_path}...")
            loaded_index = faiss.read_index(output_index_path)
            print(f"索引加载成功，包含 {loaded_index.ntotal} 个向量。")
            
            # 示例查询（可选）
            # k = 5 # 查找最近的5个邻居
            # D, I = loaded_index.search(sample_vectors[:1], k) # 查询第一个向量的邻居
            # print(f"查询第一个向量的 {k} 个最近邻居:")
            # print("距离:", D)
            # print("索引:", I)
        except Exception as e:
            print(f"加载或查询索引时出错: {e}")
    else:
        print(f"错误：索引文件 {output_index_path} 未找到。")