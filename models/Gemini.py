# Licensed under the MIT license.

import os
import threading
import time
from tqdm import tqdm
import concurrent.futures
from google import genai
import google.generativeai as genai_google # For explicit client configuration

thread_lock = threading.Lock()

# 全局 API Key 和 client 将被移除，改为动态创建
# gemini_api_key = "AIzaSyCIvTvgk8ixSDtsQLPBffq-pzEevo7m5-0" # 请使用环境变量
# client = genai.Client(api_key=gemini_api_key)

max_threads = 64


def load_Gemini_model(model_name: str): # Parameter changed to model_name for clarity
    # This function seems to be a placeholder.
    # For actual model loading/selection, it would need more logic based on model_name.
    return None, model_name


def get_gemini_api_key(model_config: dict) -> str:
    """Helper to get Gemini API key from model_config or environment variable."""
    api_key = model_config.get("api_key")
    if api_key:
        return api_key
    
    api_key_from_env = os.getenv("GEMINI_API_KEY")
    if api_key_from_env:
        return api_key_from_env
    
    raise ValueError(
        "Gemini API key not found in model_config (key: 'api_key') "
        "or environment variable ('GEMINI_API_KEY'). Please ensure it is set."
    )

def generate_with_Gemini_model(
    prompt: str,
    model_config: dict, # 更改：接受模型配置字典
    n: int = 1,
    max_tokens: int = 8192, # Adjusted to a more common default, can be overridden by model_config
    temperature: float = 0.5, # Adjusted, can be overridden
    top_p: float = 0.95, # Can be overridden
    # top_k is also a valid parameter for Gemini's GenerationConfig
    top_k: Optional[int] = None
):
    model_name = model_config.get("model_name")
    if not model_name:
        raise ValueError("model_config must contain 'model_name' for Gemini.")

    api_key = get_gemini_api_key(model_config)
    
    # Configure the client dynamically
    genai_google.configure(api_key=api_key)

    # Generation Config
    generation_config_params = {
        "candidate_count": n, # n controls how many responses are generated
        "max_output_tokens": model_config.get("max_tokens", max_tokens),
        "temperature": model_config.get("temperature", temperature),
        "top_p": model_config.get("top_p", top_p),
    }
    if model_config.get("top_k", top_k) is not None: # Add top_k if provided
        generation_config_params["top_k"] = model_config.get("top_k", top_k)
    
    # Stop sequences can also be added to generation_config if needed:
    # "stop_sequences": model_config.get("stop_sequences", [])

    generation_config = genai_google.types.GenerationConfig(**generation_config_params)
    
    # Safety settings (optional, can be configured via model_config)
    safety_settings = model_config.get("safety_settings", None) # Example: {"HARM_CATEGORY_HARASSMENT": "BLOCK_NONE"}

    # Initialize the model
    # Note: Gemini API typically uses 'gemini-pro' or specific versions like 'gemini-1.5-flash-latest'
    # The model_name from model_config should be the actual model identifier.
    # The old 'client.models.generate_content' is a bit different from the newer SDK's 'GenerativeModel'.
    # Using GenerativeModel for more explicit control.
    try:
        model_instance = genai_google.GenerativeModel(
            model_name=model_name,
            generation_config=generation_config,
            safety_settings=safety_settings
        )
    except Exception as e:
        print(f"Error initializing Gemini model '{model_name}': {e}")
        return []


    ans_texts, timeout_val = [], 5 # timeout_val for retries
    max_retries = 5
    current_retry = 0

    while not ans_texts and current_retry < max_retries:
        try:
            # time.sleep(timeout_val) # Consider if sleep is needed before first attempt
            response = model_instance.generate_content(contents=prompt)
            
            # Extract text from candidates
            if response.candidates:
                ans_texts = [candidate.content.parts[0].text for candidate in response.candidates if candidate.content and candidate.content.parts]
            else: # Handle cases where no candidates are returned or prompt feedback exists
                if response.prompt_feedback and response.prompt_feedback.block_reason:
                    print(f"Gemini content generation blocked for model '{model_name}'. Reason: {response.prompt_feedback.block_reason_message or response.prompt_feedback.block_reason}")
                    return [] # Blocked, no retries needed for this specific error
                else:
                    print(f"Gemini response for model '{model_name}' had no candidates. Full response: {response}")
            
            if ans_texts and ans_texts[0]: # Success
                 break

        except Exception as e:
            current_retry += 1
            print(f"Gemini API call error for model '{model_name}': {e}. Retry {current_retry}/{max_retries}.")
            if not ans_texts or not ans_texts[0]:
                if current_retry >= max_retries:
                    print(f"Max retries reached for Gemini model {model_name}. Returning empty response.")
                    return []
                
                time.sleep(timeout_val)
                timeout_val = min(timeout_val * 2, 60) # Exponential backoff, max 60s
                
                try:
                    print(f"Retrying Gemini ({current_retry}/{max_retries}) for model {model_name}. Will retry in {timeout_val} seconds...")
                except:
                    pass
            else: # Successful response
                break
        
    if not ans_texts:
        print(f"Failed to get a response from Gemini model {model_name} after {max_retries} retries.")
        return []
        
    return ans_texts


def generate_n_with_Gemini_model( # This function needs to be updated to use model_config
    prompt: str,
    model_config: dict, # CHANGED
    n: int = 1,
    max_tokens: int = 8192,
    temperature: float = 0.5,
    top_k: Optional[int] = None,
    top_p: float = 0.95,
    # stop sequences are handled by generation_config inside generate_with_Gemini_model
    # stop: Optional[List[str]] = None,
    # max_threads and disable_tqdm are not used here
):
    # Pass model_config directly
    preds = generate_with_Gemini_model(
        prompt, model_config, n, max_tokens, temperature, top_p, top_k
    )
    return preds


def generate_prompts_with_Gemini_model( # This function needs to be updated to use model_config
    prompts: list,
    model_config: dict, # CHANGED
    n: int = 1,
    max_tokens: int = 8192,
    temperature: float = 0.5,
    top_k: Optional[int] = None,
    top_p: float = 0.95,
    # stop: Optional[List[str]] = None,
    max_threads: int = 10, # For ProcessPoolExecutor
    disable_tqdm: bool = True,
):
    preds = []
    with concurrent.futures.ProcessPoolExecutor(max_workers=max_threads) as executor:
        futures = [
            # Pass model_config to each submitted task
            executor.submit(
                generate_with_Gemini_model, prompt, model_config, n, max_tokens, temperature, top_p, top_k
            )
            for prompt in prompts
        ]
        for i, future in tqdm( # tqdm import might be missing if not used elsewhere
            enumerate(concurrent.futures.as_completed(futures)),
            total=len(futures),
            desc="running evaluate",
            disable=disable_tqdm,
        ):
            ans = future.result()
            preds.append(ans)
    return preds
