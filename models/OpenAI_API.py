# Licensed under the MIT license.

import os
import os
import threading
import time
from tqdm import tqdm
import concurrent.futures
from openai import AzureOpenAI, OpenAI

thread_lock = threading.Lock()

# 全局客户端将被移除，改为在函数内动态创建
# client = OpenAI(
#   base_url="https://openrouter.ai/api/v1",
#   api_key="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", # 请使用环境变量管理API密钥
# )


# openai_api_key = "2c504220-0507-499d-9369-a840379e8e27"
# client = OpenAI(
#     api_key=openai_api_key,
#     base_url="https://ark.cn-beijing.volces.com/api/v3"
# )


max_threads = 10


def load_OpenAI_model(model):
    # This function seems to be a placeholder, returning the model name.
    # For actual model loading with different providers, it would need more logic.
    return None, model


def get_api_key_for_provider(provider_name: str, model_config: dict) -> str:
    """Helper to get API key from model_config or environment variable."""
    api_key = model_config.get("api_key")
    if api_key:
        return api_key
    
    env_var_name = f"{provider_name.upper()}_API_KEY"
    api_key_from_env = os.getenv(env_var_name)
    if api_key_from_env:
        return api_key_from_env
    
    # Fallback for generic OPENAI_API_KEY if provider is 'openai' and OPENAI_API_KEY is set
    if provider_name.lower() == "openai" and os.getenv("OPENAI_API_KEY"):
        return os.getenv("OPENAI_API_KEY")
    # Fallback for OPENROUTER_API_KEY (as it was previously hardcoded)
    if provider_name.lower() == "openrouter" and os.getenv("OPENROUTER_API_KEY"):
        return os.getenv("OPENROUTER_API_KEY")

    raise ValueError(
        f"API key for provider '{provider_name}' not found in model_config (key: 'api_key') "
        f"or environment variable ('{env_var_name}'). "
        f"Please ensure it is set."
    )

def generate_with_OpenAI_model(
    prompt,
    model_config: dict, # 更改：接受模型配置字典
    n=1,
    max_tokens=512,
    temperature=0.5,
    top_p=0.95,
    # top_k is not a standard OpenAI ChatCompletion parameter, so it's ignored in client call.
    # top_k=None,
    stop=None,
):
    provider = model_config.get("provider", "openrouter").lower() # 默认为 openrouter
    model_name = model_config.get("model_name")
    
    if not model_name:
        raise ValueError("model_config must contain 'model_name'")

    api_key = get_api_key_for_provider(provider, model_config)

    client_instance = None
    
    if provider == "openrouter":
        base_url = model_config.get("base_url", "https://openrouter.ai/api/v1")
        client_instance = OpenAI(base_url=base_url, api_key=api_key)
    elif provider == "openai":
        base_url = model_config.get("base_url") # Defaults to official OpenAI if None
        client_instance = OpenAI(api_key=api_key, base_url=base_url if base_url else None)
    elif provider == "azure":
        azure_endpoint = model_config.get("azure_endpoint") or os.getenv("AZURE_OPENAI_ENDPOINT")
        api_version = model_config.get("azure_api_version") or os.getenv("AZURE_OPENAI_API_VERSION")
        if not azure_endpoint or not api_version:
            raise ValueError("For Azure provider, 'azure_endpoint' and 'azure_api_version' must be in model_config or env vars.")
        client_instance = AzureOpenAI(
            api_key=api_key,
            api_version=api_version,
            azure_endpoint=azure_endpoint
        )
        # For Azure, model_name is the deployment ID.
    elif provider == "volcengine": # Based on previous commented code
        base_url = model_config.get("base_url", "https://ark.cn-beijing.volces.com/api/v3")
        client_instance = OpenAI(api_key=api_key, base_url=base_url)
    else:
        raise ValueError(f"Unsupported provider: {provider}. Supported providers: openrouter, openai, azure, volcengine.")

    messages = [{"role": "user", "content": prompt}]
    parameters = {
        "model": model_name, # 使用 model_config 中的 model_name
        "temperature": temperature,
        "max_tokens": max_tokens,
        "top_p": top_p,
        "n": n,
    }
    
    if stop is not None:
        parameters["stop"] = stop

    ans, timeout = "", 5 # Initial timeout for retries
    max_retries = 5
    current_retry = 0
    
    while not ans and current_retry < max_retries:
        try:
            # time.sleep(timeout) # Consider if sleep is needed before first attempt or only on retry
            completion = client_instance.chat.completions.create(messages=messages, **parameters)
            ans = [choice.message.content for choice in completion.choices]
            if ans and ans[0]: # Success
                break
        except Exception as e:
            current_retry += 1
            print(f"API call error for provider '{provider}', model '{model_name}': {e}. Retry {current_retry}/{max_retries}.")
            if not ans or not ans[0]: # If ans is still empty after potential error
                if current_retry >= max_retries:
                    print(f"Max retries reached for model {model_name}. Returning empty response.")
                    return [] # Or raise an error
                
                # Exponential backoff for retries
                time.sleep(timeout) # Wait before retrying
                timeout = min(timeout * 2, 60) # Double timeout, max 60 seconds
                
                try:
                    print(f"Retrying ({current_retry}/{max_retries})... Current response: {ans}")
                    print(f"Message length: {len(messages[0]['content'])}")
                    print(f"Will retry in {timeout} seconds...")
                except:
                    pass # Avoid print errors if ans is unusual
            else: # Successful response
                break
        
    if not ans: # If loop finished without success
        print(f"Failed to get a response from {model_name} after {max_retries} retries.")
        return []
        
    return ans


def generate_n_with_OpenAI_model( # This function needs to be updated to use model_config
    prompt,
    model_config: dict, # CHANGED
    n=1,
    max_tokens=512,
    temperature=0.8,
    # top_k=40, # As noted, top_k is not used for chat completions
    top_p=0.95,
    stop=["\n"],
    # max_threads=10, # This parameter is not used here
    # disable_tqdm=True, # This parameter is not used here
):
    # Pass model_config directly
    preds = generate_with_OpenAI_model(prompt, model_config, n, max_tokens, temperature, top_p, stop)
    return preds

def generate_prompts_with_OpenAI_model( # This function needs to be updated to use model_config
    prompts: list,
    model_config: dict, # CHANGED
    n=1,
    max_tokens=512,
    temperature=0.8,
    # top_k=40, # As noted
    top_p=0.95,
    stop=["\n"],
    max_threads=10, # This is for ProcessPoolExecutor
    disable_tqdm=True,
):
    preds = []
    # Note: ProcessPoolExecutor might have issues with non-picklable client objects if client creation was complex
    # and not self-contained in generate_with_OpenAI_model. Current change should be fine.
    with concurrent.futures.ProcessPoolExecutor(max_workers=max_threads) as executor:
        futures = [
            # Pass model_config to each submitted task
            executor.submit(generate_with_OpenAI_model, prompt, model_config, n, max_tokens, temperature, top_p, stop)
            for prompt in prompts
        ]
        for i, future in tqdm( # tqdm import might be missing if not used elsewhere
            enumerate(concurrent.futures.as_completed(futures)),
            total=len(futures),
            desc="running evaluate",
            disable=disable_tqdm,
        ):
            ans = future.result()
            preds.append(ans)
    return preds
