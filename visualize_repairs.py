import json
import difflib
import argparse
import os
import sys
import csv
from collections import defaultdict
from typing import List, Dict, Any, Optional
import matplotlib.pyplot as plt
import numpy as np
from utils.cwe_find import CWEProcessor # 新增导入
csv.field_size_limit(10000000)

# 主要CWE类型列表 (参考index_builder.py)
MAIN_CWE_TYPES = [
    "CWE-189", "CWE-254", "CWE-264", "CWE-284", "CWE-310",
    "CWE-399", "CWE-664", "CWE-682", "CWE-691", "CWE-703", "CWE-707"
]
CWE_OTHER_CATEGORY = "CWE-Other" # 用于不在主要列表中的CWE

# Ensure matplotlib can handle negative signs correctly
try:
    plt.rcParams['axes.unicode_minus'] = False
except Exception as e:
    print(f"Warning: Failed to set axes.unicode_minus: {e}")

# 初始化CWE处理器
cwe_processor = CWEProcessor()

def get_main_cwe_type(cwe_id: str) -> str:
    """
    获取给定CWE ID的主要父类型。
    如果父类型不在MAIN_CWE_TYPES中，则返回CWE_OTHER_CATEGORY。
    """
    if not cwe_id or not cwe_id.startswith("CWE-"):
        return CWE_OTHER_CATEGORY
    
    try:
        # 假设 cwe_processor.process_cwe 返回 (原始CWE, 顶层父CWE)
        _, top_parent = cwe_processor.process_cwe(cwe_id)
        if top_parent in MAIN_CWE_TYPES:
            return top_parent
        return CWE_OTHER_CATEGORY
    except Exception as e:
        # print(f"处理CWE ID {cwe_id} 时出错: {e}") # 可选的调试信息
        return CWE_OTHER_CATEGORY

def display_evaluation_results(evaluations: List[Dict[str, Any]], eval_stats: Dict[str, int]) -> None:
    """
    显示LLM评估结果和统计信息。
    
    Args:
        evaluations: 评估结果列表
        eval_stats: 评估统计信息
    """
    if not evaluations:
        print("📝 无LLM评估结果")
        return
    
    print("📝 LLM自动评估结果:")
    print("="*50)
    
    # 显示统计摘要
    print(f"📊 评估统计摘要:")
    print(f"  - 总建议数: {eval_stats.get('total_suggestions', 0)}")
    print(f"  - 语法等价 (SynPatchEq): {eval_stats.get('syntactic_patch_equivalent', 0)}")
    print(f"  - 语义等价 (SemEq): {eval_stats.get('semantic_equivalent', 0)}")
    print(f"  - 合理性 (Plausible): {eval_stats.get('plausible', 0)}")
    print(f"  - 不正确 (Incorrect): {eval_stats.get('incorrect', 0)}")
    print(f"  - 未知 (Unknown): {eval_stats.get('unknown', 0)}")
    print()
    
    # 显示每个建议的详细评估
    for eval_item in evaluations:
        suggestion_idx = eval_item.get("suggestion_index", "?")
        eval_result = eval_item.get("evaluation_result", "Unknown")
        explanation = eval_item.get("explanation", "无解释")
        eval_details = eval_item.get("evaluation_details", {})
        
        # 根据评估结果选择不同的图标和颜色
        if eval_result == "SynPatchEq":
            icon = "🎯"
            color_start = "\033[96m"  # 青色
        elif eval_result == "SemEq":
            icon = "✅"
            color_start = "\033[92m"  # 绿色
        elif eval_result == "Plausible":
            icon = "⚡"
            color_start = "\033[93m"  # 黄色
        elif eval_result == "Incorrect":
            icon = "❌"
            color_start = "\033[91m"  # 红色
        else:
            icon = "❓"
            color_start = "\033[94m"  # 蓝色
        color_end = "\033[0m"
        
        print(f"{icon} {color_start}建议 {suggestion_idx + 1}: {eval_result}{color_end}")
        print(f"   💭 评估解释: {explanation[:100]}{'...' if len(explanation) > 100 else ''}")
        
        # 显示详细评估指标
        if eval_details:
            security = eval_details.get("security_effectiveness", "N/A")
            functional = eval_details.get("functional_correctness", "N/A")
            quality = eval_details.get("implementation_quality", "N/A")
            confidence = eval_details.get("confidence_level", "N/A")
            
            print(f"   🔒 安全有效性: {security}")
            print(f"   ⚙️ 功能正确性: {functional}")
            print(f"   🏗️ 实现质量: {quality}")
            print(f"   🎯 置信度: {confidence}")
        
        print()

def colorize_evaluation_summary(eval_stats: Dict[str, int]) -> str:
    """
    为评估统计信息添加颜色和图标。
    
    Args:
        eval_stats: 评估统计信息
        
    Returns:
        带颜色的统计摘要字符串
    """
    total = eval_stats.get('total_suggestions', 0)
    if total == 0:
        return "📝 无评估数据"
    
    synpatcheq = eval_stats.get('syntactic_patch_equivalent', 0)
    semeq = eval_stats.get('semantic_equivalent', 0)
    plausible = eval_stats.get('plausible', 0)
    incorrect = eval_stats.get('incorrect', 0)
    unknown = eval_stats.get('unknown', 0)
    
    # 计算成功率（SynPatchEq + SemEq + Plausible）
    success_rate = ((synpatcheq + semeq + plausible) / total * 100) if total > 0 else 0
    
    # 根据成功率选择颜色
    if success_rate >= 70:
        color = "\033[92m"  # 绿色
        emoji = "🎉"
    elif success_rate >= 50:
        color = "\033[93m"  # 黄色  
        emoji = "⚡"
    else:
        color = "\033[91m"  # 红色
        emoji = "⚠️"
    
    return f"{emoji} {color}评估成功率: {success_rate:.1f}% ({synpatcheq + semeq + plausible}/{total})\033[0m"

def load_jsonl_results(result_file_path: str) -> List[Dict[str, Any]]:
    """
    加载JSON Lines格式的结果文件。
    
    Args:
        result_file_path: JSON Lines结果文件的路径
        
    Returns:
        包含所有结果条目的列表
    """
    results = []
    with open(result_file_path, 'r', encoding='utf-8') as f:
        for line in f:
            line = line.strip()
            if line:  # 跳过空行
                try:
                    results.append(json.loads(line))
                except json.JSONDecodeError as e:
                    print(f"警告: 解析JSON行时出错: {e}, 行内容: {line[:100]}...")
    return results

def generate_diff(original: str, modified: str, from_label: str = "原始代码", to_label: str = "修复代码") -> str:
    """
    生成两段代码之间的unified diff。
    
    Args:
        original: 原始代码
        modified: 修复后的代码
        from_label: 原始代码的标签
        to_label: 修复后代码的标签
        
    Returns:
        格式化的diff字符串
    """
    if not original or not modified:
        return "无法生成diff: 原始代码或修复代码为空"
        
    diff_lines = list(difflib.unified_diff(
        original.splitlines(keepends=True),
        modified.splitlines(keepends=True),
        fromfile=from_label,
        tofile=to_label
    ))
    
    return ''.join(diff_lines) if diff_lines else "无差异"

def colorize_diff(diff_text: str) -> str:
    """
    为diff添加颜色，使其在终端中更易阅读。
    
    Args:
        diff_text: 原始diff文本
        
    Returns:
        带有颜色的diff文本
    """
    lines = diff_text.splitlines(True)
    colored_lines = []
    
    for line in lines:
        if line.startswith('+'):
            # 绿色文本 (添加的内容)
            colored_lines.append(f"\033[92m{line}\033[0m")
        elif line.startswith('-'):
            # 红色文本 (删除的内容)
            colored_lines.append(f"\033[91m{line}\033[0m")
        elif line.startswith('@'):
            # 青色文本 (位置标记)
            colored_lines.append(f"\033[96m{line}\033[0m")
        else:
            colored_lines.append(line)
    
    return ''.join(colored_lines)

def load_original_csv_data(csv_file_path: str) -> Dict[int, Dict[str, Any]]:
    """
    加载原始CSV文件数据，按行号建立索引。
    
    Args:
        csv_file_path: 原始CSV文件路径
        
    Returns:
        以行号为键的字典，包含每行的所有字段信息
    """
    csv_data = {}
    try:
        with open(csv_file_path, 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row_idx, row in enumerate(reader):
                # CSV行号从2开始（第1行是标题）
                csv_row_number = row_idx + 2
                
                # 提取项目名称（从repo_url中提取）
                repo_url = row.get('repo_url', '')
                project_name = 'Unknown'
                if repo_url:
                    # 从URL中提取项目名，例如：https://github.com/user/project -> project
                    if 'github.com' in repo_url:
                        parts = repo_url.rstrip('/').split('/')
                        if len(parts) >= 2:
                            project_name = parts[-1]  # 取最后一部分作为项目名
                    else:
                        # 对于其他类型的URL，尝试提取最后一部分
                        project_name = repo_url.split('/')[-1].split('.')[0]
                
                # 安全转换num_method_changed为整数
                num_method_changed = 0
                try:
                    num_method_changed_str = row.get('num_method_changed', '0')
                    if num_method_changed_str and num_method_changed_str.strip():
                        num_method_changed = int(num_method_changed_str.strip())
                except (ValueError, TypeError):
                    num_method_changed = 0
                
                csv_data[csv_row_number] = {
                    'cwe_id': row.get('cwe_id', 'Unknown'),
                    'cve_id': row.get('cve_id', 'Unknown'), 
                    'repo_url': repo_url,
                    'project_name': project_name,
                    'num_method_changed': num_method_changed,
                    'is_interprocedural': row.get('is_interprocedural', 'Unknown'),
                    'cve_year': row.get('cve_year', 'Unknown'),
                    'hash': row.get('hash', 'Unknown')
                }
    except FileNotFoundError:
        print(f"警告: 找不到原始CSV文件: {csv_file_path}")
    except Exception as e:
        print(f"警告: 加载原始CSV文件时出错: {e}")
    
    return csv_data

def analyze_num_method_changed_distribution(
    results: List[Dict[str, Any]], 
    csv_data: Dict[int, Dict[str, Any]]
) -> None:
    """
    分析num_method_changed的分布及对应的修复能力。
    
    Args:
        results: 修复结果列表
        csv_data: 原始CSV数据字典
    """
    print("\n" + "="*80)
    print("📊 num_method_changed 分布统计与修复能力分析")
    print("="*80)
    
    # 统计num_method_changed的分布
    method_changed_stats = defaultdict(lambda: {
        'total_vulnerabilities': 0,
        'processed_vulnerabilities': 0,  # 有修复建议的
        'successfully_fixed': 0,         # 成功修复的
        'not_fixed': 0,                  # 未修复的
        'no_evaluation': 0,              # 无评估的
        'total_suggestions': 0,          # 总修复建议数
        'successful_suggestions': 0      # 成功的修复建议数
    })
    
    # 遍历所有结果，进行统计
    for entry in results:
        csv_row = entry.get("csv_row")
        if csv_row not in csv_data:
            continue  # 跳过无法在原始CSV中找到的条目
            
        original_data = csv_data[csv_row]
        num_method_changed = original_data['num_method_changed']
        
        # 更新总漏洞数
        method_changed_stats[num_method_changed]['total_vulnerabilities'] += 1
        
        # 检查是否有修复建议
        status = entry.get("status", "")
        if status == "success":
            method_changed_stats[num_method_changed]['processed_vulnerabilities'] += 1
            
            # 检查修复效果
            eval_stats = entry.get("evaluation_statistics", {})
            if eval_stats:
                synpatcheq_count = eval_stats.get("syntactic_patch_equivalent", 0)
                semeq_count = eval_stats.get("semantic_equivalent", 0)
                plausible_count = eval_stats.get("plausible", 0)
                total_suggestions = eval_stats.get("total_suggestions", 0)
                
                method_changed_stats[num_method_changed]['total_suggestions'] += total_suggestions
                method_changed_stats[num_method_changed]['successful_suggestions'] += (synpatcheq_count + semeq_count + plausible_count)
                
                # 判断该漏洞是否被成功修复（至少有一个成功的建议）
                if synpatcheq_count > 0 or semeq_count > 0 or plausible_count > 0:
                    method_changed_stats[num_method_changed]['successfully_fixed'] += 1
                else:
                    method_changed_stats[num_method_changed]['not_fixed'] += 1
            else:
                method_changed_stats[num_method_changed]['no_evaluation'] += 1
    
    # 显示分布统计
    print(f"🔍 num_method_changed 分布统计:")
    print(f"{'方法变更数':^12} {'总漏洞数':^10} {'处理数':^8} {'修复数':^8} {'修复率':^10} {'建议数':^8} {'建议成功率':^12}")
    print("-" * 80)
    
    # 按num_method_changed排序显示
    sorted_stats = sorted(method_changed_stats.items())
    
    total_all_vulns = 0
    total_all_processed = 0
    total_all_fixed = 0
    total_all_suggestions = 0
    total_all_successful_suggestions = 0
    
    for num_methods, stats in sorted_stats:
        total_vulns = stats['total_vulnerabilities']
        processed_vulns = stats['processed_vulnerabilities']
        fixed_vulns = stats['successfully_fixed']
        total_suggestions = stats['total_suggestions']
        successful_suggestions = stats['successful_suggestions']
        
        # 计算修复率（基于处理过的漏洞）
        fix_rate = (fixed_vulns / processed_vulns * 100) if processed_vulns > 0 else 0
        
        # 计算建议成功率
        suggestion_success_rate = (successful_suggestions / total_suggestions * 100) if total_suggestions > 0 else 0
        
        # 根据修复率选择颜色
        if processed_vulns > 0 and fix_rate >= 70:
            color_start = "\033[92m"  # 绿色
        elif processed_vulns > 0 and fix_rate >= 50:
            color_start = "\033[93m"  # 黄色
        elif processed_vulns > 0:
            color_start = "\033[91m"  # 红色
        else:
            color_start = "\033[94m"  # 蓝色
        color_end = "\033[0m"
        
        print(f"{color_start}{num_methods:^12}{color_end} {total_vulns:^10} {processed_vulns:^8} {fixed_vulns:^8} {fix_rate:^9.1f}% {total_suggestions:^8} {suggestion_success_rate:^11.1f}%")
        
        # 累计统计
        total_all_vulns += total_vulns
        total_all_processed += processed_vulns
        total_all_fixed += fixed_vulns
        total_all_suggestions += total_suggestions
        total_all_successful_suggestions += successful_suggestions
    
    print("-" * 80)
    overall_fix_rate = (total_all_fixed / total_all_processed * 100) if total_all_processed > 0 else 0
    overall_suggestion_success_rate = (total_all_successful_suggestions / total_all_suggestions * 100) if total_all_suggestions > 0 else 0
    print(f"{'总计':^12} {total_all_vulns:^10} {total_all_processed:^8} {total_all_fixed:^8} {overall_fix_rate:^9.1f}% {total_all_suggestions:^8} {overall_suggestion_success_rate:^11.1f}%")
    
    # 分析趋势
    print(f"\n📈 num_method_changed 趋势分析:")
    
    if len(sorted_stats) >= 3:
        # 分析不同方法变更数量的修复难度趋势
        single_method_stats = method_changed_stats.get(1, {})
        multi_method_stats = {}
        
        # 计算多方法修改的平均修复率
        multi_method_total_processed = 0
        multi_method_total_fixed = 0
        
        for num_methods, stats in sorted_stats:
            if num_methods > 1:
                multi_method_total_processed += stats['processed_vulnerabilities']
                multi_method_total_fixed += stats['successfully_fixed']
        
        single_fix_rate = (single_method_stats.get('successfully_fixed', 0) / 
                          single_method_stats.get('processed_vulnerabilities', 1) * 100) if single_method_stats.get('processed_vulnerabilities', 0) > 0 else 0
        
        multi_fix_rate = (multi_method_total_fixed / multi_method_total_processed * 100) if multi_method_total_processed > 0 else 0
        
        print(f"  - 🔧 单方法修改修复率: {single_fix_rate:.1f}%")
        print(f"  - 🔧 多方法修改修复率: {multi_fix_rate:.1f}%")
        
        if single_fix_rate > multi_fix_rate + 10:
            print(f"  - 💡 观察: 单方法修改的漏洞更容易修复")
        elif multi_fix_rate > single_fix_rate + 10:
            print(f"  - 💡 观察: 多方法修改的漏洞修复效果更好")
        else:
            print(f"  - 💡 观察: 方法变更数量对修复效果影响不明显")
        
        # 找出修复率最高和最低的方法变更数量
        method_fix_rates = [(num, (stats['successfully_fixed'] / stats['processed_vulnerabilities'] * 100) if stats['processed_vulnerabilities'] > 0 else 0) 
                           for num, stats in sorted_stats if stats['processed_vulnerabilities'] >= 3]  # 只考虑样本数足够的
        
        if method_fix_rates:
            best_method_count = max(method_fix_rates, key=lambda x: x[1])
            worst_method_count = min(method_fix_rates, key=lambda x: x[1])
            
            print(f"  - 🎯 修复效果最好的方法变更数: {best_method_count[0]} ({best_method_count[1]:.1f}%)")
            print(f"  - ⚠️ 修复效果最差的方法变更数: {worst_method_count[0]} ({worst_method_count[1]:.1f}%)")

def analyze_project_distribution(
    results: List[Dict[str, Any]], 
    csv_data: Dict[int, Dict[str, Any]]
) -> None:
    """
    分析项目分布及对应的修复能力。
    
    Args:
        results: 修复结果列表
        csv_data: 原始CSV数据字典
    """
    print("\n" + "="*80)
    print("🏗️ 项目分布统计与修复能力分析")
    print("="*80)
    
    # 统计项目分布
    project_stats = defaultdict(lambda: {
        'total_vulnerabilities': 0,
        'processed_vulnerabilities': 0,  # 有修复建议的
        'successfully_fixed': 0,         # 成功修复的
        'not_fixed': 0,                  # 未修复的
        'no_evaluation': 0,              # 无评估的
        'total_suggestions': 0,          # 总修复建议数
        'successful_suggestions': 0,     # 成功的修复建议数
        'cwe_types': set()               # 该项目涉及的CWE类型
    })
    
    # 遍历所有结果，进行统计
    for entry in results:
        csv_row = entry.get("csv_row")
        if csv_row not in csv_data:
            continue
            
        original_data = csv_data[csv_row]
        project_name = original_data['project_name']
        cwe_id = entry.get("cwe_id", "Unknown")
        
        # 更新总漏洞数和CWE类型
        project_stats[project_name]['total_vulnerabilities'] += 1
        project_stats[project_name]['cwe_types'].add(cwe_id)
        
        # 检查是否有修复建议
        status = entry.get("status", "")
        if status == "success":
            project_stats[project_name]['processed_vulnerabilities'] += 1
            
            # 检查修复效果
            eval_stats = entry.get("evaluation_statistics", {})
            if eval_stats:
                synpatcheq_count = eval_stats.get("syntactic_patch_equivalent", 0)
                semeq_count = eval_stats.get("semantic_equivalent", 0)
                plausible_count = eval_stats.get("plausible", 0)
                total_suggestions = eval_stats.get("total_suggestions", 0)
                
                project_stats[project_name]['total_suggestions'] += total_suggestions
                project_stats[project_name]['successful_suggestions'] += (synpatcheq_count + semeq_count + plausible_count)
                
                # 判断该漏洞是否被成功修复
                if synpatcheq_count > 0 or semeq_count > 0 or plausible_count > 0:
                    project_stats[project_name]['successfully_fixed'] += 1
                else:
                    project_stats[project_name]['not_fixed'] += 1
            else:
                project_stats[project_name]['no_evaluation'] += 1
    
    # 按漏洞数量排序（降序）
    sorted_projects = sorted(project_stats.items(), 
                           key=lambda x: x[1]['total_vulnerabilities'], 
                           reverse=True)
    
    print(f"🔍 项目分布统计:")
    print(f"{'项目名称':<25} {'总漏洞':^8} {'处理':^6} {'修复':^6} {'修复率':^9} {'建议数':^8} {'建议成功率':^12} {'CWE类型数':^10}")
    print("-" * 95)
    
    total_projects = len(sorted_projects)
    total_all_vulns = 0
    total_all_processed = 0
    total_all_fixed = 0
    total_all_suggestions = 0
    total_all_successful_suggestions = 0
    all_cwe_types = set()
    
    # 只显示前20个项目
    display_projects = sorted_projects
    
    for project_name, stats in display_projects:
        total_vulns = stats['total_vulnerabilities']
        processed_vulns = stats['processed_vulnerabilities']
        fixed_vulns = stats['successfully_fixed']
        total_suggestions = stats['total_suggestions']
        successful_suggestions = stats['successful_suggestions']
        cwe_count = len(stats['cwe_types'])
        
        # 计算修复率
        fix_rate = (fixed_vulns / processed_vulns * 100) if processed_vulns > 0 else 0
        
        # 计算建议成功率
        suggestion_success_rate = (successful_suggestions / total_suggestions * 100) if total_suggestions > 0 else 0
        
        # 截断过长的项目名称
        display_name = project_name[:24] if len(project_name) > 24 else project_name
        
        # 根据修复率选择颜色
        if processed_vulns > 0 and fix_rate >= 70:
            color_start = "\033[92m"  # 绿色
        elif processed_vulns > 0 and fix_rate >= 50:
            color_start = "\033[93m"  # 黄色
        elif processed_vulns > 0:
            color_start = "\033[91m"  # 红色
        else:
            color_start = "\033[94m"  # 蓝色
        color_end = "\033[0m"
        
        print(f"{color_start}{display_name:<25}{color_end} {total_vulns:^8} {processed_vulns:^6} {fixed_vulns:^6} {fix_rate:^8.1f}% {total_suggestions:^8} {suggestion_success_rate:^11.1f}% {cwe_count:^10}")
    
    # 计算总体统计（包括所有项目，不只是显示的前20个）
    for project_name, stats in sorted_projects:
        total_all_vulns += stats['total_vulnerabilities']
        total_all_processed += stats['processed_vulnerabilities']
        total_all_fixed += stats['successfully_fixed']
        total_all_suggestions += stats['total_suggestions']
        total_all_successful_suggestions += stats['successful_suggestions']
        all_cwe_types.update(stats['cwe_types'])
    
    print("-" * 95)
    overall_fix_rate = (total_all_fixed / total_all_processed * 100) if total_all_processed > 0 else 0
    overall_suggestion_success_rate = (total_all_successful_suggestions / total_all_suggestions * 100) if total_all_suggestions > 0 else 0
    print(f"{'总计':<25} {total_all_vulns:^8} {total_all_processed:^6} {total_all_fixed:^6} {overall_fix_rate:^8.1f}% {total_all_suggestions:^8} {overall_suggestion_success_rate:^11.1f}% {len(all_cwe_types):^10}")
    
    # 项目分析
    print(f"\n📊 项目分析:")
    print(f"  - 📁 总项目数: {total_projects}")
    print(f"  - 🔍 涉及CWE类型数: {len(all_cwe_types)}")
    
    if len(sorted_projects) >= 3:
        # 找出漏洞最多的项目
        top_vuln_project = sorted_projects[0]
        print(f"  - 🔥 漏洞最多的项目: {top_vuln_project[0]} ({top_vuln_project[1]['total_vulnerabilities']} 个)")
        
        # 找出修复率最高的项目（至少有5个漏洞的）
        high_vuln_projects = [(name, stats) for name, stats in sorted_projects 
                             if stats['processed_vulnerabilities'] >= 5]
        
        if high_vuln_projects:
            best_fix_project = max(high_vuln_projects, 
                                 key=lambda x: x[1]['successfully_fixed'] / x[1]['processed_vulnerabilities'] if x[1]['processed_vulnerabilities'] > 0 else 0)
            
            best_fix_rate = (best_fix_project[1]['successfully_fixed'] / best_fix_project[1]['processed_vulnerabilities'] * 100) if best_fix_project[1]['processed_vulnerabilities'] > 0 else 0
            
            print(f"  - 🎯 修复率最高的项目 (≥5个漏洞): {best_fix_project[0]} ({best_fix_rate:.1f}%)")
        
        # 分析项目规模与修复率的关系
        large_projects = [(name, stats) for name, stats in sorted_projects if stats['total_vulnerabilities'] >= 10]
        small_projects = [(name, stats) for name, stats in sorted_projects if stats['total_vulnerabilities'] < 10 and stats['processed_vulnerabilities'] > 0]
        
        if large_projects and small_projects:
            large_fix_rate = sum(stats['successfully_fixed'] for _, stats in large_projects) / sum(stats['processed_vulnerabilities'] for _, stats in large_projects) * 100
            small_fix_rate = sum(stats['successfully_fixed'] for _, stats in small_projects) / sum(stats['processed_vulnerabilities'] for _, stats in small_projects) * 100
            
            print(f"  - 🏢 大型项目 (≥10个漏洞) 修复率: {large_fix_rate:.1f}%")
            print(f"  - 🏘️ 小型项目 (<10个漏洞) 修复率: {small_fix_rate:.1f}%")
            
            if abs(large_fix_rate - small_fix_rate) > 10:
                if large_fix_rate > small_fix_rate:
                    print(f"  - 💡 观察: 大型项目的修复效果更好")
                else:
                    print(f"  - 💡 观察: 小型项目的修复效果更好")
            else:
                print(f"  - 💡 观察: 项目规模对修复效果影响不明显")

def visualize_repairs(
    results_file: str,
    max_entries: Optional[int] = None,
    csv_file_path: Optional[str] = None,
    export_csv_rows_to: Optional[str] = None,
    filter_by_csv_rows_file: Optional[str] = None
) -> None:
    """
    在终端中可视化修复代码与ground truth的比较，以unified diff格式展示。
    按照csv_row的顺序排序。
    
    Args:
        results_file: 修复结果的JSON Lines文件路径
        max_entries: 最大处理条目数，None表示处理所有条目
        csv_file_path: 原始CSV文件路径，用于扩展分析
        export_csv_rows_to: 文件路径，用于导出所有唯一的CSV行号并退出
        filter_by_csv_rows_file: 文件路径，用于读取CSV行号并仅分析这些行
    """
    # 检查互斥参数
    if export_csv_rows_to and filter_by_csv_rows_file:
        print("错误: --export_csv_rows_to 和 --filter_by_csv_rows_file 参数是互斥的。请只使用其中一个。")
        sys.exit(1)

    results = load_jsonl_results(results_file)
    if not results:
        print(f"警告: 从 {results_file} 未加载到任何结果记录。")
        # 如果是导出模式，也应该处理空结果的情况
        if export_csv_rows_to:
            try:
                with open(export_csv_rows_to, 'w', encoding='utf-8') as f:
                    pass # 创建一个空文件
                print(f"结果文件为空，已创建空的行号文件: {export_csv_rows_to}")
            except IOError as e:
                print(f"错误: 无法写入行号文件 {export_csv_rows_to}: {e}")
            sys.exit(0) # 正常退出，因为文件是空的
        return # 对于其他模式，直接返回

    print(f"从 {results_file} 加载了 {len(results)} 条结果记录")

    # 处理 --filter_by_csv_rows_file 参数
    # 这个过滤应该在排序和max_entries应用之前，以确保我们只加载和排序需要的数据
    if filter_by_csv_rows_file:
        if not os.path.exists(filter_by_csv_rows_file):
            print(f"错误: 用于过滤的CSV行号文件不存在: {filter_by_csv_rows_file}")
            sys.exit(1)
        
        if os.path.getsize(filter_by_csv_rows_file) == 0:
            print(f"错误: 用于过滤的CSV行号文件为空: {filter_by_csv_rows_file}")
            sys.exit(1)
            
        filter_rows = set()
        try:
            with open(filter_by_csv_rows_file, 'r', encoding='utf-8') as f:
                for line_num, line in enumerate(f, 1):
                    line = line.strip()
                    if line: # 跳过空行
                        try:
                            filter_rows.add(int(line))
                        except ValueError:
                            print(f"错误: 在行号文件 {filter_by_csv_rows_file} 的第 {line_num} 行发现无效行号 '{line}'。请确保每行只有一个整数。")
                            sys.exit(1)
            
            if not filter_rows: # 如果读取后集合仍为空（例如，文件只包含空行）
                print(f"错误: 未从行号文件 {filter_by_csv_rows_file} 中读取到有效的CSV行号。")
                sys.exit(1)

            print(f"将根据文件 {filter_by_csv_rows_file} 中的 {len(filter_rows)} 个行号进行过滤。")
            
            original_results_count = len(results)
            results = [entry for entry in results if entry.get("csv_row") is not None and int(entry.get("csv_row")) in filter_rows]
            
            if not results:
                print(f"警告: 根据提供的行号过滤后，没有剩余的结果可供分析。")
                # 考虑是否要在此处退出，或者允许后续统计显示为0
                # sys.exit(0)
            else:
                print(f"过滤后剩余 {len(results)}/{original_results_count} 条结果记录。")

        except IOError as e:
            print(f"错误: 无法读取CSV行号文件 {filter_by_csv_rows_file}: {e}")
            sys.exit(1)
        except Exception as e: # 捕获其他潜在错误，例如在处理过程中
            print(f"处理CSV行号文件 {filter_by_csv_rows_file} 时发生意外错误: {e}")
            sys.exit(1)

    # 按照csv_row的值对结果进行排序
    # 首先确保所有条目都有csv_row，如果没有则使用索引作为备用
    for idx, entry in enumerate(results):
        if "csv_row" not in entry:
            entry["csv_row"] = idx + 2  # 假设第一行是标题，所以从2开始
    
    # 根据csv_row排序
    results.sort(key=lambda x: x.get("csv_row", float('inf')))
    print("已按照CSV行号对结果进行排序")
    
    if max_entries is not None and max_entries > 0:
        results = results[:max_entries]
        print(f"将处理前 {max_entries} 条记录")
    
    # 统计LLM评估结果（按修复建议统计）
    total_entries_with_evaluation = 0
    global_eval_stats = {
        "total_suggestions": 0,
        "syntactic_patch_equivalent": 0,
        "semantic_equivalent": 0,
        "plausible": 0,
        "incorrect": 0,
        "unknown": 0
    }
    
    # 新增：漏洞级别的修复率统计
    vulnerability_stats = {
        "total_vulnerabilities": 0,
        "successfully_fixed": 0,
        "not_fixed": 0,
        "no_evaluation": 0
    }
    
    # 新增：CWE类型统计
    cwe_stats = {}  # CWE类型 -> 统计信息的字典
    main_cwe_stats = defaultdict(lambda: { # 新增：主要CWE类型统计
        "total": 0,
        "processed": 0,
        "fixed": 0,
        "not_fixed": 0,
        "no_evaluation": 0,
        "suggestions": 0,
        "synpatcheq": 0,
        "semeq": 0,
        "plausible": 0,
        "incorrect": 0,
        "unknown": 0,
        "original_cwes": set() # 记录该主要类型下包含的原始CWE ID
    })
    
    # 新增：跳过数据原因统计
    skip_stats = {
        "error_missing_fields": 0,        # 错误：缺少必要字段
        "skipped_no_line_numbers": 0,     # 跳过：无法找到漏洞行号
        "error_diff_analysis": 0,         # 错误：代码差异分析失败
        "no_suggestion_found": 0,         # 无修复建议找到
        "error_repair_generation": 0,     # 错误：修复生成过程异常
        "missing_original_code": 0,       # 缺少原始代码
        "missing_fixed_code": 0,          # 缺少Ground Truth代码
        "total_skipped": 0,               # 总跳过数
        "total_processed": 0              # 总处理数
    }
    
    show_details = True  # 控制是否显示详细内容的标志位
    processed_csv_rows_for_export = set() # 用于收集实际处理的行号
    
    for idx, entry in enumerate(results):
        # csv_row_original = entry.get("csv_row") # 保留原始行号用于可能的导出
        # csv_row_display = csv_row_original if csv_row_original is not None else f"未知行_{idx}" # 用于显示的行号
        # 使用 entry.get("csv_row") 直接获取，如果为None，后续逻辑会处理
        csv_row_value = entry.get("csv_row")
        csv_row_display_key = csv_row_value if csv_row_value is not None else f"未知行_{idx}"


        status = entry.get("status", "未知状态")
        original_code = entry.get("original_code")
        fixed_code = entry.get("fixed_code")  # ground truth
        cwe_id = entry.get("cwe_id", "未知CWE")
        
        # 初始化CWE统计信息（如果尚未存在）
        if cwe_id not in cwe_stats:
            cwe_stats[cwe_id] = {
                "total": 0,
                "processed": 0,  # 有修复建议且有评估的
                "fixed": 0,      # 被成功修复的
                "not_fixed": 0,  # 未被修复的
                "no_evaluation": 0,  # 无评估的
                "suggestions": 0,  # 总修复建议数
                "synpatcheq": 0,  # 语法等价建议数
                "semeq": 0,      # 语义等价建议数
                "plausible": 0,  # 合理建议数
                "incorrect": 0,  # 不正确建议数
                "unknown": 0     # 未知建议数
            }
        
        # 更新CWE总数
        cwe_stats[cwe_id]["total"] += 1
        
        # 获取主要CWE类型并更新其统计
        main_cwe = get_main_cwe_type(cwe_id)
        main_cwe_stats[main_cwe]["total"] += 1
        if cwe_id != "未知CWE":
            main_cwe_stats[main_cwe]["original_cwes"].add(cwe_id)
        
        # 获取LLM评估相关数据
        llm_evaluations = entry.get("llm_evaluations", [])
        eval_stats = entry.get("evaluation_statistics", {})
        ground_truth_patch = entry.get("ground_truth_patch", "")
        
        # 更新全局统计（按修复建议统计）
        if eval_stats:
            total_entries_with_evaluation += 1
            for key in global_eval_stats:
                global_eval_stats[key] += eval_stats.get(key, 0)
        
        # 检查跳过原因并统计
        skip_reason = None
        
        # 首先检查是否缺少原始代码或Ground Truth（这些在inference.py中会导致error状态）
        if not original_code:
            skip_reason = "missing_original_code"
            skip_stats["missing_original_code"] += 1
        elif not fixed_code:
            skip_reason = "missing_fixed_code"
            skip_stats["missing_fixed_code"] += 1
        # 然后检查具体的非成功状态
        elif status == "error":
            # 根据错误消息进一步分类，如果有的话
            error_msg = entry.get("error_message", "").lower()
            if "missing" in error_msg and "required fields" in error_msg:
                skip_reason = "error_missing_fields"
                skip_stats["error_missing_fields"] += 1
            else:
                skip_reason = "error_repair_generation"
                skip_stats["error_repair_generation"] += 1
        elif status == "skipped_no_line_numbers":
            skip_reason = "skipped_no_line_numbers"
            skip_stats["skipped_no_line_numbers"] += 1
        elif status == "error_diff_analysis":
            skip_reason = "error_diff_analysis"  
            skip_stats["error_diff_analysis"] += 1
        elif status == "no_suggestion_found":
            skip_reason = "no_suggestion_found"
            skip_stats["no_suggestion_found"] += 1
        elif status != "success":
            # 对于其他未知的非成功状态，归类为repair generation错误
            skip_reason = "error_repair_generation"
            skip_stats["error_repair_generation"] += 1
        
        if skip_reason:
            skip_stats["total_skipped"] += 1
            if show_details:
                status_desc = status if status != "未知状态" else "unknown"
                print(f"跳过条目 {csv_row_display_key} (CWE: {cwe_id}): 状态={status_desc}, 原因={skip_reason}")
            continue
        
        repairs = entry.get("repairs", [])
        if not repairs:
            # 这种情况应该在inference.py中被标记为no_suggestion_found，但以防万一
            skip_stats["no_suggestion_found"] += 1
            skip_stats["total_skipped"] += 1
            if show_details:
                print(f"跳过条目 {csv_row_display_key} (CWE: {cwe_id}): 无修复建议")
            continue
        
        # 记录为已处理 (移到此处，确保只有未跳过的条目被计数和考虑导出)
        skip_stats["total_processed"] += 1
        
        # 如果条目未被跳过，并且我们处于导出模式，则记录其原始行号
        if export_csv_rows_to and csv_row_value is not None:
            try:
                processed_csv_rows_for_export.add(int(csv_row_value))
            except ValueError:
                print(f"警告: 在条目 {idx} 中发现无效的CSV行号 '{csv_row_value}'，将跳过导出此行。")

        # 确保repairs是列表
        if not isinstance(repairs, list):
            repairs = [repairs]
        
        # 更新CWE统计 - 有修复建议的条目
        cwe_stats[cwe_id]["processed"] += 1
        main_cwe_stats[main_cwe]["processed"] += 1 # 更新主要CWE统计
        
        # 新增：判断当前漏洞是否被成功修复
        vulnerability_stats["total_vulnerabilities"] += 1
        vulnerability_fixed = False
        
        if eval_stats:
            # 更新CWE的修复建议统计
            total_sugg = eval_stats.get("total_suggestions", 0)
            syn_eq_sugg = eval_stats.get("syntactic_patch_equivalent", 0)
            sem_eq_sugg = eval_stats.get("semantic_equivalent", 0)
            plau_sugg = eval_stats.get("plausible", 0)
            incorr_sugg = eval_stats.get("incorrect", 0)
            unk_sugg = eval_stats.get("unknown", 0)

            cwe_stats[cwe_id]["suggestions"] += total_sugg
            cwe_stats[cwe_id]["synpatcheq"] += syn_eq_sugg
            cwe_stats[cwe_id]["semeq"] += sem_eq_sugg
            cwe_stats[cwe_id]["plausible"] += plau_sugg
            cwe_stats[cwe_id]["incorrect"] += incorr_sugg
            cwe_stats[cwe_id]["unknown"] += unk_sugg

            main_cwe_stats[main_cwe]["suggestions"] += total_sugg
            main_cwe_stats[main_cwe]["synpatcheq"] += syn_eq_sugg
            main_cwe_stats[main_cwe]["semeq"] += sem_eq_sugg
            main_cwe_stats[main_cwe]["plausible"] += plau_sugg
            main_cwe_stats[main_cwe]["incorrect"] += incorr_sugg
            main_cwe_stats[main_cwe]["unknown"] += unk_sugg
            
            # 如果有任何一个修复建议被评为SynPatchEq、SemEq或Plausible，则认为漏洞被修复
            synpatcheq_count = eval_stats.get("syntactic_patch_equivalent", 0)
            semeq_count = eval_stats.get("semantic_equivalent", 0)
            plausible_count = eval_stats.get("plausible", 0)
            if synpatcheq_count > 0 or semeq_count > 0 or plausible_count > 0:
                vulnerability_fixed = True
                vulnerability_stats["successfully_fixed"] += 1
                cwe_stats[cwe_id]["fixed"] += 1
                main_cwe_stats[main_cwe]["fixed"] += 1 # 更新主要CWE统计
            else:
                vulnerability_stats["not_fixed"] += 1
                cwe_stats[cwe_id]["not_fixed"] += 1
                main_cwe_stats[main_cwe]["not_fixed"] += 1 # 更新主要CWE统计
        else:
            vulnerability_stats["no_evaluation"] += 1
            cwe_stats[cwe_id]["no_evaluation"] += 1
            main_cwe_stats[main_cwe]["no_evaluation"] += 1 # 更新主要CWE统计
        
        if show_details:
            print("\n" + "="*80)
            print(f"## 条目: CSV行 {csv_row_display_key}, CWE: {cwe_id}")
            if eval_stats:
                eval_summary = colorize_evaluation_summary(eval_stats)
                print(f"   {eval_summary}")
                # 新增：显示漏洞修复状态
                if vulnerability_fixed:
                    vuln_status = "🎯 \033[92m漏洞已修复\033[0m"
                else:
                    vuln_status = "⚠️ \033[91m漏洞未修复\033[0m"
                print(f"   {vuln_status}")
            print("="*80 + "\n")
            
            # 显示LLM评估结果（如果存在）
            if llm_evaluations:
                display_evaluation_results(llm_evaluations, eval_stats)
                print()
            
            # 原始代码与Ground Truth的比较
            print("### 原始代码与Ground Truth的比较\n")
            diff_gt = generate_diff(original_code, fixed_code, "原始代码", "Ground_Truth")
            print(colorize_diff(diff_gt))
            print("\n")
            
            # 遍历所有修复建议
            for i, repair in enumerate(repairs):
                # 处理新的patch格式
                suggestion_patch = repair.get("suggestion_patch", "")
                suggestion_code = repair.get("suggestion_code", "")
                
                # 如果有patch但没有代码，需要应用patch生成代码用于显示
                if suggestion_patch and not suggestion_code:
                    # 这里我们只是显示patch，不需要真正应用
                    # 但为了向后兼容，我们可以尝试从元数据中获取
                    pass
                
                # 检查是否是旧版本的字符串格式
                if not suggestion_patch and not suggestion_code:
                    if isinstance(repair, str):
                        suggestion_code = repair
                    else:
                        continue
                
                print("-"*80)
                
                # 从评估结果中查找对应的评估信息
                eval_info = None
                if llm_evaluations:
                    eval_info = next((e for e in llm_evaluations if e.get("suggestion_index") == i), None)
                
                # 修复建议标题，包含评估结果
                if eval_info:
                    eval_result = eval_info.get("evaluation_result", "Unknown")
                    if eval_result == "SynPatchEq":
                        eval_icon = "🎯"
                        eval_color = "\033[96m"
                    elif eval_result == "SemEq":
                        eval_icon = "✅"
                        eval_color = "\033[92m"
                    elif eval_result == "Plausible":
                        eval_icon = "⚡"
                        eval_color = "\033[93m"
                    elif eval_result == "Incorrect":
                        eval_icon = "❌"
                        eval_color = "\033[91m"
                    else:
                        eval_icon = "❓"
                        eval_color = "\033[94m"
                    print(f"### 修复建议 {i+1} {eval_icon} {eval_color}{eval_result}\033[0m\n")
                else:
                    print(f"### 修复建议 {i+1}\n")
                
                # 修复策略和关键变量信息
                if isinstance(repair, dict):
                    repair_strategy = repair.get("repair_strategy", "未提供修复策略")
                    key_variables = repair.get("key_variables", [])
                    key_vars_str = ", ".join(key_variables) if key_variables else "未提供关键变量"
                    llm_score = repair.get("llm_score", "未评分")
                    print(f"- 修复策略: {repair_strategy}")
                    print(f"- 关键变量: {key_vars_str}")
                    print(f"- LLM评分: {llm_score}")
                    
                    # 显示评估详情（如果存在）
                    if eval_info:
                        explanation = eval_info.get("explanation", "")
                        if explanation:
                            print(f"- 🤖 LLM评估解释: {explanation}")
                    print()
                
                # 显示生成的patch
                if suggestion_patch:
                    print(f"#### LLM生成的补丁 (Unified Diff格式)\n")
                    print("```diff")
                    print(suggestion_patch)
                    print("```\n")
        
        # 询问是否继续查看下一个条目（仅在显示详细内容模式下）
        if show_details and idx < len(results) - 1:
            user_input = input("\n按Enter键继续查看下一个条目，输入q退出，输入v查看统计: ")
            if user_input.lower() == 'q':
                # 如果用户退出详细查看，并且处于导出模式，则执行导出
                if export_csv_rows_to:
                    if not processed_csv_rows_for_export:
                        print(f"未找到可导出的已处理CSV行号 (用户提前退出)。")
                        try:
                            with open(export_csv_rows_to, 'w', encoding='utf-8') as f:
                                pass # 创建一个空文件
                            print(f"已创建空的行号文件 (无已处理行): {export_csv_rows_to}")
                        except IOError as e:
                            print(f"错误: 无法写入空的行号文件 {export_csv_rows_to}: {e}")
                        sys.exit(0)

                    sorted_export_rows = sorted(list(processed_csv_rows_for_export))
                    try:
                        with open(export_csv_rows_to, 'w', encoding='utf-8') as f:
                            for row_num in sorted_export_rows:
                                f.write(f"{row_num}\n")
                        print(f"已将 {len(sorted_export_rows)} 个实际处理的CSV行号导出到 (用户提前退出): {export_csv_rows_to}")
                    except IOError as e:
                        print(f"错误: 无法写入行号文件 {export_csv_rows_to}: {e}")
                        sys.exit(1)
                    sys.exit(0) # 成功导出后退出
                return  # 直接退出函数
            elif user_input.lower() == 'v':
                print("\n📊 正在收集剩余条目的统计信息...")
                show_details = False  # 切换到只统计模式

    # 在所有条目处理完毕后（或用户提前退出详细查看），如果处于导出模式，则执行导出
    if export_csv_rows_to:
        if not processed_csv_rows_for_export:
            print(f"未找到可导出的已处理CSV行号。")
            try:
                with open(export_csv_rows_to, 'w', encoding='utf-8') as f:
                    pass # 创建一个空文件
                print(f"已创建空的行号文件 (无已处理行): {export_csv_rows_to}")
            except IOError as e:
                print(f"错误: 无法写入空的行号文件 {export_csv_rows_to}: {e}")
            sys.exit(0) # 正常退出

        sorted_export_rows = sorted(list(processed_csv_rows_for_export))
        try:
            with open(export_csv_rows_to, 'w', encoding='utf-8') as f:
                for row_num in sorted_export_rows:
                    f.write(f"{row_num}\n")
            print(f"已将 {len(sorted_export_rows)} 个实际处理的CSV行号导出到: {export_csv_rows_to}")
        except IOError as e:
            print(f"错误: 无法写入行号文件 {export_csv_rows_to}: {e}")
            sys.exit(1)
        sys.exit(0) # 成功导出后退出
    
    # 显示全局统计
    print("\n" + "="*80)
    print("🏆 总体评估统计")
    print("="*80)
    
    # 新增：显示漏洞级别的修复率统计
    print("🎯 漏洞修复率统计:")
    total_vuln = vulnerability_stats["total_vulnerabilities"]
    fixed_vuln = vulnerability_stats["successfully_fixed"]
    not_fixed_vuln = vulnerability_stats["not_fixed"]
    no_eval_vuln = vulnerability_stats["no_evaluation"]
    
    if total_vuln > 0:
        vulnerability_fix_rate = (fixed_vuln / total_vuln) * 100
        print(f"  - 总漏洞数: {total_vuln}")
        print(f"  - 🎯 成功修复: {fixed_vuln} ({vulnerability_fix_rate:.1f}%)")
        print(f"  - ⚠️ 未修复: {not_fixed_vuln}")
        print(f"  - ❓ 无评估: {no_eval_vuln}")
        
        # 根据修复率显示颜色化的总结
        if vulnerability_fix_rate >= 70:
            color = "\033[92m"  # 绿色
            emoji = "🎉"
        elif vulnerability_fix_rate >= 50:
            color = "\033[93m"  # 黄色  
            emoji = "⚡"
        else:
            color = "\033[91m"  # 红色
            emoji = "⚠️"
        
        print(f"\n{emoji} {color}漏洞修复率: {vulnerability_fix_rate:.1f}% ({fixed_vuln}/{total_vuln})\033[0m")
    else:
        print("  无漏洞数据")
    
    print("\n" + "-"*50)
    
    # 新增：显示CWE类型统计
    if cwe_stats:
        print("🔍 CWE类型分布统计:")
        
        # 按CWE-ID排序显示
        sorted_cwe_items = sorted(cwe_stats.items(), key=lambda x: x[0])
        
        print(f"{'CWE类型':<15} {'总数':^6} {'处理':^6} {'修复':^6} {'修复率':^10} {'建议数':^8} {'成功率':^8}")
        print("-" * 70)
        
        total_cwe_types = len(sorted_cwe_items)
        total_all_vulns = sum(stats["total"] for stats in cwe_stats.values())
        total_all_processed = sum(stats["processed"] for stats in cwe_stats.values())
        total_all_fixed = sum(stats["fixed"] for stats in cwe_stats.values())
        total_all_suggestions = sum(stats["suggestions"] for stats in cwe_stats.values())
        total_all_successful = sum(stats["synpatcheq"] + stats["semeq"] + stats["plausible"] for stats in cwe_stats.values())
        
        for cwe_id, stats in sorted_cwe_items:
            total_count = stats["total"]
            processed_count = stats["processed"]
            fixed_count = stats["fixed"]
            suggestions_count = stats["suggestions"]
            successful_suggestions = stats["synpatcheq"] + stats["semeq"] + stats["plausible"]
            
            # 计算修复率（基于处理过的条目）
            if processed_count > 0:
                fix_rate = (fixed_count / processed_count) * 100
                fix_rate_str = f"{fix_rate:.1f}%"
            else:
                fix_rate_str = "N/A"
            
            # 计算建议成功率
            if suggestions_count > 0:
                suggestion_success_rate = (successful_suggestions / suggestions_count) * 100
                suggestion_success_str = f"{suggestion_success_rate:.1f}%"
            else:
                suggestion_success_str = "N/A"
            
            # 根据修复率选择颜色
            if processed_count > 0 and fix_rate >= 70:
                color_start = "\033[92m"  # 绿色
            elif processed_count > 0 and fix_rate >= 50:
                color_start = "\033[93m"  # 黄色
            elif processed_count > 0:
                color_start = "\033[91m"  # 红色
            else:
                color_start = "\033[94m"  # 蓝色
            color_end = "\033[0m"
            
            print(f"{color_start}{cwe_id:<15}{color_end} {total_count:^6} {processed_count:^6} {fixed_count:^6} {fix_rate_str:^10} {suggestions_count:^8} {suggestion_success_str:^8}")
        
        print("-" * 70)
        overall_fix_rate = (total_all_fixed/total_all_processed*100) if total_all_processed > 0 else 0
        overall_suggestion_rate = (total_all_successful/total_all_suggestions*100) if total_all_suggestions > 0 else 0
        print(f"{'总计':<15} {total_all_vulns:^6} {total_all_processed:^6} {total_all_fixed:^6} {overall_fix_rate:^9.1f}% {total_all_suggestions:^8} {overall_suggestion_rate:^7.1f}%")
        
        print(f"\n📈 CWE类型分析:")
        print(f"  - 涉及CWE类型数: {total_cwe_types}")
        print(f"  - 最常见的CWE类型: {max(cwe_stats.items(), key=lambda x: x[1]['total'])[0]} ({max(cwe_stats.values(), key=lambda x: x['total'])['total']} 个)")
        
        # 找出修复率最高和最低的CWE类型
        cwe_with_fixes = {cwe: stats for cwe, stats in cwe_stats.items() if stats["processed"] > 0}
        if cwe_with_fixes:
            best_cwe = max(cwe_with_fixes.items(), key=lambda x: x[1]["fixed"]/x[1]["processed"] if x[1]["processed"] > 0 else 0)
            worst_cwe = min(cwe_with_fixes.items(), key=lambda x: x[1]["fixed"]/x[1]["processed"] if x[1]["processed"] > 0 else 1)
            
            best_rate = (best_cwe[1]["fixed"] / best_cwe[1]["processed"]) * 100 if best_cwe[1]["processed"] > 0 else 0
            worst_rate = (worst_cwe[1]["fixed"] / worst_cwe[1]["processed"]) * 100 if worst_cwe[1]["processed"] > 0 else 0
            
            print(f"  - 🎯 修复率最高: {best_cwe[0]} ({best_rate:.1f}%)")
            print(f"  - ⚠️ 修复率最低: {worst_cwe[0]} ({worst_rate:.1f}%)")
    else:
        print("🔍 无CWE类型统计数据")

    print("\n" + "-"*50)

    # 新增：显示主要CWE类型分布统计
    if main_cwe_stats:
        print("🌲 主要CWE类型分布统计 (基于CWE树结构):")
        # 按主要CWE类型名称排序，但将CWE-Other放在最后
        sorted_main_cwe_items = sorted(
            main_cwe_stats.items(),
            key=lambda x: (x[0] == CWE_OTHER_CATEGORY, x[0]) # True (1) for CWE_OTHER_CATEGORY, False (0) for others
        )
        
        print(f"{'主要CWE类型':<15} {'总数':^6} {'处理':^6} {'修复':^6} {'修复率':^10} {'建议数':^8} {'成功率':^8} {'包含CWE数':^10}")
        print("-" * 85)
        
        total_main_cwe_types = len(sorted_main_cwe_items)
        total_main_all_vulns = sum(stats["total"] for stats in main_cwe_stats.values())
        total_main_all_processed = sum(stats["processed"] for stats in main_cwe_stats.values())
        total_main_all_fixed = sum(stats["fixed"] for stats in main_cwe_stats.values())
        total_main_all_suggestions = sum(stats["suggestions"] for stats in main_cwe_stats.values())
        total_main_all_successful = sum(stats["synpatcheq"] + stats["semeq"] + stats["plausible"] for stats in main_cwe_stats.values())
        
        for main_cwe_id, stats in sorted_main_cwe_items:
            total_count = stats["total"]
            processed_count = stats["processed"]
            fixed_count = stats["fixed"]
            suggestions_count = stats["suggestions"]
            successful_suggestions = stats["synpatcheq"] + stats["semeq"] + stats["plausible"]
            contained_cwes_count = len(stats["original_cwes"])
            
            fix_rate_str = f"{(fixed_count / processed_count * 100):.1f}%" if processed_count > 0 else "N/A"
            suggestion_success_str = f"{(successful_suggestions / suggestions_count * 100):.1f}%" if suggestions_count > 0 else "N/A"
            
            color_start, color_end = "", ""
            if processed_count > 0:
                fix_rate_val = (fixed_count / processed_count * 100)
                if fix_rate_val >= 70: color_start = "\033[92m"  # 绿色
                elif fix_rate_val >= 50: color_start = "\033[93m"  # 黄色
                else: color_start = "\033[91m"  # 红色
                color_end = "\033[0m"
            elif main_cwe_id != CWE_OTHER_CATEGORY : # 对于非Other且未处理的，用蓝色
                 color_start = "\033[94m" # 蓝色
                 color_end = "\033[0m"

            print(f"{color_start}{main_cwe_id:<15}{color_end} {total_count:^6} {processed_count:^6} {fixed_count:^6} {fix_rate_str:^10} {suggestions_count:^8} {suggestion_success_str:^8} {contained_cwes_count:^10}")
            
        print("-" * 85)
        overall_main_fix_rate = (total_main_all_fixed / total_main_all_processed * 100) if total_main_all_processed > 0 else 0
        overall_main_suggestion_rate = (total_main_all_successful / total_main_all_suggestions * 100) if total_main_all_suggestions > 0 else 0
        print(f"{'总计':<15} {total_main_all_vulns:^6} {total_main_all_processed:^6} {total_main_all_fixed:^6} {overall_main_fix_rate:^9.1f}% {total_main_all_suggestions:^8} {overall_main_suggestion_rate:^7.1f}% {'-':^10}")
        
        print(f"\n📈 主要CWE类型分析:")
        print(f"  - 涉及主要CWE类型数 (含'{CWE_OTHER_CATEGORY}'): {total_main_cwe_types}")
        
        # 找出修复率最高和最低的主要CWE类型 (排除 CWE-Other 且有处理的)
        main_cwe_with_fixes = {
            cwe: stats for cwe, stats in main_cwe_stats.items()
            if stats["processed"] > 0 and cwe != CWE_OTHER_CATEGORY
        }
        if main_cwe_with_fixes:
            best_main_cwe = max(main_cwe_with_fixes.items(), key=lambda x: x[1]["fixed"]/x[1]["processed"])
            worst_main_cwe = min(main_cwe_with_fixes.items(), key=lambda x: x[1]["fixed"]/x[1]["processed"])
            
            best_main_rate = (best_main_cwe[1]["fixed"] / best_main_cwe[1]["processed"]) * 100
            worst_main_rate = (worst_main_cwe[1]["fixed"] / worst_main_cwe[1]["processed"]) * 100
            
            print(f"  - 🎯 修复率最高 (主要类型): {best_main_cwe[0]} ({best_main_rate:.1f}%)")
            print(f"  - ⚠️ 修复率最低 (主要类型): {worst_main_cwe[0]} ({worst_main_rate:.1f}%)")
    else:
        print("🌲 无主要CWE类型统计数据")
        
    print("\n" + "-"*50)
    
    # 新增：Top-K修复正确率统计
    print("🎯 Top-K修复正确率统计:")
    
    # 收集所有有评估结果的漏洞数据
    vulnerabilities_with_topk_data = []
    
    for entry in results:
        status = entry.get("status", "")
        if status != "success":
            continue
            
        llm_evaluations = entry.get("llm_evaluations", [])
        if not llm_evaluations:
            continue
            
        # 按建议索引排序评估结果（确保按照原始排序）
        sorted_evaluations = sorted(llm_evaluations, key=lambda x: x.get("suggestion_index", 0))
        
        # 检查每个位置是否有成功的修复（SynPatchEq、SemEq或Plausible）
        # 同时收集每个位置的详细评估结果
        topk_success = []
        topk_eval_results = [] # 新增：收集详细评估结果
        for eval_item in sorted_evaluations:
            eval_result = eval_item.get("evaluation_result", "Unknown") # 保持Unknown
            is_success = eval_result in ["SynPatchEq", "SemEq", "Plausible"]
            topk_success.append(is_success)
            topk_eval_results.append(eval_result)
        
        if topk_success:  # 只有当有评估结果时才加入统计
            vulnerabilities_with_topk_data.append({
                "csv_row": entry.get("csv_row", "unknown"),
                "cwe_id": entry.get("cwe_id", "unknown"),
                "topk_success": topk_success,
                "topk_eval_results": topk_eval_results # 新增
            })
    
    if vulnerabilities_with_topk_data:
        total_vulns_for_topk = len(vulnerabilities_with_topk_data)
        
        # 计算Top-1, Top-3, Top-5正确率
        topk_metrics = {}
        for k in [1, 2, 3, 4, 5]:
            successful_at_topk = 0
            
            for vuln_data in vulnerabilities_with_topk_data:
                topk_success = vuln_data["topk_success"]
                # 检查前k个建议中是否有任何一个成功
                if len(topk_success) >= k:
                    if any(topk_success[:k]):
                        successful_at_topk += 1
                elif len(topk_success) > 0:
                    # 如果建议数少于k，但有建议，检查现有的建议
                    if any(topk_success):
                        successful_at_topk += 1
            
            topk_rate = (successful_at_topk / total_vulns_for_topk) * 100 if total_vulns_for_topk > 0 else 0
            topk_metrics[k] = {
                "successful": successful_at_topk,
                "total": total_vulns_for_topk,
                "rate": topk_rate
            }
        
        print(f"  - 📊 基于 {total_vulns_for_topk} 个有评估结果的漏洞")
        print(f"  - 🥇 Top-1 正确率: {topk_metrics[1]['successful']}/{topk_metrics[1]['total']} ({topk_metrics[1]['rate']:.1f}%)")
        print(f"  - 🥈 Top-2 正确率: {topk_metrics[2]['successful']}/{topk_metrics[2]['total']} ({topk_metrics[2]['rate']:.1f}%)")
        print(f"  - 🥈 Top-3 正确率: {topk_metrics[3]['successful']}/{topk_metrics[3]['total']} ({topk_metrics[3]['rate']:.1f}%)")
        print(f"  - 🥉 Top-4 正确率: {topk_metrics[4]['successful']}/{topk_metrics[4]['total']} ({topk_metrics[4]['rate']:.1f}%)")
        print(f"  - 🥉 Top-5 正确率: {topk_metrics[5]['successful']}/{topk_metrics[5]['total']} ({topk_metrics[5]['rate']:.1f}%)")
        
        # 显示颜色化的Top-1结果（最重要的指标）
        top1_rate = topk_metrics[1]['rate']
        if top1_rate >= 70:
            color = "\033[92m"  # 绿色
            emoji = "🎉"
        elif top1_rate >= 50:
            color = "\033[93m"  # 黄色  
            emoji = "⚡"
        else:
            color = "\033[91m"  # 红色
            emoji = "⚠️"
        
        print(f"\n{emoji} {color}Top-1 修复正确率: {top1_rate:.1f}%\033[0m")
        
        # 显示Top-K趋势分析
        print(f"\n📈 Top-K趋势分析:")
        improvement_1_to_2 = topk_metrics[2]['rate'] - topk_metrics[1]['rate']
        improvement_1_to_3 = topk_metrics[3]['rate'] - topk_metrics[1]['rate']
        improvement_1_to_5 = topk_metrics[5]['rate'] - topk_metrics[1]['rate']
        improvement_3_to_5 = topk_metrics[5]['rate'] - topk_metrics[3]['rate']
        
        print(f"  - Top-1 → Top-2 提升: {improvement_1_to_2:+.1f}%")
        print(f"  - Top-1 → Top-3 提升: {improvement_1_to_3:+.1f}%")
        print(f"  - Top-1 → Top-5 提升: {improvement_1_to_5:+.1f}%")
        print(f"  - Top-3 → Top-5 提升: {improvement_3_to_5:+.1f}%")
        
        if improvement_1_to_3 > 10:
            print(f"  - 💡 建议: 考虑增加候选修复数量，Top-3相比Top-1有显著提升")
        elif improvement_1_to_3 < 5:
            print(f"  - 💡 建议: Top-1质量已较高，增加候选数量收益有限")
            
        # 按CWE类型分析Top-1正确率
        cwe_topk_stats = {}
        for vuln_data in vulnerabilities_with_topk_data:
            cwe_id = vuln_data["cwe_id"]
            topk_success = vuln_data["topk_success"]
            
            if cwe_id not in cwe_topk_stats:
                cwe_topk_stats[cwe_id] = {"total": 0, "top1_success": 0}
            
            cwe_topk_stats[cwe_id]["total"] += 1
            if len(topk_success) > 0 and topk_success[0]:  # Top-1成功
                cwe_topk_stats[cwe_id]["top1_success"] += 1
        
        if len(cwe_topk_stats) > 1:  # 只有当有多个CWE类型时才显示
            print(f"\n🔍 各CWE类型Top-1正确率:")
            sorted_cwe_topk = sorted(cwe_topk_stats.items(), 
                                   key=lambda x: x[1]["top1_success"]/x[1]["total"] if x[1]["total"] > 0 else 0, 
                                   reverse=True)
            
            for cwe_id, stats in sorted_cwe_topk[:5]:  # 显示前5个
                if stats["total"] >= 3:  # 只显示有足够样本的CWE类型
                    cwe_top1_rate = (stats["top1_success"] / stats["total"]) * 100
                    print(f"    - {cwe_id}: {stats['top1_success']}/{stats['total']} ({cwe_top1_rate:.1f}%)")
        
        # 新增：修复建议级别的Top-K成功率统计
        print(f"\n📊 修复建议级别Top-K成功率:")
        
        # 收集所有修复建议的评估结果
        all_suggestions_by_position = {}  # position -> [success_list]
        
        for vuln_data in vulnerabilities_with_topk_data:
            topk_success = vuln_data["topk_success"]
            for pos, is_success in enumerate(topk_success):
                position = pos + 1  # 转换为1-based索引
                if position not in all_suggestions_by_position:
                    all_suggestions_by_position[position] = []
                all_suggestions_by_position[position].append(is_success)
        
        # 计算每个位置的成功率
        position_success_rates = {}
        for position, success_list in all_suggestions_by_position.items():
            if position <= 5:  # 只统计前5个位置
                total_at_position = len(success_list)
                successful_at_position = sum(success_list)
                success_rate = (successful_at_position / total_at_position) * 100 if total_at_position > 0 else 0
                position_success_rates[position] = {
                    "successful": successful_at_position,
                    "total": total_at_position,
                    "rate": success_rate
                }
        
        # 显示各位置的成功率
        print(f"  各位置修复建议成功率:")
        for pos in sorted(position_success_rates.keys()):
            stats = position_success_rates[pos]
            rate_color = ""
            rate_end = ""
            if stats["rate"] >= 70:
                rate_color = "\033[92m"  # 绿色
            elif stats["rate"] >= 50:
                rate_color = "\033[93m"  # 黄色
            elif stats["rate"] >= 30:
                rate_color = "\033[91m"  # 红色
            else:
                rate_color = "\033[90m"  # 灰色
            rate_end = "\033[0m"
            
            print(f"    - 第{pos}位: {rate_color}{stats['successful']}/{stats['total']} ({stats['rate']:.1f}%){rate_end}")
        
        # 计算累积Top-K建议成功率
        print(f"\n  累积Top-K建议成功率:")
        cumulative_stats = {}
        
        for k in [1, 2, 3, 4, 5]:
            total_suggestions_in_topk = 0
            successful_suggestions_in_topk = 0
            
            for vuln_data in vulnerabilities_with_topk_data:
                topk_success = vuln_data["topk_success"]
                # 统计前k个建议
                k_suggestions = topk_success[:min(k, len(topk_success))]
                total_suggestions_in_topk += len(k_suggestions)
                successful_suggestions_in_topk += sum(k_suggestions)
            
            if total_suggestions_in_topk > 0:
                cumulative_rate = (successful_suggestions_in_topk / total_suggestions_in_topk) * 100
                cumulative_stats[k] = {
                    "successful": successful_suggestions_in_topk,
                    "total": total_suggestions_in_topk,
                    "rate": cumulative_rate
                }
            else:
                cumulative_stats[k] = {"successful": 0, "total": 0, "rate": 0.0}
        
        print(f"    - Top-1建议成功率: {cumulative_stats[1]['successful']}/{cumulative_stats[1]['total']} ({cumulative_stats[1]['rate']:.1f}%)")
        print(f"    - Top-2建议成功率: {cumulative_stats[2]['successful']}/{cumulative_stats[2]['total']} ({cumulative_stats[2]['rate']:.1f}%)")
        print(f"    - Top-3建议成功率: {cumulative_stats[3]['successful']}/{cumulative_stats[3]['total']} ({cumulative_stats[3]['rate']:.1f}%)")
        print(f"    - Top-4建议成功率: {cumulative_stats[4]['successful']}/{cumulative_stats[4]['total']} ({cumulative_stats[4]['rate']:.1f}%)")
        print(f"    - Top-5建议成功率: {cumulative_stats[5]['successful']}/{cumulative_stats[5]['total']} ({cumulative_stats[5]['rate']:.1f}%)")
        
        # 分析建议质量趋势
        print(f"\n📈 建议质量趋势分析:")
        if len(position_success_rates) >= 3:
            pos1_rate = position_success_rates.get(1, {}).get("rate", 0)
            pos2_rate = position_success_rates.get(2, {}).get("rate", 0)
            pos3_rate = position_success_rates.get(3, {}).get("rate", 0)
            
            if pos1_rate > pos2_rate > pos3_rate:
                print(f"  - ✅ 建议排序质量良好：第1位 > 第2位 > 第3位")
            elif pos1_rate > pos2_rate:
                print(f"  - ⚡ 建议排序部分有效：第1位 > 第2位，但第3位表现异常")
            else:
                print(f"  - ⚠️ 建议排序需要优化：排序与质量不匹配")
            
            # 计算质量衰减率
            if pos1_rate > 0:
                quality_decay_1_to_2 = ((pos1_rate - pos2_rate) / pos1_rate) * 100 if pos2_rate < pos1_rate else 0
                quality_decay_1_to_3 = ((pos1_rate - pos3_rate) / pos1_rate) * 100 if pos3_rate < pos1_rate else 0
                print(f"  - 质量衰减：第1→2位 {quality_decay_1_to_2:.1f}%，第1→3位 {quality_decay_1_to_3:.1f}%")
        
        # 建议优化方向
        suggestion_success_rate = cumulative_stats[1]['rate']
        vulnerability_success_rate = topk_metrics[1]['rate']
        
        print(f"\n💡 优化建议:")
        if suggestion_success_rate < 40:
            print(f"  - 🔧 建议质量偏低({suggestion_success_rate:.1f}%)，需要改进修复算法")
        elif vulnerability_success_rate < suggestion_success_rate * 0.7:
            print(f"  - 📈 建议质量尚可({suggestion_success_rate:.1f}%)，但排序需要优化")
        else:
            print(f"  - ✨ 系统表现良好，建议质量({suggestion_success_rate:.1f}%)和排序都较为理想")
        
        # 新增：Top-N建议评估结果分布统计
        print(f"\n" + "="*80)
        print("📊 Top-N建议评估结果分布统计")
        print("="*80)
        
        # 1. 累积Top-K统计（Top-K包含前K个建议的累积统计）
        print(f"\n🔄 累积Top-K评估结果统计:")
        print(f"说明: Top-K包含前K个建议的累积统计")
        
        # 收集累积Top-K中每种评估结果的统计
        cumulative_topk_stats = {}  # k -> {"SynPatchEq": count, "SemEq": count, ...}
        
        for k in [1, 2, 3, 4, 5]:
            cumulative_topk_stats[k] = {
                "SynPatchEq": 0,
                "SemEq": 0, 
                "Plausible": 0,
                "Incorrect": 0,
                "Unknown": 0,
                "total": 0
            }
            
            # 累积统计前k个建议中每种评估结果的数量
            for vuln_data in vulnerabilities_with_topk_data:
                csv_row = vuln_data["csv_row"]
                matching_entry = next((entry for entry in results if entry.get("csv_row") == csv_row), None)
                
                if matching_entry:
                    llm_evaluations = matching_entry.get("llm_evaluations", [])
                    sorted_evaluations = sorted(llm_evaluations, key=lambda x: x.get("suggestion_index", 0))
                    
                    # 累积统计前k个建议
                    for i in range(min(k, len(sorted_evaluations))):
                        eval_result = sorted_evaluations[i].get("evaluation_result", "Unknown")
                        cumulative_topk_stats[k][eval_result] += 1
                        cumulative_topk_stats[k]["total"] += 1
        
        # 显示累积Top-K评估结果分布表格（绝对数量）
        print(f"\n📊 累积Top-K建议数量分布:")
        print(f"{'Top-K':<6} {'总数':<6} {'SynEq':<8} {'SemEq':<8} {'Plausible':<10} {'Incorrect':<10} {'Unknown':<8}")
        print("-" * 70)
        
        for k in [1, 2, 3, 4, 5]:
            stats = cumulative_topk_stats[k]
            total = stats["total"]
            
            if total > 0:
                syneq_count = stats["SynPatchEq"]
                semeq_count = stats["SemEq"]
                plausible_count = stats["Plausible"]
                incorrect_count = stats["Incorrect"]
                unknown_count = stats["Unknown"]
                
                print(f"Top-{k:<2} {total:<6} {syneq_count:<8} {semeq_count:<8} {plausible_count:<10} {incorrect_count:<10} {unknown_count:<8}")
        
        # 显示累积Top-K评估结果比例分布
        print(f"\n📈 累积Top-K建议比例分布:")
        print(f"{'Top-K':<6} {'SynEq%':<8} {'SemEq%':<8} {'Plausible%':<11} {'Incorrect%':<11} {'Unknown%':<9} {'成功率%':<8}")
        print("-" * 75)
        
        for k in [1, 2, 3, 4, 5]:
            stats = cumulative_topk_stats[k]
            total = stats["total"]
            
            if total > 0:
                syneq_pct = (stats["SynPatchEq"] / total) * 100
                semeq_pct = (stats["SemEq"] / total) * 100
                plausible_pct = (stats["Plausible"] / total) * 100
                incorrect_pct = (stats["Incorrect"] / total) * 100
                unknown_pct = (stats["Unknown"] / total) * 100
                success_pct = syneq_pct + semeq_pct + plausible_pct
                
                # 根据成功率选择颜色
                if success_pct >= 70:
                    color_start = "\033[92m"  # 绿色
                elif success_pct >= 50:
                    color_start = "\033[93m"  # 黄色
                else:
                    color_start = "\033[91m"  # 红色
                color_end = "\033[0m"
                
                print(f"{color_start}Top-{k:<2}{color_end} {syneq_pct:<7.1f} {semeq_pct:<7.1f} {plausible_pct:<10.1f} {incorrect_pct:<10.1f} {unknown_pct:<8.1f} {color_start}{success_pct:<7.1f}{color_end}")
        
        # 累积Top-K趋势分析
        print(f"\n📊 累积Top-K趋势分析:")
        
        if cumulative_topk_stats[1]["total"] > 0 and cumulative_topk_stats[5]["total"] > 0:
            # 分析各评估类型的累积分布趋势
            syneq_top1_pct = (cumulative_topk_stats[1]["SynPatchEq"] / cumulative_topk_stats[1]["total"]) * 100
            syneq_top5_pct = (cumulative_topk_stats[5]["SynPatchEq"] / cumulative_topk_stats[5]["total"]) * 100
            syneq_trend = syneq_top5_pct - syneq_top1_pct
            
            semeq_top1_pct = (cumulative_topk_stats[1]["SemEq"] / cumulative_topk_stats[1]["total"]) * 100
            semeq_top5_pct = (cumulative_topk_stats[5]["SemEq"] / cumulative_topk_stats[5]["total"]) * 100
            semeq_trend = semeq_top5_pct - semeq_top1_pct
            
            plausible_top1_pct = (cumulative_topk_stats[1]["Plausible"] / cumulative_topk_stats[1]["total"]) * 100
            plausible_top5_pct = (cumulative_topk_stats[5]["Plausible"] / cumulative_topk_stats[5]["total"]) * 100
            plausible_trend = plausible_top5_pct - plausible_top1_pct
            
            incorrect_top1_pct = (cumulative_topk_stats[1]["Incorrect"] / cumulative_topk_stats[1]["total"]) * 100
            incorrect_top5_pct = (cumulative_topk_stats[5]["Incorrect"] / cumulative_topk_stats[5]["total"]) * 100
            incorrect_trend = incorrect_top5_pct - incorrect_top1_pct
            
            print(f"  🎯 SynPatchEq: Top-1({syneq_top1_pct:.1f}%) → Top-5({syneq_top5_pct:.1f}%) 变化:{syneq_trend:+.1f}%")
            print(f"  ✅ SemEq: Top-1({semeq_top1_pct:.1f}%) → Top-5({semeq_top5_pct:.1f}%) 变化:{semeq_trend:+.1f}%")
            print(f"  ⚡ Plausible: Top-1({plausible_top1_pct:.1f}%) → Top-5({plausible_top5_pct:.1f}%) 变化:{plausible_trend:+.1f}%")
            print(f"  ❌ Incorrect: Top-1({incorrect_top1_pct:.1f}%) → Top-5({incorrect_top5_pct:.1f}%) 变化:{incorrect_trend:+.1f}%")
            
            # 累积质量分析
            print(f"\n💡 累积质量分析:")
            total_success_top1 = syneq_top1_pct + semeq_top1_pct + plausible_top1_pct
            total_success_top5 = syneq_top5_pct + semeq_top5_pct + plausible_top5_pct
            success_trend = total_success_top5 - total_success_top1
            
            if syneq_top1_pct > 30:
                print(f"  - 🎯 语法等价建议质量优秀: Top-1中{syneq_top1_pct:.1f}%为语法等价")
            elif syneq_top1_pct + semeq_top1_pct > 30:
                print(f"  - ✅ 等价建议质量良好: Top-1中{syneq_top1_pct + semeq_top1_pct:.1f}%为等价修复")
            elif total_success_top1 > 50:
                print(f"  - ⚡ 建议质量尚可: Top-1中{total_success_top1:.1f}%为可接受修复")
            else:
                print(f"  - ⚠️ 建议质量需要改进: Top-1成功率仅{total_success_top1:.1f}%")
            
            if abs(success_trend) < 5:
                print(f"  - 📊 累积效果稳定: Top-1到Top-5成功率变化很小({success_trend:+.1f}%)")
            elif success_trend > 0:
                print(f"  - 📈 增加候选有效: Top-5比Top-1成功率高{success_trend:.1f}%")
            else:
                print(f"  - 📉 质量集中度高: 最佳建议主要集中在前面位置")
        
        # 2. 单独位置统计（只统计特定位置的建议）
        print(f"\n" + "-"*80)
        print(f"📍 单独位置评估结果统计:")
        print(f"说明: 分别统计每个位置上建议的评估结果分布")
        
        position_eval_stats = {}
        
        # 统计各个位置的评估结果分布
        for vuln_data in vulnerabilities_with_topk_data:
            csv_row = vuln_data["csv_row"]
            matching_entry = next((entry for entry in results if entry.get("csv_row") == csv_row), None)
            
            if matching_entry:
                llm_evaluations = matching_entry.get("llm_evaluations", [])
                sorted_evaluations = sorted(llm_evaluations, key=lambda x: x.get("suggestion_index", 0))
                
                for i, eval_item in enumerate(sorted_evaluations[:5]):  # 只统计前5个位置
                    position = i + 1
                    eval_result = eval_item.get("evaluation_result", "Unknown")
                    
                    if position not in position_eval_stats:
                        position_eval_stats[position] = {
                            "SynPatchEq": 0, "SemEq": 0, "Plausible": 0, 
                            "Incorrect": 0, "Unknown": 0, "total": 0
                        }
                    
                    position_eval_stats[position][eval_result] += 1
                    position_eval_stats[position]["total"] += 1
        
        # 显示各位置的评估结果分布（绝对数量）
        print(f"\n📊 各位置建议数量分布:")
        print(f"{'位置':<6} {'总数':<6} {'SynEq':<8} {'SemEq':<8} {'Plausible':<10} {'Incorrect':<10} {'Unknown':<8}")
        print("-" * 70)
        
        for pos in sorted(position_eval_stats.keys()):
            stats = position_eval_stats[pos]
            total = stats["total"]
            
            if total > 0:
                syneq_count = stats["SynPatchEq"]
                semeq_count = stats["SemEq"]
                plausible_count = stats["Plausible"]
                incorrect_count = stats["Incorrect"]
                unknown_count = stats["Unknown"]
                
                print(f"第{pos}位  {total:<6} {syneq_count:<8} {semeq_count:<8} {plausible_count:<10} {incorrect_count:<10} {unknown_count:<8}")
        
        # 显示各位置的评估结果分布（比例）
        print(f"\n📈 各位置建议比例分布:")
        print(f"{'位置':<6} {'SynEq%':<8} {'SemEq%':<8} {'Plausible%':<11} {'Incorrect%':<11} {'Unknown%':<9} {'成功率%':<8}")
        print("-" * 75)
        
        for pos in sorted(position_eval_stats.keys()):
            stats = position_eval_stats[pos]
            total = stats["total"]
            
            if total > 0:
                syneq_pct = (stats["SynPatchEq"] / total) * 100
                semeq_pct = (stats["SemEq"] / total) * 100
                plausible_pct = (stats["Plausible"] / total) * 100
                incorrect_pct = (stats["Incorrect"] / total) * 100
                unknown_pct = (stats["Unknown"] / total) * 100
                success_pct = syneq_pct + semeq_pct + plausible_pct
                
                # 根据成功率选择颜色
                if success_pct >= 70:
                    color_start = "\033[92m"  # 绿色
                elif success_pct >= 50:
                    color_start = "\033[93m"  # 黄色
                elif success_pct >= 30:
                    color_start = "\033[91m"  # 红色
                else:
                    color_start = "\033[90m"  # 灰色
                color_end = "\033[0m"
                
                print(f"{color_start}第{pos}位{color_end}  {syneq_pct:<7.1f} {semeq_pct:<7.1f} {plausible_pct:<10.1f} {incorrect_pct:<10.1f} {unknown_pct:<8.1f} {color_start}{success_pct:<7.1f}{color_end}")
        
        # 单独位置质量衰减分析
        if len(position_eval_stats) >= 3:
            print(f"\n📉 位置质量衰减分析:")
            
            # 分析高质量建议(SynPatchEq + SemEq)的位置分布
            high_quality_by_position = {}
            success_rate_by_position = {}
            
            for pos, stats in position_eval_stats.items():
                if stats["total"] > 0:
                    high_quality_pct = ((stats["SynPatchEq"] + stats["SemEq"]) / stats["total"]) * 100
                    success_pct = ((stats["SynPatchEq"] + stats["SemEq"] + stats["Plausible"]) / stats["total"]) * 100
                    high_quality_by_position[pos] = high_quality_pct
                    success_rate_by_position[pos] = success_pct
            
            if len(high_quality_by_position) >= 3:
                pos1_hq = high_quality_by_position.get(1, 0)
                pos2_hq = high_quality_by_position.get(2, 0)
                pos3_hq = high_quality_by_position.get(3, 0)
                pos1_success = success_rate_by_position.get(1, 0)
                pos3_success = success_rate_by_position.get(3, 0)
                
                print(f"  - 高质量建议(SynEq+SemEq)分布: 第1位({pos1_hq:.1f}%) 第2位({pos2_hq:.1f}%) 第3位({pos3_hq:.1f}%)")
                print(f"  - 总成功率分布: 第1位({pos1_success:.1f}%) 第3位({pos3_success:.1f}%)")
                
                if pos1_hq > pos2_hq > pos3_hq:
                    print(f"  - ✅ 排序效果良好: 高质量建议集中在前面位置")
                elif pos1_hq > pos3_hq:
                    print(f"  - ⚡ 排序效果一般: 整体趋势正确但有波动")
                else:
                    print(f"  - ⚠️ 排序需要优化: 高质量建议分布不均")
                
                if len(high_quality_by_position) >= 5:
                    pos5_hq = high_quality_by_position.get(5, 0)
                    pos5_success = success_rate_by_position.get(5, 0)
                    decay_1_to_5_hq = pos1_hq - pos5_hq
                    decay_1_to_5_success = pos1_success - pos5_success
                    
                    print(f"  - 高质量建议衰减: 第1位到第5位衰减{decay_1_to_5_hq:.1f}%")
                    print(f"  - 总成功率衰减: 第1位到第5位衰减{decay_1_to_5_success:.1f}%")
                    
                    if decay_1_to_5_success < 10:
                        print(f"  - 💡 建议: 质量衰减较小，可考虑增加候选数量")
                    elif decay_1_to_5_success > 30:
                        print(f"  - 💡 建议: 质量衰减较大，重点优化排序算法")
                    else:
                        print(f"  - 💡 建议: 质量衰减适中，排序算法表现正常")
        
        # 累积vs单独位置对比分析
        print(f"\n🔍 累积 vs 单独位置对比分析:")
        
        if cumulative_topk_stats[1]["total"] > 0 and position_eval_stats.get(1, {}).get("total", 0) > 0:
            # Top-1的累积和单独位置应该是相同的
            cum_top1_success = ((cumulative_topk_stats[1]["SynPatchEq"] + cumulative_topk_stats[1]["SemEq"] + cumulative_topk_stats[1]["Plausible"]) / cumulative_topk_stats[1]["total"]) * 100
            pos1_success = ((position_eval_stats[1]["SynPatchEq"] + position_eval_stats[1]["SemEq"] + position_eval_stats[1]["Plausible"]) / position_eval_stats[1]["total"]) * 100
            
            print(f"  - Top-1成功率验证: 累积统计({cum_top1_success:.1f}%) = 位置统计({pos1_success:.1f}%) ✓")
            
            if cumulative_topk_stats[5]["total"] > 0:
                cum_top5_success = ((cumulative_topk_stats[5]["SynPatchEq"] + cumulative_topk_stats[5]["SemEq"] + cumulative_topk_stats[5]["Plausible"]) / cumulative_topk_stats[5]["total"]) * 100
                improvement = cum_top5_success - cum_top1_success
                
                print(f"  - Top-5累积效果: {cum_top5_success:.1f}% (相比Top-1提升{improvement:+.1f}%)")
                
                if improvement > 15:
                    print(f"  - 💡 结论: 增加候选建议数量能显著提升整体成功率")
                elif improvement > 5:
                    print(f"  - 💡 结论: 增加候选建议数量有一定帮助")
                else:
                    print(f"  - 💡 结论: 最佳建议主要集中在Top-1，增加候选数量收益有限")
    else:
        print("  📝 无足够的评估数据进行Top-K分析")
    
    print("\n" + "-"*50)
    
    # 显示修复建议级别的统计
    if total_entries_with_evaluation > 0:
        print(f"📈 修复建议评估统计 (处理了 {total_entries_with_evaluation} 个包含LLM评估的条目):")
        print(f"  - 总修复建议数: {global_eval_stats['total_suggestions']}")
        print(f"  - 🎯 语法等价 (SynPatchEq): {global_eval_stats['syntactic_patch_equivalent']}")
        print(f"  - ✅ 语义等价 (SemEq): {global_eval_stats['semantic_equivalent']}")
        print(f"  - ⚡ 合理性 (Plausible): {global_eval_stats['plausible']}")
        print(f"  - ❌ 不正确 (Incorrect): {global_eval_stats['incorrect']}")
        print(f"  - ❓ 未知 (Unknown): {global_eval_stats['unknown']}")
        
        # 计算全局成功率
        total_suggestions = global_eval_stats['total_suggestions']
        successful_suggestions = global_eval_stats['syntactic_patch_equivalent'] + global_eval_stats['semantic_equivalent'] + global_eval_stats['plausible']
        if total_suggestions > 0:
            success_rate = (successful_suggestions / total_suggestions) * 100
            print(f"\n🎯 修复建议成功率: {success_rate:.1f}% ({successful_suggestions}/{total_suggestions})")
    else:
        print("📝 无LLM评估数据")
    
    # 新增：显示数据处理统计
    print("\n" + "-"*50)
    print("📋 数据处理统计:")
    total_loaded = len(results)
    total_processed = skip_stats["total_processed"]
    total_skipped = skip_stats["total_skipped"]
    
    print(f"  - 📁 加载记录总数: {total_loaded}")
    print(f"  - ✅ 成功处理记录: {total_processed}")
    print(f"  - ⏭️ 跳过记录总数: {total_skipped}")
    
    if total_skipped > 0:
        print(f"\n📊 跳过原因分析:")
        print(f"  - ❌ 错误-缺少必要字段: {skip_stats['error_missing_fields']} 条")
        print(f"  - 📄 缺少原始代码: {skip_stats['missing_original_code']} 条")
        print(f"  - 🎯 缺少Ground Truth: {skip_stats['missing_fixed_code']} 条") 
        print(f"  - 📍 跳过-无漏洞行号: {skip_stats['skipped_no_line_numbers']} 条")
        print(f"  - 🔍 错误-差异分析失败: {skip_stats['error_diff_analysis']} 条")
        print(f"  - 🔧 无修复建议: {skip_stats['no_suggestion_found']} 条")
        print(f"  - ⚠️ 错误-修复生成异常: {skip_stats['error_repair_generation']} 条")
        
        # 计算处理率
        if total_loaded > 0:
            process_rate = (total_processed / total_loaded) * 100
            print(f"\n📈 数据处理率: {process_rate:.1f}% ({total_processed}/{total_loaded})")
            
            # 按错误类型分组显示
            data_issues = skip_stats['missing_original_code'] + skip_stats['missing_fixed_code'] + skip_stats['error_missing_fields']
            processing_issues = skip_stats['skipped_no_line_numbers'] + skip_stats['error_diff_analysis'] + skip_stats['error_repair_generation']
            algorithm_issues = skip_stats['no_suggestion_found']
            
            print(f"\n🔍 错误类型汇总:")
            print(f"  - 💾 数据质量问题: {data_issues} 条 ({data_issues/total_loaded*100:.1f}%)")
            print(f"  - ⚙️ 处理流程问题: {processing_issues} 条 ({processing_issues/total_loaded*100:.1f}%)")
            print(f"  - 🤖 算法生成问题: {algorithm_issues} 条 ({algorithm_issues/total_loaded*100:.1f}%)")
    
    # 新增：num_method_changed和项目分布分析
    if csv_file_path:
        print("\n" + "="*80)
        print("🔍 加载原始CSV数据进行扩展分析...")
        print("="*80)
        
        csv_data = load_original_csv_data(csv_file_path)
        if csv_data:
            print(f"成功加载 {len(csv_data)} 行原始CSV数据")
            
            # 分析num_method_changed分布
            analyze_num_method_changed_distribution(results, csv_data)
            
            # 分析项目分布
            analyze_project_distribution(results, csv_data)
        else:
            print("警告: 无法加载原始CSV数据，跳过扩展分析")
    else:
        print(f"\n💡 提示: 如需查看num_method_changed和项目分布分析，请使用--csv_file参数指定原始CSV文件路径")

    # Call the plotting function
    if vulnerabilities_with_topk_data:
        print("\n" + "="*80)
        print("🎨 Generating Top-N metrics charts...")
        print("="*80)
        plot_top_n_charts(vulnerabilities_with_topk_data, max_k=5) # Assuming analysis up to Top-5
        print("Chart generation complete!")
    else:
        print("\n⚠️ Not enough data to generate Top-N charts.")
    
    print(f"\nVisualization complete! Processed {total_processed}/{total_loaded} valid records.")

def _plot_single_chart(
    top_n_values: List[int],
    metric_data: Dict[str, List[float]],
    title: str,
    y_label: str,
    output_filename: str,
    is_cumulative: bool
) -> None:
    """
    Plots and saves a single Top-N metric chart.

    Args:
        top_n_values: List of N values for Top-N (e.g., [1, 2, 3, 4, 5])
        metric_data: Dictionary containing metric names and corresponding percentage lists
                     e.g., {"Cumulative Top-K Suggestion Success Rate": [10.0, 15.0, ...]}
        title: Chart title
        y_label: Y-axis label
        output_filename: Output image filename
        is_cumulative: Whether the chart is cumulative (affects X-axis label)
    """
    plt.figure(figsize=(16, 10))  # 改为16:10
    
    markers = ['o', 's', '^', 'D']  # Different markers for different lines
    line_styles = ['-', '--', '-.', ':'] # Different linestyles for different lines
    
    for i, (metric_name, percentages) in enumerate(metric_data.items()):
        if len(percentages) == len(top_n_values):
            plt.plot(top_n_values, percentages, marker=markers[i % len(markers)], linestyle=line_styles[i % len(line_styles)], label=metric_name)
            # Display value on each data point
            for x, y in zip(top_n_values, percentages):
                plt.text(x, y, f'{y:.1f}%', ha='center', va='bottom', fontsize=24)
        else:
            print(f"Warning: Number of data points ({len(percentages)}) for metric '{metric_name}' does not match number of Top-N values ({len(top_n_values)}). Skipping plot.")

    plt.xlabel(f"Top-N (N value{' - Cumulative' if is_cumulative else ''})", fontsize=30)
    plt.ylabel(y_label, fontsize=30)
    plt.title(title, fontsize=30)
    plt.xticks(top_n_values, fontsize=30) # Ensure X-axis only shows our defined N values
    plt.yticks(fontsize=30) # Add y-axis tick font size
    plt.ylim(0, 100) # Y-axis range from 0% to 100%
    plt.legend(loc='best', fontsize=25) # 调整图例位置和字体大小
    # plt.grid(True, linestyle='--', alpha=0.7) # Removed grid lines as per request
    
    try:
        # Ensure the output filename ends with .pdf
        if not output_filename.lower().endswith(".pdf"):
            output_filename = os.path.splitext(output_filename)[0] + ".pdf"
        plt.savefig(output_filename, format='pdf', dpi=200) # Save as PDF
        print(f"Chart saved to: {output_filename}")
    except Exception as e:
        print(f"Error: Failed to save chart '{output_filename}': {e}")
    plt.close() # Close the plot to free memory

def plot_top_n_charts(vulnerabilities_data: List[Dict[str, Any]], max_k: int = 5) -> None:
    """
    Calculates and plots Top-N metric charts.

    Args:
        vulnerabilities_data: List containing evaluation results for each vulnerability.
                              Each element should contain "topk_eval_results": List[str]
        max_k: Maximum N value to analyze (e.g., 5 means analyze Top-1 to Top-5)
    """
    if not vulnerabilities_data:
        print("No data available to plot Top-N charts.")
        return

    top_n_values = list(range(1, max_k + 1))
    num_vulnerabilities = len(vulnerabilities_data)

    # 初始化累积图表数据
    cumulative_success_rate = [0.0] * max_k
    cumulative_correct_rate = [0.0] * max_k # SynPatchEq only
    cumulative_semeq_syneq_rate = [0.0] * max_k # SemEq or SynPatchEq
    cumulative_plausible_rate = [0.0] * max_k # Plausible only

    # 初始化各位置单独图表数据
    individual_success_rate = [0.0] * max_k
    individual_correct_rate = [0.0] * max_k
    individual_semeq_syneq_rate = [0.0] * max_k
    individual_plausible_rate = [0.0] * max_k
    
    # 用于计算各位置单独成功率的分母（即在该位置有建议的漏洞数量）
    counts_at_individual_position = [0] * max_k

    for k_idx, k_val in enumerate(top_n_values): # k_val 是 1, 2, 3, 4, 5
        # 累积统计
        vulns_successful_cumulative = 0
        vulns_correct_cumulative = 0
        vulns_semeq_syneq_cumulative = 0
        vulns_plausible_cumulative = 0
        
        # 各位置单独统计
        suggestions_successful_individual = 0
        suggestions_correct_individual = 0
        suggestions_semeq_syneq_individual = 0
        suggestions_plausible_individual = 0

        for vuln_entry in vulnerabilities_data:
            eval_results = vuln_entry.get("topk_eval_results", [])
            
            # --- 累积计算 ---
            # 检查前 k_val 个建议中是否有任何一个满足条件
            has_success_cumulative = False
            has_correct_cumulative = False
            has_semeq_syneq_cumulative = False
            has_plausible_cumulative = False

            for i in range(min(k_val, len(eval_results))):
                res = eval_results[i]
                if res in ["SynPatchEq", "SemEq", "Plausible"]:
                    has_success_cumulative = True
                if res == "SynPatchEq":
                    has_correct_cumulative = True
                if res in ["SynPatchEq", "SemEq"]:
                    has_semeq_syneq_cumulative = True
                if res == "Plausible": # 仅Plausible
                    has_plausible_cumulative = True
            
            if has_success_cumulative:
                vulns_successful_cumulative += 1
            if has_correct_cumulative:
                vulns_correct_cumulative += 1
            if has_semeq_syneq_cumulative:
                vulns_semeq_syneq_cumulative += 1
            if has_plausible_cumulative:
                vulns_plausible_cumulative += 1

            # --- 各位置单独计算 (只关注第 k_val 个位置) ---
            if k_val <= len(eval_results): # 确保该漏洞在第 k_val 个位置有建议
                counts_at_individual_position[k_idx] += 1
                res_at_k = eval_results[k_val - 1] # k_val is 1-based, index is 0-based
                
                if res_at_k in ["SynPatchEq", "SemEq", "Plausible"]:
                    suggestions_successful_individual += 1
                if res_at_k == "SynPatchEq":
                    suggestions_correct_individual += 1
                if res_at_k in ["SynPatchEq", "SemEq"]:
                    suggestions_semeq_syneq_individual += 1
                if res_at_k == "Plausible":
                    suggestions_plausible_individual += 1
        
        # 计算累积百分比
        # num_vulnerabilities+=1
        cumulative_success_rate[k_idx] = (vulns_successful_cumulative / num_vulnerabilities) * 100 if num_vulnerabilities > 0 else 0
        cumulative_correct_rate[k_idx] = (vulns_correct_cumulative / num_vulnerabilities) * 100 if num_vulnerabilities > 0 else 0
        cumulative_semeq_syneq_rate[k_idx] = (vulns_semeq_syneq_cumulative / num_vulnerabilities) * 100 if num_vulnerabilities > 0 else 0
        cumulative_plausible_rate[k_idx] = (vulns_plausible_cumulative / num_vulnerabilities) * 100 if num_vulnerabilities > 0 else 0

        # 计算各位置单独百分比
        count_at_pos = counts_at_individual_position[k_idx]
        individual_success_rate[k_idx] = (suggestions_successful_individual / count_at_pos) * 100 if count_at_pos > 0 else 0
        individual_correct_rate[k_idx] = (suggestions_correct_individual / count_at_pos) * 100 if count_at_pos > 0 else 0
        individual_semeq_syneq_rate[k_idx] = (suggestions_semeq_syneq_individual / count_at_pos) * 100 if count_at_pos > 0 else 0
        individual_plausible_rate[k_idx] = (suggestions_plausible_individual / count_at_pos) * 100 if count_at_pos > 0 else 0

    # Prepare plot data in English
    cumulative_plot_data = {
        "Correct": cumulative_success_rate, # 缩短图例文字
        "SynEq": cumulative_correct_rate, # 缩短图例文字
        "SemEq": cumulative_semeq_syneq_rate, # 缩短图例文字
        "Plausible": cumulative_plausible_rate # 缩短图例文字
    }

    individual_plot_data = {
        "Indiv. Pos. Success (All)": individual_success_rate, # 缩短图例文字
        "Indiv. Pos. Correct (SynEq)": individual_correct_rate, # 缩短图例文字
        "Indiv. Pos. SemEq+SynEq": individual_semeq_syneq_rate, # 缩短图例文字
        "Indiv. Pos. Plausible": individual_plausible_rate # 缩短图例文字
    }

    # Plot Chart 1: Cumulative Metrics
    _plot_single_chart(
        top_n_values=top_n_values,
        metric_data=cumulative_plot_data,
        title="Cumulative Top-N Repair Metrics",
        y_label="Percentage (%)",
        output_filename="cumulative_top_n_metrics_plot.pdf", # Changed to .pdf
        is_cumulative=True
    )

    # Plot Chart 2: Individual Position Metrics
    _plot_single_chart(
        top_n_values=top_n_values,
        metric_data=individual_plot_data,
        title="Individual Position Top-N Repair Metrics",
        y_label="Percentage (%)",
        output_filename="individual_top_n_metrics_plot.pdf", # Changed to .pdf
        is_cumulative=False
    )

def main():
    parser = argparse.ArgumentParser(description="Visualize repair attempts against ground truth in the terminal.") # English description
    parser.add_argument("--results_file", required=True, help="Path to the JSON Lines file with repair results.") # English help
    parser.add_argument("--max-entries", "-m", type=int, default=None,
                        help="Maximum number of entries to process. Default is all entries.") # English help
    parser.add_argument("--csv_file", help="Path to the original CSV file for num_method_changed and project distribution analysis.") # English help
    parser.add_argument("--export_csv_rows_to", metavar="FILE_PATH", help="Export all unique CSV row numbers from the results to the specified file and exit.")
    parser.add_argument("--filter_by_csv_rows_file", metavar="FILE_PATH", help="Filter analysis to only include CSV row numbers read from the specified file.")
    args = parser.parse_args()
    
    visualize_repairs(
        args.results_file,
        args.max_entries,
        args.csv_file,
        args.export_csv_rows_to,
        args.filter_by_csv_rows_file
    )

if __name__ == "__main__":
    main()