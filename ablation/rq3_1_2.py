#!/usr/bin/env python3
"""
分析JSON文件中iterations_attempted字段的分布

用法: python rq3_2.py <json_file_path>
例如: python rq3_2.py /mnt/projects/unnamed/datasets/manual_example/outputs/strategy_analysis_verbose/strategy_analysis_output.json
"""

import json
import sys
import argparse
from collections import Counter
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

def analyze_iterations_distribution(json_file_path):
    """
    分析JSON文件中iterations_attempted字段的分布
    
    Args:
        json_file_path (str): JSON文件路径
    
    Returns:
        dict: 包含分析结果的字典
    """
    try:
        with open(json_file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
    except FileNotFoundError:
        print(f"错误: 文件 {json_file_path} 不存在")
        return None
    except json.JSONDecodeError as e:
        print(f"错误: JSON解析失败 - {e}")
        return None
    
    # 提取iterations_attempted值
    iterations_list = []
    for item in data:
        if isinstance(item, dict) and 'iterations_attempted' in item:
            iterations_list.append(item['iterations_attempted'])
    
    if not iterations_list:
        print("警告: 没有找到iterations_attempted字段")
        return None
    
    # 统计分布
    counter = Counter(iterations_list)
    total_count = len(iterations_list)
    
    # 计算统计信息
    mean_iterations = np.mean(iterations_list)
    median_iterations = np.median(iterations_list)
    std_iterations = np.std(iterations_list)
    min_iterations = min(iterations_list)
    max_iterations = max(iterations_list)
    
    # 准备结果
    result = {
        'total_samples': total_count,
        'distribution': dict(counter),
        'statistics': {
            'mean': mean_iterations,
            'median': median_iterations,
            'std': std_iterations,
            'min': min_iterations,
            'max': max_iterations
        }
    }
    
    return result, iterations_list

def print_analysis_results(result):
    """
    打印分析结果
    
    Args:
        result (dict): 分析结果字典
    """
    if not result:
        return
    
    print("\n" + "="*60)
    print("ITERATIONS_ATTEMPTED 分布分析结果")
    print("="*60)
    
    print(f"\n总样本数: {result['total_samples']}")
    
    print(f"\n统计信息:")
    stats = result['statistics']
    print(f"  平均值: {stats['mean']:.2f}")
    print(f"  中位数: {stats['median']:.2f}")
    print(f"  标准差: {stats['std']:.2f}")
    print(f"  最小值: {stats['min']}")
    print(f"  最大值: {stats['max']}")
    
    print(f"\n分布详情:")
    distribution = result['distribution']
    sorted_dist = sorted(distribution.items())
    
    for iterations, count in sorted_dist:
        percentage = (count / result['total_samples']) * 100
        print(f"  {iterations} 次尝试: {count} 个样本 ({percentage:.1f}%)")

def create_visualization(iterations_list, output_path=None):
    """
    创建可视化图表
    
    Args:
        iterations_list (list): iterations_attempted值列表
        output_path (str): 保存图片的路径，如果为None则显示图表
    """
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 6))
    
    # 直方图
    ax1.hist(iterations_list, bins=range(min(iterations_list), max(iterations_list) + 2), 
             alpha=0.7, edgecolor='black', color='skyblue')
    ax1.set_xlabel('Iterations Attempted')
    ax1.set_ylabel('频次')
    ax1.set_title('Iterations Attempted 分布直方图')
    ax1.grid(True, alpha=0.3)
    
    # 饼图
    counter = Counter(iterations_list)
    labels = [f'{k} 次' for k in sorted(counter.keys())]
    sizes = [counter[k] for k in sorted(counter.keys())]
    colors = plt.cm.Set3(range(len(labels)))
    
    ax2.pie(sizes, labels=labels, autopct='%1.1f%%', colors=colors, startangle=90)
    ax2.set_title('Iterations Attempted 分布饼图')
    
    plt.tight_layout()
    
    if output_path:
        plt.savefig(output_path, dpi=300, bbox_inches='tight')
        print(f"\n图表已保存到: {output_path}")
    else:
        plt.show()

def main():
    parser = argparse.ArgumentParser(
        description='分析JSON文件中iterations_attempted字段的分布',
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例用法:
  python rq3_2.py data.json
  python rq3_2.py data.json --output results.png
  python rq3_2.py data.json --no-plot
        """
    )
    
    parser.add_argument('json_file', help='要分析的JSON文件路径')
    parser.add_argument('--output', '-o', help='保存图表的文件路径（可选）')
    parser.add_argument('--no-plot', action='store_true', help='不生成图表，只显示统计信息')
    
    args = parser.parse_args()
    
    # 验证文件路径
    if not Path(args.json_file).exists():
        print(f"错误: 文件 {args.json_file} 不存在")
        sys.exit(1)
    
    # 分析数据
    result_data = analyze_iterations_distribution(args.json_file)
    if result_data is None:
        sys.exit(1)
    
    result, iterations_list = result_data
    
    # 打印结果
    print_analysis_results(result)
    
    # 生成图表（如果需要）
    if not args.no_plot:
        try:
            create_visualization(iterations_list, args.output)
        except ImportError:
            print("\n注意: matplotlib未安装，跳过图表生成")
            print("要安装matplotlib，请运行: pip install matplotlib")
        except Exception as e:
            print(f"\n生成图表时出错: {e}")

if __name__ == "__main__":
    main()
