import subprocess
import tempfile
import re
import os
from typing import List, <PERSON><PERSON>, Set

# Helper function to process code strings
def _get_lines_from_string(code_string: str) -> <PERSON>ple[List[str], int]:
    """Splits a string into lines, preserving line endings, and counts them."""
    if not code_string:
        return [], 0
    lines = code_string.splitlines(keepends=True)
    return lines, len(lines)

# parse_diff_output function (core logic from original, with mapping section carefully adapted)
def parse_diff_output(diff_text: str, total_old_lines: int, total_new_lines: int) -> Tuple[List[int], List[int], List[int], List[Tuple[int, int]]]:
    """
    Parses the output of 'git diff -U0' command.
    Returns:
        - modified_lines_old (list): Line numbers in the old file that were modified.
        - deleted_lines_old (list): Line numbers in the old file that were deleted.
        - added_lines_new (list): Line numbers in the new file that were added.
        - unchanged_mapping (list of tuples): (old_line_num, new_line_num) for unchanged lines.
    """
    modified_lines_old: Set[int] = set()
    deleted_lines_old: Set[int] = set()
    added_lines_new: Set[int] = set()

    hunks_info: List[Tuple[int, int, int, int]] = [] 
    hunk_header_regex = re.compile(r"^@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@")

    lines = diff_text.splitlines()
    for line in lines:
        match = hunk_header_regex.match(line)
        if match:
            old_start = int(match.group(1))
            old_len = int(match.group(2)) if match.group(2) is not None else 1
            new_start = int(match.group(3))
            new_len = int(match.group(4)) if match.group(4) is not None else 1
            
            hunks_info.append((old_start, old_len, new_start, new_len))

            if old_len > 0 and new_len > 0: # Modification
                for i in range(old_len):
                    modified_lines_old.add(old_start + i)
                for i in range(new_len): 
                    added_lines_new.add(new_start + i)
            elif old_len > 0 and new_len == 0: # Deletion
                for i in range(old_len):
                    deleted_lines_old.add(old_start + i)
            elif old_len == 0 and new_len > 0: # Addition
                for i in range(new_len):
                    added_lines_new.add(new_start + i)
    
    unchanged_mapping: List[Tuple[int, int]] = []
    hunks_info.sort(key=lambda h: (h[0], h[2]))

    current_old_line = 1
    current_new_line = 1
    hunk_idx = 0

    while current_old_line <= total_old_lines or current_new_line <= total_new_lines:
        processed_by_hunk_logic = False
        if hunk_idx < len(hunks_info):
            h_old_s, h_old_l, h_new_s, h_new_l = hunks_info[hunk_idx]

            if h_old_l == 0: 
                while current_new_line < h_new_s:
                    if current_old_line <= total_old_lines:
                         unchanged_mapping.append((current_old_line, current_new_line))
                         current_old_line += 1
                    current_new_line += 1
                
                current_new_line = h_new_s + h_new_l 
                hunk_idx += 1
                processed_by_hunk_logic = True
            elif current_old_line >= h_old_s and current_old_line < (h_old_s + h_old_l):
                current_old_line = h_old_s + h_old_l 
                current_new_line = h_new_s + h_new_l 
                hunk_idx += 1
                processed_by_hunk_logic = True
            
        if not processed_by_hunk_logic:
            if current_old_line <= total_old_lines and current_new_line <= total_new_lines:
                unchanged_mapping.append((current_old_line, current_new_line))
                current_old_line += 1
                current_new_line += 1
            elif current_old_line <= total_old_lines: 
                current_old_line += 1
            elif current_new_line <= total_new_lines: 
                current_new_line += 1
            else: 
                break
                
    return sorted(list(modified_lines_old)), sorted(list(deleted_lines_old)), sorted(list(added_lines_new)), unchanged_mapping

# Main analysis function
def analyze_code_diff(old_code_str: str, new_code_str: str) -> Tuple[List[int], List[int]]:
    """
    Analyzes the difference between two code strings.
    Returns:
        - changed_or_deleted_lines_old (List[int]): Line numbers in the old file that were modified or deleted.
        - added_or_modified_lines_new (List[int]): Line numbers in the new file that were added or are the new versions of modified lines.
    """
    old_code_lines, total_old_lines = _get_lines_from_string(old_code_str)
    new_code_lines, total_new_lines = _get_lines_from_string(new_code_str)

    if not old_code_str and not new_code_str:
        return [], []
    if not old_code_str:
        return [], list(range(1, total_new_lines + 1))
    if not new_code_str:
        return list(range(1, total_old_lines + 1)), []

    diff_text = None
    tmp_old_file_path = None
    tmp_new_file_path = None

    try:
        with tempfile.NamedTemporaryFile(mode='w+', delete=False, encoding='utf-8', prefix="old_", suffix=".tmp", newline='') as tmp_old_file, \
             tempfile.NamedTemporaryFile(mode='w+', delete=False, encoding='utf-8', prefix="new_", suffix=".tmp", newline='') as tmp_new_file:
            
            tmp_old_file.writelines(old_code_lines)
            tmp_old_file_path = tmp_old_file.name
            tmp_old_file.flush() 
            
            tmp_new_file.writelines(new_code_lines)
            tmp_new_file_path = tmp_new_file.name
            tmp_new_file.flush()

        cmd = ["git", "diff", "--no-index", "-U0", tmp_old_file_path, tmp_new_file_path]
        process = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', check=False)
        
        if process.returncode > 1:
            print(f"git diff 命令执行错误 (返回码 {process.returncode}):\n{process.stderr}")
            return [], []
        diff_text = process.stdout

    finally:
        if tmp_old_file_path and os.path.exists(tmp_old_file_path):
            os.remove(tmp_old_file_path)
        if tmp_new_file_path and os.path.exists(tmp_new_file_path):
            os.remove(tmp_new_file_path)

    if diff_text is None:
        print("未能获取 diff 输出。")
        return [], []
    
    if not diff_text.strip():
        # No textual changes detected by diff.
        # If line counts are the same, it's truly no change.
        # If line counts differ but diff is empty (e.g. only trailing whitespace changes),
        # we still report no changed/added/deleted lines based on diff's output.
        return [], []

    modified_old, deleted_old, added_new, _ = parse_diff_output(diff_text, total_old_lines, total_new_lines)
    
    changed_or_deleted_lines_old = sorted(list(set(modified_old) | set(deleted_old)))
    # added_new from parse_diff_output already includes lines that are new versions of modified lines
    added_or_modified_lines_new = added_new
    
    return changed_or_deleted_lines_old, added_or_modified_lines_new

if __name__ == "__main__":
    # Example Usage
    old_code_example = """line1 common
line2 old and changed
line3 old and deleted
line4 common
"""
    new_code_example = """line1 common
line2 new version of line2
line4 common
line5 new and added
"""
    print(f"分析示例代码差异 1:")
    print("--- 旧代码 ---")
    print(old_code_example.strip())
    print("--- 新代码 ---")
    print(new_code_example.strip())
    print("------ 结果 ------")

    changed_old_deleted, added_new_modified = analyze_code_diff(old_code_example, new_code_example)

    print(f"旧文件中修改或删除的行: {changed_old_deleted if changed_old_deleted else '无'}")
    print(f"新文件中新增或修改对应的行: {added_new_modified if added_new_modified else '无'}")
    # The mapping is no longer returned directly by analyze_code_diff,
    # so we cannot print it here in the same way.
    # If mapping is still needed for some examples, parse_diff_output would have to be called separately
    # or analyze_code_diff adjusted again. For now, removing mapping print.
    # print("不变行映射 (旧 -> 新):")
    # if mapping:
    #     for old_l, new_l in mapping:
    #         print(f"  旧 {old_l} -> 新 {new_l}")
    # else:
    #     print("  无不变行")

    print("\n--- 示例 2: 纯添加中间 ---")
    old_code_example_2 = "line A\nline B"
    new_code_example_2 = "line A\nline new1\nline new2\nline B"
    print(f"\n旧代码:\n{old_code_example_2}\n新代码:\n{new_code_example_2}")
    changed_old_deleted_2, added_new_modified_2 = analyze_code_diff(old_code_example_2, new_code_example_2)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_2}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_2}")
    # print("映射:")
    # for o, n in mapping_2: print(f"  旧 {o} -> 新 {n}")

    print("\n--- 示例 3: 纯删除中间 ---")
    old_code_example_3 = "line A\nline to delete\nline B"
    new_code_example_3 = "line A\nline B"
    print(f"\n旧代码:\n{old_code_example_3}\n新代码:\n{new_code_example_3}")
    changed_old_deleted_3, added_new_modified_3 = analyze_code_diff(old_code_example_3, new_code_example_3)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_3}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_3}")
    # print("映射:")
    # for o, n in mapping_3: print(f"  旧 {o} -> 新 {n}")

    print("\n--- 示例 4: 文件开头添加 ---") 
    old_code_example_4 = "line A\nline B"
    new_code_example_4 = "new line 0\nline A\nline B"
    print(f"\n旧代码:\n{old_code_example_4}\n新代码:\n{new_code_example_4}")
    changed_old_deleted_4, added_new_modified_4 = analyze_code_diff(old_code_example_4, new_code_example_4)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_4}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_4}")
    # print("映射:")
    # for o, n in mapping_4: print(f"  旧 {o} -> 新 {n}")

    print("\n--- 示例 5: 文件末尾添加 ---")
    old_code_example_5 = "line A\nline B"
    new_code_example_5 = "line A\nline B\nnew line 3"
    print(f"\n旧代码:\n{old_code_example_5}\n新代码:\n{new_code_example_5}")
    changed_old_deleted_5, added_new_modified_5 = analyze_code_diff(old_code_example_5, new_code_example_5)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_5}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_5}")
    # print("映射:")
    # for o, n in mapping_5: print(f"  旧 {o} -> 新 {n}")

    print("\n--- 示例 6: 空旧代码, 有新代码 ---")
    old_code_example_6 = ""
    new_code_example_6 = "new line 1\nnew line 2"
    print(f"\n旧代码:\n'{old_code_example_6}'\n新代码:\n{new_code_example_6}")
    changed_old_deleted_6, added_new_modified_6 = analyze_code_diff(old_code_example_6, new_code_example_6)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_6}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_6}")
    # print("映射:") # Mapping is no longer returned
    # if mapping_6:
    #   for o, n in mapping_6: print(f"  旧 {o} -> 新 {n}")
    # else: print("  无不变行")

    print("\n--- 示例 7: 有旧代码, 空新代码 ---")
    old_code_example_7 = "old line 1\nold line 2"
    new_code_example_7 = ""
    print(f"\n旧代码:\n{old_code_example_7}\n新代码:\n'{new_code_example_7}'")
    changed_old_deleted_7, added_new_modified_7 = analyze_code_diff(old_code_example_7, new_code_example_7)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_7}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_7}")
    # print("映射:") # Mapping is no longer returned
    # if mapping_7:
    #   for o, n in mapping_7: print(f"  旧 {o} -> 新 {n}")
    # else: print("  无不变行")

    print("\n--- 示例 8: 两者均为空 ---")
    old_code_example_8 = ""
    new_code_example_8 = ""
    print(f"\n旧代码:\n'{old_code_example_8}'\n新代码:\n'{new_code_example_8}'")
    changed_old_deleted_8, added_new_modified_8 = analyze_code_diff(old_code_example_8, new_code_example_8)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_8}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_8}")
    # print("映射:") # Mapping is no longer returned
    # if mapping_8:
    #   for o, n in mapping_8: print(f"  旧 {o} -> 新 {n}")
    # else: print("  无不变行")

    print("\n--- 示例 9: 完全相同的内容 ---")
    old_code_example_9 = "line A\nline B"
    new_code_example_9 = "line A\nline B"
    print(f"\n旧代码:\n{old_code_example_9}\n新代码:\n{new_code_example_9}")
    changed_old_deleted_9, added_new_modified_9 = analyze_code_diff(old_code_example_9, new_code_example_9)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_9}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_9}")
    # print("映射:") # Mapping is no longer returned
    # if mapping_9:
    #   for o, n in mapping_9: print(f"  旧 {o} -> 新 {n}")
    # else: print("  无不变行")

    print("\n--- 示例 10: 复杂修改 ---")
    old_code_example_10 = """def func_a():
    print("hello")
    val = 10
    return val

def func_b():
    print("world")
"""
    new_code_example_10 = """def func_a_renamed():
    print("hello there")
    new_val = 20
    return new_val

def func_c():
    print("new function")
"""
    print(f"\n旧代码:\n{old_code_example_10}\n新代码:\n{new_code_example_10}")
    changed_old_deleted_10, added_new_modified_10 = analyze_code_diff(old_code_example_10, new_code_example_10)
    print(f"旧文件中修改或删除的行: {changed_old_deleted_10}")
    print(f"新文件中新增或修改对应的行: {added_new_modified_10}")
    # print("映射:") # Mapping is no longer returned
    # if mapping_10:
    #   for o, n in mapping_10: print(f"  旧 {o} -> 新 {n}")
    # else: print("  无不变行")