id,name,parent_id,parent_name,url
284,Improper Access Control,,,https://cwe.mitre.org/data/definitions/284.html
1220,Insufficient Granularity of Access Control,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1220.html
1222,Insufficient Granularity of Address Regions Protected by Register Locks,1220,Insufficient Granularity of Access Control,https://cwe.mitre.org/data/definitions/1222.html
1222,Insufficient Granularity of Address Regions Protected by Register Locks,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1222.html
1224,Improper Restriction of Write-Once Bit Fields,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1224.html
1231,Improper Implementation of Lock Protection Registers,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1231.html
1242,Inclusion of Undocumented Features or Chicken Bits,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1242.html
1252,CPU Hardware Not Configured to Support Exclusivity of Write and Execute Operations,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1252.html
1256,Hardware Features Enable Physical Attacks from Software,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1256.html
1257,Improper Access Control Applied to Mirrored or Aliased Memory Regions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1257.html
1259,Improper Restriction of Security Token Assignment,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1259.html
1260,Improper Handling of Overlap Between Protected Memory Ranges,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1260.html
1262,Register Interface Allows Software Access to Sensitive Data or Security Settings,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1262.html
1263,Improper Physical Access Control,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1263.html
1243,Sensitive Non-Volatile Information Not Protected During Debug,1263,Improper Physical Access Control,https://cwe.mitre.org/data/definitions/1243.html
1243,Sensitive Non-Volatile Information Not Protected During Debug,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1243.html
1267,Policy Uses Obsolete Encoding,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1267.html
1268,Policy Privileges are not Assigned Consistently Between Control and Data Agents,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1268.html
1270,Generation of Incorrect Security Tokens,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1270.html
1274,Insufficient Protections on the Volatile Memory Containing Boot Code,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1274.html
1275,Sensitive Cookie with Improper SameSite Attribute,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1275.html
1276,Hardware Child Block Incorrectly Connected to Parent System,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1276.html
1280,Access Control Check Implemented After Asset is Accessed,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1280.html
1283,Mutable Attestation or Measurement Reporting Data,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1283.html
1290,Incorrect Decoding of Security Identifiers ,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1290.html
1292,Incorrect Conversion of Security Identifiers,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1292.html
1294,Insecure Security Identifier Mechanism,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1294.html
1302,Missing Security Identifier,1294,Insecure Security Identifier Mechanism,https://cwe.mitre.org/data/definitions/1302.html
1302,Missing Security Identifier,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1302.html
1296,Incorrect Chaining or Granularity of Debug Components,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1296.html
1304,Improperly Preserved Integrity of Hardware Configuration State During a Power Save/Restore Operation,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1304.html
1311,Improper Translation of Security Attributes by Fabric Bridge,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1311.html
1312,Missing Protection for Mirrored Regions in On-Chip Fabric Firewall,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1312.html
1313,Hardware Allows Activation of Test or Debug Logic at Runtime,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1313.html
1315,Improper Setting of Bus Controlling Capability in Fabric End-point,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1315.html
1316,Fabric-Address Map Allows Programming of Unwarranted Overlaps of Protected and Unprotected Ranges,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1316.html
1317,Missing Security Checks in Fabric Bridge,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1317.html
1320,Improper Protection for Out of Bounds Signal Level Alerts,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1320.html
1323,Improper Management of Sensitive Trace Data,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1323.html
1334,Unauthorized Error Injection Can Degrade Hardware Redundancy,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1334.html
269,Improper Privilege Management,284,Improper Access Control,https://cwe.mitre.org/data/definitions/269.html
250,Execution with Unnecessary Privileges,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/250.html
266,Incorrect Privilege Assignment,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/266.html
1022,Use of Web Link to Untrusted Target with window.opener Access,266,Incorrect Privilege Assignment,https://cwe.mitre.org/data/definitions/1022.html
520,.NET Misconfiguration: Use of Impersonation,266,Incorrect Privilege Assignment,https://cwe.mitre.org/data/definitions/520.html
556,ASP.NET Misconfiguration: Use of Identity Impersonation,266,Incorrect Privilege Assignment,https://cwe.mitre.org/data/definitions/556.html
9,J2EE Misconfiguration: Weak Access Permissions for EJB Methods,266,Incorrect Privilege Assignment,https://cwe.mitre.org/data/definitions/9.html
1022,Use of Web Link to Untrusted Target with window.opener Access,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/1022.html
520,.NET Misconfiguration: Use of Impersonation,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/520.html
556,ASP.NET Misconfiguration: Use of Identity Impersonation,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/556.html
9,J2EE Misconfiguration: Weak Access Permissions for EJB Methods,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/9.html
267,Privilege Defined With Unsafe Actions,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/267.html
623,Unsafe ActiveX Control Marked Safe For Scripting,267,Privilege Defined With Unsafe Actions,https://cwe.mitre.org/data/definitions/623.html
623,Unsafe ActiveX Control Marked Safe For Scripting,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/623.html
268,Privilege Chaining,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/268.html
270,Privilege Context Switching Error,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/270.html
271,Privilege Dropping / Lowering Errors,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/271.html
272,Least Privilege Violation,271,Privilege Dropping / Lowering Errors,https://cwe.mitre.org/data/definitions/272.html
273,Improper Check for Dropped Privileges,271,Privilege Dropping / Lowering Errors,https://cwe.mitre.org/data/definitions/273.html
272,Least Privilege Violation,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/272.html
273,Improper Check for Dropped Privileges,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/273.html
274,Improper Handling of Insufficient Privileges,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/274.html
648,Incorrect Use of Privileged APIs,269,Improper Privilege Management,https://cwe.mitre.org/data/definitions/648.html
250,Execution with Unnecessary Privileges,284,Improper Access Control,https://cwe.mitre.org/data/definitions/250.html
266,Incorrect Privilege Assignment,284,Improper Access Control,https://cwe.mitre.org/data/definitions/266.html
1022,Use of Web Link to Untrusted Target with window.opener Access,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1022.html
520,.NET Misconfiguration: Use of Impersonation,284,Improper Access Control,https://cwe.mitre.org/data/definitions/520.html
556,ASP.NET Misconfiguration: Use of Identity Impersonation,284,Improper Access Control,https://cwe.mitre.org/data/definitions/556.html
9,J2EE Misconfiguration: Weak Access Permissions for EJB Methods,284,Improper Access Control,https://cwe.mitre.org/data/definitions/9.html
267,Privilege Defined With Unsafe Actions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/267.html
623,Unsafe ActiveX Control Marked Safe For Scripting,284,Improper Access Control,https://cwe.mitre.org/data/definitions/623.html
268,Privilege Chaining,284,Improper Access Control,https://cwe.mitre.org/data/definitions/268.html
270,Privilege Context Switching Error,284,Improper Access Control,https://cwe.mitre.org/data/definitions/270.html
271,Privilege Dropping / Lowering Errors,284,Improper Access Control,https://cwe.mitre.org/data/definitions/271.html
272,Least Privilege Violation,284,Improper Access Control,https://cwe.mitre.org/data/definitions/272.html
273,Improper Check for Dropped Privileges,284,Improper Access Control,https://cwe.mitre.org/data/definitions/273.html
274,Improper Handling of Insufficient Privileges,284,Improper Access Control,https://cwe.mitre.org/data/definitions/274.html
648,Incorrect Use of Privileged APIs,284,Improper Access Control,https://cwe.mitre.org/data/definitions/648.html
282,Improper Ownership Management,284,Improper Access Control,https://cwe.mitre.org/data/definitions/282.html
283,Unverified Ownership,282,Improper Ownership Management,https://cwe.mitre.org/data/definitions/283.html
708,Incorrect Ownership Assignment,282,Improper Ownership Management,https://cwe.mitre.org/data/definitions/708.html
283,Unverified Ownership,284,Improper Access Control,https://cwe.mitre.org/data/definitions/283.html
708,Incorrect Ownership Assignment,284,Improper Access Control,https://cwe.mitre.org/data/definitions/708.html
285,Improper Authorization,284,Improper Access Control,https://cwe.mitre.org/data/definitions/285.html
1230,Exposure of Sensitive Information Through Metadata,285,Improper Authorization,https://cwe.mitre.org/data/definitions/1230.html
202,Exposure of Sensitive Information Through Data Queries,1230,Exposure of Sensitive Information Through Metadata,https://cwe.mitre.org/data/definitions/202.html
612,Improper Authorization of Index Containing Sensitive Information,1230,Exposure of Sensitive Information Through Metadata,https://cwe.mitre.org/data/definitions/612.html
202,Exposure of Sensitive Information Through Data Queries,285,Improper Authorization,https://cwe.mitre.org/data/definitions/202.html
612,Improper Authorization of Index Containing Sensitive Information,285,Improper Authorization,https://cwe.mitre.org/data/definitions/612.html
1244,Improper Access to Sensitive Information Using Debug and Test Interfaces,285,Improper Authorization,https://cwe.mitre.org/data/definitions/1244.html
1297,Unprotected Confidential Information on Device is Accessible by OSAT Vendors,285,Improper Authorization,https://cwe.mitre.org/data/definitions/1297.html
1328,Security Version Number Mutable to Older Versions,285,Improper Authorization,https://cwe.mitre.org/data/definitions/1328.html
552,Files or Directories Accessible to External Parties,285,Improper Authorization,https://cwe.mitre.org/data/definitions/552.html
219,Storage of File with Sensitive Data Under Web Root,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/219.html
433,Unparsed Raw Web Content Delivery,219,Storage of File with Sensitive Data Under Web Root,https://cwe.mitre.org/data/definitions/433.html
433,Unparsed Raw Web Content Delivery,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/433.html
220,Storage of File With Sensitive Data Under FTP Root,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/220.html
527,Exposure of Version-Control Repository to an Unauthorized Control Sphere,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/527.html
528,Exposure of Core Dump File to an Unauthorized Control Sphere,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/528.html
529,Exposure of Access Control List Files to an Unauthorized Control Sphere,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/529.html
530,Exposure of Backup File to an Unauthorized Control Sphere,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/530.html
539,Use of Persistent Cookies Containing Sensitive Information,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/539.html
553,Command Shell in Externally Accessible Directory,552,Files or Directories Accessible to External Parties,https://cwe.mitre.org/data/definitions/553.html
219,Storage of File with Sensitive Data Under Web Root,285,Improper Authorization,https://cwe.mitre.org/data/definitions/219.html
433,Unparsed Raw Web Content Delivery,285,Improper Authorization,https://cwe.mitre.org/data/definitions/433.html
220,Storage of File With Sensitive Data Under FTP Root,285,Improper Authorization,https://cwe.mitre.org/data/definitions/220.html
527,Exposure of Version-Control Repository to an Unauthorized Control Sphere,285,Improper Authorization,https://cwe.mitre.org/data/definitions/527.html
528,Exposure of Core Dump File to an Unauthorized Control Sphere,285,Improper Authorization,https://cwe.mitre.org/data/definitions/528.html
529,Exposure of Access Control List Files to an Unauthorized Control Sphere,285,Improper Authorization,https://cwe.mitre.org/data/definitions/529.html
530,Exposure of Backup File to an Unauthorized Control Sphere,285,Improper Authorization,https://cwe.mitre.org/data/definitions/530.html
539,Use of Persistent Cookies Containing Sensitive Information,285,Improper Authorization,https://cwe.mitre.org/data/definitions/539.html
553,Command Shell in Externally Accessible Directory,285,Improper Authorization,https://cwe.mitre.org/data/definitions/553.html
732,Incorrect Permission Assignment for Critical Resource,285,Improper Authorization,https://cwe.mitre.org/data/definitions/732.html
1004,Sensitive Cookie Without 'HttpOnly' Flag,732,Incorrect Permission Assignment for Critical Resource,https://cwe.mitre.org/data/definitions/1004.html
276,Incorrect Default Permissions,732,Incorrect Permission Assignment for Critical Resource,https://cwe.mitre.org/data/definitions/276.html
277,Insecure Inherited Permissions,732,Incorrect Permission Assignment for Critical Resource,https://cwe.mitre.org/data/definitions/277.html
278,Insecure Preserved Inherited Permissions,732,Incorrect Permission Assignment for Critical Resource,https://cwe.mitre.org/data/definitions/278.html
279,Incorrect Execution-Assigned Permissions,732,Incorrect Permission Assignment for Critical Resource,https://cwe.mitre.org/data/definitions/279.html
281,Improper Preservation of Permissions,732,Incorrect Permission Assignment for Critical Resource,https://cwe.mitre.org/data/definitions/281.html
1004,Sensitive Cookie Without 'HttpOnly' Flag,285,Improper Authorization,https://cwe.mitre.org/data/definitions/1004.html
276,Incorrect Default Permissions,285,Improper Authorization,https://cwe.mitre.org/data/definitions/276.html
277,Insecure Inherited Permissions,285,Improper Authorization,https://cwe.mitre.org/data/definitions/277.html
278,Insecure Preserved Inherited Permissions,285,Improper Authorization,https://cwe.mitre.org/data/definitions/278.html
279,Incorrect Execution-Assigned Permissions,285,Improper Authorization,https://cwe.mitre.org/data/definitions/279.html
281,Improper Preservation of Permissions,285,Improper Authorization,https://cwe.mitre.org/data/definitions/281.html
862,Missing Authorization,285,Improper Authorization,https://cwe.mitre.org/data/definitions/862.html
1314,Missing Write Protection for Parametric Data Values,862,Missing Authorization,https://cwe.mitre.org/data/definitions/1314.html
425,Direct Request ('Forced Browsing'),862,Missing Authorization,https://cwe.mitre.org/data/definitions/425.html
638,Not Using Complete Mediation,862,Missing Authorization,https://cwe.mitre.org/data/definitions/638.html
424,Improper Protection of Alternate Path,638,Not Using Complete Mediation,https://cwe.mitre.org/data/definitions/424.html
425,Direct Request ('Forced Browsing'),424,Improper Protection of Alternate Path,https://cwe.mitre.org/data/definitions/425.html
425,Direct Request ('Forced Browsing'),638,Not Using Complete Mediation,https://cwe.mitre.org/data/definitions/425.html
424,Improper Protection of Alternate Path,862,Missing Authorization,https://cwe.mitre.org/data/definitions/424.html
939,Improper Authorization in Handler for Custom URL Scheme,862,Missing Authorization,https://cwe.mitre.org/data/definitions/939.html
1314,Missing Write Protection for Parametric Data Values,285,Improper Authorization,https://cwe.mitre.org/data/definitions/1314.html
425,Direct Request ('Forced Browsing'),285,Improper Authorization,https://cwe.mitre.org/data/definitions/425.html
638,Not Using Complete Mediation,285,Improper Authorization,https://cwe.mitre.org/data/definitions/638.html
424,Improper Protection of Alternate Path,285,Improper Authorization,https://cwe.mitre.org/data/definitions/424.html
939,Improper Authorization in Handler for Custom URL Scheme,285,Improper Authorization,https://cwe.mitre.org/data/definitions/939.html
863,Incorrect Authorization,285,Improper Authorization,https://cwe.mitre.org/data/definitions/863.html
1191,Exposed Chip Debug and Test Interface With Insufficient or Missing Authorization,863,Incorrect Authorization,https://cwe.mitre.org/data/definitions/1191.html
551,Incorrect Behavior Order: Authorization Before Parsing and Canonicalization,863,Incorrect Authorization,https://cwe.mitre.org/data/definitions/551.html
639,Authorization Bypass Through User-Controlled Key,863,Incorrect Authorization,https://cwe.mitre.org/data/definitions/639.html
566,Authorization Bypass Through User-Controlled SQL Primary Key,639,Authorization Bypass Through User-Controlled Key,https://cwe.mitre.org/data/definitions/566.html
566,Authorization Bypass Through User-Controlled SQL Primary Key,863,Incorrect Authorization,https://cwe.mitre.org/data/definitions/566.html
647,Use of Non-Canonical URL Paths for Authorization Decisions,863,Incorrect Authorization,https://cwe.mitre.org/data/definitions/647.html
804,Guessable CAPTCHA,863,Incorrect Authorization,https://cwe.mitre.org/data/definitions/804.html
1191,Exposed Chip Debug and Test Interface With Insufficient or Missing Authorization,285,Improper Authorization,https://cwe.mitre.org/data/definitions/1191.html
551,Incorrect Behavior Order: Authorization Before Parsing and Canonicalization,285,Improper Authorization,https://cwe.mitre.org/data/definitions/551.html
639,Authorization Bypass Through User-Controlled Key,285,Improper Authorization,https://cwe.mitre.org/data/definitions/639.html
566,Authorization Bypass Through User-Controlled SQL Primary Key,285,Improper Authorization,https://cwe.mitre.org/data/definitions/566.html
647,Use of Non-Canonical URL Paths for Authorization Decisions,285,Improper Authorization,https://cwe.mitre.org/data/definitions/647.html
804,Guessable CAPTCHA,285,Improper Authorization,https://cwe.mitre.org/data/definitions/804.html
926,Improper Export of Android Application Components,285,Improper Authorization,https://cwe.mitre.org/data/definitions/926.html
927,Use of Implicit Intent for Sensitive Communication,285,Improper Authorization,https://cwe.mitre.org/data/definitions/927.html
1230,Exposure of Sensitive Information Through Metadata,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1230.html
202,Exposure of Sensitive Information Through Data Queries,284,Improper Access Control,https://cwe.mitre.org/data/definitions/202.html
612,Improper Authorization of Index Containing Sensitive Information,284,Improper Access Control,https://cwe.mitre.org/data/definitions/612.html
1244,Improper Access to Sensitive Information Using Debug and Test Interfaces,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1244.html
1297,Unprotected Confidential Information on Device is Accessible by OSAT Vendors,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1297.html
1328,Security Version Number Mutable to Older Versions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1328.html
552,Files or Directories Accessible to External Parties,284,Improper Access Control,https://cwe.mitre.org/data/definitions/552.html
219,Storage of File with Sensitive Data Under Web Root,284,Improper Access Control,https://cwe.mitre.org/data/definitions/219.html
433,Unparsed Raw Web Content Delivery,284,Improper Access Control,https://cwe.mitre.org/data/definitions/433.html
220,Storage of File With Sensitive Data Under FTP Root,284,Improper Access Control,https://cwe.mitre.org/data/definitions/220.html
527,Exposure of Version-Control Repository to an Unauthorized Control Sphere,284,Improper Access Control,https://cwe.mitre.org/data/definitions/527.html
528,Exposure of Core Dump File to an Unauthorized Control Sphere,284,Improper Access Control,https://cwe.mitre.org/data/definitions/528.html
529,Exposure of Access Control List Files to an Unauthorized Control Sphere,284,Improper Access Control,https://cwe.mitre.org/data/definitions/529.html
530,Exposure of Backup File to an Unauthorized Control Sphere,284,Improper Access Control,https://cwe.mitre.org/data/definitions/530.html
539,Use of Persistent Cookies Containing Sensitive Information,284,Improper Access Control,https://cwe.mitre.org/data/definitions/539.html
553,Command Shell in Externally Accessible Directory,284,Improper Access Control,https://cwe.mitre.org/data/definitions/553.html
732,Incorrect Permission Assignment for Critical Resource,284,Improper Access Control,https://cwe.mitre.org/data/definitions/732.html
1004,Sensitive Cookie Without 'HttpOnly' Flag,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1004.html
276,Incorrect Default Permissions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/276.html
277,Insecure Inherited Permissions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/277.html
278,Insecure Preserved Inherited Permissions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/278.html
279,Incorrect Execution-Assigned Permissions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/279.html
281,Improper Preservation of Permissions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/281.html
862,Missing Authorization,284,Improper Access Control,https://cwe.mitre.org/data/definitions/862.html
1314,Missing Write Protection for Parametric Data Values,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1314.html
425,Direct Request ('Forced Browsing'),284,Improper Access Control,https://cwe.mitre.org/data/definitions/425.html
638,Not Using Complete Mediation,284,Improper Access Control,https://cwe.mitre.org/data/definitions/638.html
424,Improper Protection of Alternate Path,284,Improper Access Control,https://cwe.mitre.org/data/definitions/424.html
939,Improper Authorization in Handler for Custom URL Scheme,284,Improper Access Control,https://cwe.mitre.org/data/definitions/939.html
863,Incorrect Authorization,284,Improper Access Control,https://cwe.mitre.org/data/definitions/863.html
1191,Exposed Chip Debug and Test Interface With Insufficient or Missing Authorization,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1191.html
551,Incorrect Behavior Order: Authorization Before Parsing and Canonicalization,284,Improper Access Control,https://cwe.mitre.org/data/definitions/551.html
639,Authorization Bypass Through User-Controlled Key,284,Improper Access Control,https://cwe.mitre.org/data/definitions/639.html
566,Authorization Bypass Through User-Controlled SQL Primary Key,284,Improper Access Control,https://cwe.mitre.org/data/definitions/566.html
647,Use of Non-Canonical URL Paths for Authorization Decisions,284,Improper Access Control,https://cwe.mitre.org/data/definitions/647.html
804,Guessable CAPTCHA,284,Improper Access Control,https://cwe.mitre.org/data/definitions/804.html
926,Improper Export of Android Application Components,284,Improper Access Control,https://cwe.mitre.org/data/definitions/926.html
927,Use of Implicit Intent for Sensitive Communication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/927.html
286,Incorrect User Management,284,Improper Access Control,https://cwe.mitre.org/data/definitions/286.html
842,Placement of User into Incorrect Group,286,Incorrect User Management,https://cwe.mitre.org/data/definitions/842.html
842,Placement of User into Incorrect Group,284,Improper Access Control,https://cwe.mitre.org/data/definitions/842.html
287,Improper Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/287.html
261,Weak Encoding for Password,287,Improper Authentication,https://cwe.mitre.org/data/definitions/261.html
262,Not Using Password Aging,287,Improper Authentication,https://cwe.mitre.org/data/definitions/262.html
263,Password Aging with Long Expiration,287,Improper Authentication,https://cwe.mitre.org/data/definitions/263.html
288,Authentication Bypass Using an Alternate Path or Channel,287,Improper Authentication,https://cwe.mitre.org/data/definitions/288.html
1299,Missing Protection Mechanism for Alternate Hardware Interface,288,Authentication Bypass Using an Alternate Path or Channel,https://cwe.mitre.org/data/definitions/1299.html
425,Direct Request ('Forced Browsing'),288,Authentication Bypass Using an Alternate Path or Channel,https://cwe.mitre.org/data/definitions/425.html
1299,Missing Protection Mechanism for Alternate Hardware Interface,287,Improper Authentication,https://cwe.mitre.org/data/definitions/1299.html
425,Direct Request ('Forced Browsing'),287,Improper Authentication,https://cwe.mitre.org/data/definitions/425.html
289,Authentication Bypass by Alternate Name,287,Improper Authentication,https://cwe.mitre.org/data/definitions/289.html
290,Authentication Bypass by Spoofing,287,Improper Authentication,https://cwe.mitre.org/data/definitions/290.html
291,Reliance on IP Address for Authentication,290,Authentication Bypass by Spoofing,https://cwe.mitre.org/data/definitions/291.html
293,Using Referer Field for Authentication,290,Authentication Bypass by Spoofing,https://cwe.mitre.org/data/definitions/293.html
350,Reliance on Reverse DNS Resolution for a Security-Critical Action,290,Authentication Bypass by Spoofing,https://cwe.mitre.org/data/definitions/350.html
291,Reliance on IP Address for Authentication,287,Improper Authentication,https://cwe.mitre.org/data/definitions/291.html
293,Using Referer Field for Authentication,287,Improper Authentication,https://cwe.mitre.org/data/definitions/293.html
350,Reliance on Reverse DNS Resolution for a Security-Critical Action,287,Improper Authentication,https://cwe.mitre.org/data/definitions/350.html
294,Authentication Bypass by Capture-replay,287,Improper Authentication,https://cwe.mitre.org/data/definitions/294.html
295,Improper Certificate Validation,287,Improper Authentication,https://cwe.mitre.org/data/definitions/295.html
296,Improper Following of a Certificate's Chain of Trust,295,Improper Certificate Validation,https://cwe.mitre.org/data/definitions/296.html
297,Improper Validation of Certificate with Host Mismatch,295,Improper Certificate Validation,https://cwe.mitre.org/data/definitions/297.html
298,Improper Validation of Certificate Expiration,295,Improper Certificate Validation,https://cwe.mitre.org/data/definitions/298.html
299,Improper Check for Certificate Revocation,295,Improper Certificate Validation,https://cwe.mitre.org/data/definitions/299.html
370,Missing Check for Certificate Revocation after Initial Check,299,Improper Check for Certificate Revocation,https://cwe.mitre.org/data/definitions/370.html
370,Missing Check for Certificate Revocation after Initial Check,295,Improper Certificate Validation,https://cwe.mitre.org/data/definitions/370.html
599,Missing Validation of OpenSSL Certificate,295,Improper Certificate Validation,https://cwe.mitre.org/data/definitions/599.html
296,Improper Following of a Certificate's Chain of Trust,287,Improper Authentication,https://cwe.mitre.org/data/definitions/296.html
297,Improper Validation of Certificate with Host Mismatch,287,Improper Authentication,https://cwe.mitre.org/data/definitions/297.html
298,Improper Validation of Certificate Expiration,287,Improper Authentication,https://cwe.mitre.org/data/definitions/298.html
299,Improper Check for Certificate Revocation,287,Improper Authentication,https://cwe.mitre.org/data/definitions/299.html
370,Missing Check for Certificate Revocation after Initial Check,287,Improper Authentication,https://cwe.mitre.org/data/definitions/370.html
599,Missing Validation of OpenSSL Certificate,287,Improper Authentication,https://cwe.mitre.org/data/definitions/599.html
301,Reflection Attack in an Authentication Protocol,287,Improper Authentication,https://cwe.mitre.org/data/definitions/301.html
302,Authentication Bypass by Assumed-Immutable Data,287,Improper Authentication,https://cwe.mitre.org/data/definitions/302.html
303,Incorrect Implementation of Authentication Algorithm,287,Improper Authentication,https://cwe.mitre.org/data/definitions/303.html
304,Missing Critical Step in Authentication,287,Improper Authentication,https://cwe.mitre.org/data/definitions/304.html
305,Authentication Bypass by Primary Weakness,287,Improper Authentication,https://cwe.mitre.org/data/definitions/305.html
306,Missing Authentication for Critical Function,287,Improper Authentication,https://cwe.mitre.org/data/definitions/306.html
307,Improper Restriction of Excessive Authentication Attempts,287,Improper Authentication,https://cwe.mitre.org/data/definitions/307.html
308,Use of Single-factor Authentication,287,Improper Authentication,https://cwe.mitre.org/data/definitions/308.html
309,Use of Password System for Primary Authentication,287,Improper Authentication,https://cwe.mitre.org/data/definitions/309.html
521,Weak Password Requirements,287,Improper Authentication,https://cwe.mitre.org/data/definitions/521.html
258,Empty Password in Configuration File,521,Weak Password Requirements,https://cwe.mitre.org/data/definitions/258.html
258,Empty Password in Configuration File,287,Improper Authentication,https://cwe.mitre.org/data/definitions/258.html
522,Insufficiently Protected Credentials,287,Improper Authentication,https://cwe.mitre.org/data/definitions/522.html
256,Plaintext Storage of a Password,522,Insufficiently Protected Credentials,https://cwe.mitre.org/data/definitions/256.html
257,Storing Passwords in a Recoverable Format,522,Insufficiently Protected Credentials,https://cwe.mitre.org/data/definitions/257.html
260,Password in Configuration File,522,Insufficiently Protected Credentials,https://cwe.mitre.org/data/definitions/260.html
13,ASP.NET Misconfiguration: Password in Configuration File,260,Password in Configuration File,https://cwe.mitre.org/data/definitions/13.html
258,Empty Password in Configuration File,260,Password in Configuration File,https://cwe.mitre.org/data/definitions/258.html
555,J2EE Misconfiguration: Plaintext Password in Configuration File,260,Password in Configuration File,https://cwe.mitre.org/data/definitions/555.html
13,ASP.NET Misconfiguration: Password in Configuration File,522,Insufficiently Protected Credentials,https://cwe.mitre.org/data/definitions/13.html
258,Empty Password in Configuration File,522,Insufficiently Protected Credentials,https://cwe.mitre.org/data/definitions/258.html
555,J2EE Misconfiguration: Plaintext Password in Configuration File,522,Insufficiently Protected Credentials,https://cwe.mitre.org/data/definitions/555.html
523,Unprotected Transport of Credentials,522,Insufficiently Protected Credentials,https://cwe.mitre.org/data/definitions/523.html
549,Missing Password Field Masking,522,Insufficiently Protected Credentials,https://cwe.mitre.org/data/definitions/549.html
256,Plaintext Storage of a Password,287,Improper Authentication,https://cwe.mitre.org/data/definitions/256.html
257,Storing Passwords in a Recoverable Format,287,Improper Authentication,https://cwe.mitre.org/data/definitions/257.html
260,Password in Configuration File,287,Improper Authentication,https://cwe.mitre.org/data/definitions/260.html
13,ASP.NET Misconfiguration: Password in Configuration File,287,Improper Authentication,https://cwe.mitre.org/data/definitions/13.html
555,J2EE Misconfiguration: Plaintext Password in Configuration File,287,Improper Authentication,https://cwe.mitre.org/data/definitions/555.html
523,Unprotected Transport of Credentials,287,Improper Authentication,https://cwe.mitre.org/data/definitions/523.html
549,Missing Password Field Masking,287,Improper Authentication,https://cwe.mitre.org/data/definitions/549.html
593,Authentication Bypass: OpenSSL CTX Object Modified after SSL Objects are Created,287,Improper Authentication,https://cwe.mitre.org/data/definitions/593.html
603,Use of Client-Side Authentication,287,Improper Authentication,https://cwe.mitre.org/data/definitions/603.html
620,Unverified Password Change,287,Improper Authentication,https://cwe.mitre.org/data/definitions/620.html
640,Weak Password Recovery Mechanism for Forgotten Password,287,Improper Authentication,https://cwe.mitre.org/data/definitions/640.html
645,Overly Restrictive Account Lockout Mechanism,287,Improper Authentication,https://cwe.mitre.org/data/definitions/645.html
798,Use of Hard-coded Credentials,287,Improper Authentication,https://cwe.mitre.org/data/definitions/798.html
259,Use of Hard-coded Password,798,Use of Hard-coded Credentials,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,798,Use of Hard-coded Credentials,https://cwe.mitre.org/data/definitions/321.html
259,Use of Hard-coded Password,287,Improper Authentication,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,287,Improper Authentication,https://cwe.mitre.org/data/definitions/321.html
804,Guessable CAPTCHA,287,Improper Authentication,https://cwe.mitre.org/data/definitions/804.html
836,Use of Password Hash Instead of Password for Authentication,287,Improper Authentication,https://cwe.mitre.org/data/definitions/836.html
261,Weak Encoding for Password,284,Improper Access Control,https://cwe.mitre.org/data/definitions/261.html
262,Not Using Password Aging,284,Improper Access Control,https://cwe.mitre.org/data/definitions/262.html
263,Password Aging with Long Expiration,284,Improper Access Control,https://cwe.mitre.org/data/definitions/263.html
288,Authentication Bypass Using an Alternate Path or Channel,284,Improper Access Control,https://cwe.mitre.org/data/definitions/288.html
1299,Missing Protection Mechanism for Alternate Hardware Interface,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1299.html
289,Authentication Bypass by Alternate Name,284,Improper Access Control,https://cwe.mitre.org/data/definitions/289.html
290,Authentication Bypass by Spoofing,284,Improper Access Control,https://cwe.mitre.org/data/definitions/290.html
291,Reliance on IP Address for Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/291.html
293,Using Referer Field for Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/293.html
350,Reliance on Reverse DNS Resolution for a Security-Critical Action,284,Improper Access Control,https://cwe.mitre.org/data/definitions/350.html
294,Authentication Bypass by Capture-replay,284,Improper Access Control,https://cwe.mitre.org/data/definitions/294.html
295,Improper Certificate Validation,284,Improper Access Control,https://cwe.mitre.org/data/definitions/295.html
296,Improper Following of a Certificate's Chain of Trust,284,Improper Access Control,https://cwe.mitre.org/data/definitions/296.html
297,Improper Validation of Certificate with Host Mismatch,284,Improper Access Control,https://cwe.mitre.org/data/definitions/297.html
298,Improper Validation of Certificate Expiration,284,Improper Access Control,https://cwe.mitre.org/data/definitions/298.html
299,Improper Check for Certificate Revocation,284,Improper Access Control,https://cwe.mitre.org/data/definitions/299.html
370,Missing Check for Certificate Revocation after Initial Check,284,Improper Access Control,https://cwe.mitre.org/data/definitions/370.html
599,Missing Validation of OpenSSL Certificate,284,Improper Access Control,https://cwe.mitre.org/data/definitions/599.html
301,Reflection Attack in an Authentication Protocol,284,Improper Access Control,https://cwe.mitre.org/data/definitions/301.html
302,Authentication Bypass by Assumed-Immutable Data,284,Improper Access Control,https://cwe.mitre.org/data/definitions/302.html
303,Incorrect Implementation of Authentication Algorithm,284,Improper Access Control,https://cwe.mitre.org/data/definitions/303.html
304,Missing Critical Step in Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/304.html
305,Authentication Bypass by Primary Weakness,284,Improper Access Control,https://cwe.mitre.org/data/definitions/305.html
306,Missing Authentication for Critical Function,284,Improper Access Control,https://cwe.mitre.org/data/definitions/306.html
307,Improper Restriction of Excessive Authentication Attempts,284,Improper Access Control,https://cwe.mitre.org/data/definitions/307.html
308,Use of Single-factor Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/308.html
309,Use of Password System for Primary Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/309.html
521,Weak Password Requirements,284,Improper Access Control,https://cwe.mitre.org/data/definitions/521.html
258,Empty Password in Configuration File,284,Improper Access Control,https://cwe.mitre.org/data/definitions/258.html
522,Insufficiently Protected Credentials,284,Improper Access Control,https://cwe.mitre.org/data/definitions/522.html
256,Plaintext Storage of a Password,284,Improper Access Control,https://cwe.mitre.org/data/definitions/256.html
257,Storing Passwords in a Recoverable Format,284,Improper Access Control,https://cwe.mitre.org/data/definitions/257.html
260,Password in Configuration File,284,Improper Access Control,https://cwe.mitre.org/data/definitions/260.html
13,ASP.NET Misconfiguration: Password in Configuration File,284,Improper Access Control,https://cwe.mitre.org/data/definitions/13.html
555,J2EE Misconfiguration: Plaintext Password in Configuration File,284,Improper Access Control,https://cwe.mitre.org/data/definitions/555.html
523,Unprotected Transport of Credentials,284,Improper Access Control,https://cwe.mitre.org/data/definitions/523.html
549,Missing Password Field Masking,284,Improper Access Control,https://cwe.mitre.org/data/definitions/549.html
593,Authentication Bypass: OpenSSL CTX Object Modified after SSL Objects are Created,284,Improper Access Control,https://cwe.mitre.org/data/definitions/593.html
603,Use of Client-Side Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/603.html
620,Unverified Password Change,284,Improper Access Control,https://cwe.mitre.org/data/definitions/620.html
640,Weak Password Recovery Mechanism for Forgotten Password,284,Improper Access Control,https://cwe.mitre.org/data/definitions/640.html
645,Overly Restrictive Account Lockout Mechanism,284,Improper Access Control,https://cwe.mitre.org/data/definitions/645.html
798,Use of Hard-coded Credentials,284,Improper Access Control,https://cwe.mitre.org/data/definitions/798.html
259,Use of Hard-coded Password,284,Improper Access Control,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,284,Improper Access Control,https://cwe.mitre.org/data/definitions/321.html
836,Use of Password Hash Instead of Password for Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/836.html
346,Origin Validation Error,284,Improper Access Control,https://cwe.mitre.org/data/definitions/346.html
923,Improper Restriction of Communication Channel to Intended Endpoints,284,Improper Access Control,https://cwe.mitre.org/data/definitions/923.html
291,Reliance on IP Address for Authentication,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/291.html
297,Improper Validation of Certificate with Host Mismatch,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/297.html
300,Channel Accessible by Non-Endpoint,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/300.html
1324,Sensitive Information Accessible by Physical Probing of JTAG Interface,300,Channel Accessible by Non-Endpoint,https://cwe.mitre.org/data/definitions/1324.html
1324,Sensitive Information Accessible by Physical Probing of JTAG Interface,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/1324.html
322,Key Exchange without Entity Authentication,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/322.html
350,Reliance on Reverse DNS Resolution for a Security-Critical Action,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/350.html
419,Unprotected Primary Channel,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/419.html
420,Unprotected Alternate Channel,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/420.html
1299,Missing Protection Mechanism for Alternate Hardware Interface,420,Unprotected Alternate Channel,https://cwe.mitre.org/data/definitions/1299.html
421,Race Condition During Access to Alternate Channel,420,Unprotected Alternate Channel,https://cwe.mitre.org/data/definitions/421.html
422,Unprotected Windows Messaging Channel ('Shatter'),420,Unprotected Alternate Channel,https://cwe.mitre.org/data/definitions/422.html
1299,Missing Protection Mechanism for Alternate Hardware Interface,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/1299.html
421,Race Condition During Access to Alternate Channel,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/421.html
422,Unprotected Windows Messaging Channel ('Shatter'),923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/422.html
925,Improper Verification of Intent by Broadcast Receiver,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/925.html
940,Improper Verification of Source of a Communication Channel,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/940.html
941,Incorrectly Specified Destination in a Communication Channel,923,Improper Restriction of Communication Channel to Intended Endpoints,https://cwe.mitre.org/data/definitions/941.html
300,Channel Accessible by Non-Endpoint,284,Improper Access Control,https://cwe.mitre.org/data/definitions/300.html
1324,Sensitive Information Accessible by Physical Probing of JTAG Interface,284,Improper Access Control,https://cwe.mitre.org/data/definitions/1324.html
322,Key Exchange without Entity Authentication,284,Improper Access Control,https://cwe.mitre.org/data/definitions/322.html
419,Unprotected Primary Channel,284,Improper Access Control,https://cwe.mitre.org/data/definitions/419.html
420,Unprotected Alternate Channel,284,Improper Access Control,https://cwe.mitre.org/data/definitions/420.html
421,Race Condition During Access to Alternate Channel,284,Improper Access Control,https://cwe.mitre.org/data/definitions/421.html
422,Unprotected Windows Messaging Channel ('Shatter'),284,Improper Access Control,https://cwe.mitre.org/data/definitions/422.html
925,Improper Verification of Intent by Broadcast Receiver,284,Improper Access Control,https://cwe.mitre.org/data/definitions/925.html
940,Improper Verification of Source of a Communication Channel,284,Improper Access Control,https://cwe.mitre.org/data/definitions/940.html
941,Incorrectly Specified Destination in a Communication Channel,284,Improper Access Control,https://cwe.mitre.org/data/definitions/941.html
942,Permissive Cross-domain Policy with Untrusted Domains,284,Improper Access Control,https://cwe.mitre.org/data/definitions/942.html
435,Improper Interaction Between Multiple Correctly-Behaving Entities,,,https://cwe.mitre.org/data/definitions/435.html
1038,Insecure Automated Optimizations,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/1038.html
1037,Processor Optimization Removal or Modification of Security-critical Code,1038,Insecure Automated Optimizations,https://cwe.mitre.org/data/definitions/1037.html
733,Compiler Optimization Removal or Modification of Security-critical Code,1038,Insecure Automated Optimizations,https://cwe.mitre.org/data/definitions/733.html
14,Compiler Removal of Code to Clear Buffers,733,Compiler Optimization Removal or Modification of Security-critical Code,https://cwe.mitre.org/data/definitions/14.html
14,Compiler Removal of Code to Clear Buffers,1038,Insecure Automated Optimizations,https://cwe.mitre.org/data/definitions/14.html
1037,Processor Optimization Removal or Modification of Security-critical Code,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/1037.html
733,Compiler Optimization Removal or Modification of Security-critical Code,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/733.html
14,Compiler Removal of Code to Clear Buffers,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/14.html
188,Reliance on Data/Memory Layout,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/188.html
198,Use of Incorrect Byte Ordering,188,Reliance on Data/Memory Layout,https://cwe.mitre.org/data/definitions/198.html
198,Use of Incorrect Byte Ordering,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/198.html
436,Interpretation Conflict,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/436.html
115,Misinterpretation of Input,436,Interpretation Conflict,https://cwe.mitre.org/data/definitions/115.html
437,Incomplete Model of Endpoint Features,436,Interpretation Conflict,https://cwe.mitre.org/data/definitions/437.html
444,Inconsistent Interpretation of HTTP Requests ('HTTP Request Smuggling'),436,Interpretation Conflict,https://cwe.mitre.org/data/definitions/444.html
626,Null Byte Interaction Error (Poison Null Byte),436,Interpretation Conflict,https://cwe.mitre.org/data/definitions/626.html
650,Trusting HTTP Permission Methods on the Server Side,436,Interpretation Conflict,https://cwe.mitre.org/data/definitions/650.html
86,Improper Neutralization of Invalid Characters in Identifiers in Web Pages,436,Interpretation Conflict,https://cwe.mitre.org/data/definitions/86.html
115,Misinterpretation of Input,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/115.html
437,Incomplete Model of Endpoint Features,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/437.html
444,Inconsistent Interpretation of HTTP Requests ('HTTP Request Smuggling'),435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/444.html
626,Null Byte Interaction Error (Poison Null Byte),435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/626.html
650,Trusting HTTP Permission Methods on the Server Side,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/650.html
86,Improper Neutralization of Invalid Characters in Identifiers in Web Pages,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/86.html
439,Behavioral Change in New Version or Environment,435,Improper Interaction Between Multiple Correctly-Behaving Entities,https://cwe.mitre.org/data/definitions/439.html
664,Improper Control of a Resource Through its Lifetime,,,https://cwe.mitre.org/data/definitions/664.html
118,Incorrect Access of Indexable Resource ('Range Error'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/118.html
119,Improper Restriction of Operations within the Bounds of a Memory Buffer,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/119.html
120,Buffer Copy without Checking Size of Input ('Classic Buffer Overflow'),119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/120.html
785,Use of Path Manipulation Function without Maximum-sized Buffer,120,Buffer Copy without Checking Size of Input ('Classic Buffer Overflow'),https://cwe.mitre.org/data/definitions/785.html
785,Use of Path Manipulation Function without Maximum-sized Buffer,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/785.html
125,Out-of-bounds Read,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/125.html
126,Buffer Over-read,125,Out-of-bounds Read,https://cwe.mitre.org/data/definitions/126.html
127,Buffer Under-read,125,Out-of-bounds Read,https://cwe.mitre.org/data/definitions/127.html
126,Buffer Over-read,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/126.html
127,Buffer Under-read,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/127.html
466,Return of Pointer Value Outside of Expected Range,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/466.html
680,Integer Overflow to Buffer Overflow,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/680.html
786,Access of Memory Location Before Start of Buffer,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/786.html
124,Buffer Underwrite ('Buffer Underflow'),786,Access of Memory Location Before Start of Buffer,https://cwe.mitre.org/data/definitions/124.html
127,Buffer Under-read,786,Access of Memory Location Before Start of Buffer,https://cwe.mitre.org/data/definitions/127.html
124,Buffer Underwrite ('Buffer Underflow'),119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/124.html
787,Out-of-bounds Write,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/787.html
121,Stack-based Buffer Overflow,787,Out-of-bounds Write,https://cwe.mitre.org/data/definitions/121.html
122,Heap-based Buffer Overflow,787,Out-of-bounds Write,https://cwe.mitre.org/data/definitions/122.html
123,Write-what-where Condition,787,Out-of-bounds Write,https://cwe.mitre.org/data/definitions/123.html
124,Buffer Underwrite ('Buffer Underflow'),787,Out-of-bounds Write,https://cwe.mitre.org/data/definitions/124.html
121,Stack-based Buffer Overflow,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/121.html
122,Heap-based Buffer Overflow,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/122.html
123,Write-what-where Condition,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/123.html
788,Access of Memory Location After End of Buffer,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/788.html
121,Stack-based Buffer Overflow,788,Access of Memory Location After End of Buffer,https://cwe.mitre.org/data/definitions/121.html
122,Heap-based Buffer Overflow,788,Access of Memory Location After End of Buffer,https://cwe.mitre.org/data/definitions/122.html
126,Buffer Over-read,788,Access of Memory Location After End of Buffer,https://cwe.mitre.org/data/definitions/126.html
805,Buffer Access with Incorrect Length Value,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/805.html
806,Buffer Access Using Size of Source Buffer,805,Buffer Access with Incorrect Length Value,https://cwe.mitre.org/data/definitions/806.html
806,Buffer Access Using Size of Source Buffer,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/806.html
822,Untrusted Pointer Dereference,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/822.html
823,Use of Out-of-range Pointer Offset,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/823.html
824,Access of Uninitialized Pointer,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/824.html
825,Expired Pointer Dereference,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/825.html
415,Double Free,825,Expired Pointer Dereference,https://cwe.mitre.org/data/definitions/415.html
416,Use After Free,825,Expired Pointer Dereference,https://cwe.mitre.org/data/definitions/416.html
415,Double Free,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/415.html
416,Use After Free,119,Improper Restriction of Operations within the Bounds of a Memory Buffer,https://cwe.mitre.org/data/definitions/416.html
120,Buffer Copy without Checking Size of Input ('Classic Buffer Overflow'),118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/120.html
785,Use of Path Manipulation Function without Maximum-sized Buffer,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/785.html
125,Out-of-bounds Read,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/125.html
126,Buffer Over-read,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/126.html
127,Buffer Under-read,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/127.html
466,Return of Pointer Value Outside of Expected Range,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/466.html
680,Integer Overflow to Buffer Overflow,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/680.html
786,Access of Memory Location Before Start of Buffer,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/786.html
124,Buffer Underwrite ('Buffer Underflow'),118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/124.html
787,Out-of-bounds Write,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/787.html
121,Stack-based Buffer Overflow,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/121.html
122,Heap-based Buffer Overflow,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/122.html
123,Write-what-where Condition,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/123.html
788,Access of Memory Location After End of Buffer,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/788.html
805,Buffer Access with Incorrect Length Value,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/805.html
806,Buffer Access Using Size of Source Buffer,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/806.html
822,Untrusted Pointer Dereference,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/822.html
823,Use of Out-of-range Pointer Offset,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/823.html
824,Access of Uninitialized Pointer,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/824.html
825,Expired Pointer Dereference,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/825.html
415,Double Free,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/415.html
416,Use After Free,118,Incorrect Access of Indexable Resource ('Range Error'),https://cwe.mitre.org/data/definitions/416.html
119,Improper Restriction of Operations within the Bounds of a Memory Buffer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/119.html
120,Buffer Copy without Checking Size of Input ('Classic Buffer Overflow'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/120.html
785,Use of Path Manipulation Function without Maximum-sized Buffer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/785.html
125,Out-of-bounds Read,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/125.html
126,Buffer Over-read,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/126.html
127,Buffer Under-read,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/127.html
466,Return of Pointer Value Outside of Expected Range,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/466.html
680,Integer Overflow to Buffer Overflow,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/680.html
786,Access of Memory Location Before Start of Buffer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/786.html
124,Buffer Underwrite ('Buffer Underflow'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/124.html
787,Out-of-bounds Write,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/787.html
121,Stack-based Buffer Overflow,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/121.html
122,Heap-based Buffer Overflow,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/122.html
123,Write-what-where Condition,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/123.html
788,Access of Memory Location After End of Buffer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/788.html
805,Buffer Access with Incorrect Length Value,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/805.html
806,Buffer Access Using Size of Source Buffer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/806.html
822,Untrusted Pointer Dereference,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/822.html
823,Use of Out-of-range Pointer Offset,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/823.html
824,Access of Uninitialized Pointer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/824.html
825,Expired Pointer Dereference,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/825.html
415,Double Free,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/415.html
416,Use After Free,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/416.html
1229,Creation of Emergent Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1229.html
514,Covert Channel,1229,Creation of Emergent Resource,https://cwe.mitre.org/data/definitions/514.html
385,Covert Timing Channel,514,Covert Channel,https://cwe.mitre.org/data/definitions/385.html
515,Covert Storage Channel,514,Covert Channel,https://cwe.mitre.org/data/definitions/515.html
385,Covert Timing Channel,1229,Creation of Emergent Resource,https://cwe.mitre.org/data/definitions/385.html
515,Covert Storage Channel,1229,Creation of Emergent Resource,https://cwe.mitre.org/data/definitions/515.html
514,Covert Channel,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/514.html
385,Covert Timing Channel,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/385.html
515,Covert Storage Channel,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/515.html
1246,Improper Write Handling in Limited-write Non-Volatile Memories,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1246.html
1250,Improper Preservation of Consistency Between Independent Representations of Shared State,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1250.html
1249,Application-Level Admin Tool with Inconsistent View of Underlying Operating System,1250,Improper Preservation of Consistency Between Independent Representations of Shared State,https://cwe.mitre.org/data/definitions/1249.html
1251,Mirrored Regions with Different Values,1250,Improper Preservation of Consistency Between Independent Representations of Shared State,https://cwe.mitre.org/data/definitions/1251.html
1249,Application-Level Admin Tool with Inconsistent View of Underlying Operating System,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1249.html
1251,Mirrored Regions with Different Values,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1251.html
1329,Reliance on Component That is Not Updateable,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1329.html
1277,Firmware Not Updateable,1329,Reliance on Component That is Not Updateable,https://cwe.mitre.org/data/definitions/1277.html
1310,Missing Ability to Patch ROM Code,1329,Reliance on Component That is Not Updateable,https://cwe.mitre.org/data/definitions/1310.html
1277,Firmware Not Updateable,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1277.html
1310,Missing Ability to Patch ROM Code,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1310.html
221,Information Loss or Omission,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/221.html
222,Truncation of Security-relevant Information,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/222.html
223,Omission of Security-relevant Information,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/223.html
778,Insufficient Logging,223,Omission of Security-relevant Information,https://cwe.mitre.org/data/definitions/778.html
778,Insufficient Logging,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/778.html
224,Obscured Security-relevant Information by Alternate Name,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/224.html
356,Product UI does not Warn User of Unsafe Actions,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/356.html
396,Declaration of Catch for Generic Exception,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/396.html
397,Declaration of Throws for Generic Exception,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/397.html
451,User Interface (UI) Misrepresentation of Critical Information,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/451.html
1007,Insufficient Visual Distinction of Homoglyphs Presented to User,451,User Interface (UI) Misrepresentation of Critical Information,https://cwe.mitre.org/data/definitions/1007.html
1021,Improper Restriction of Rendered UI Layers or Frames,451,User Interface (UI) Misrepresentation of Critical Information,https://cwe.mitre.org/data/definitions/1021.html
1007,Insufficient Visual Distinction of Homoglyphs Presented to User,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/1007.html
1021,Improper Restriction of Rendered UI Layers or Frames,221,Information Loss or Omission,https://cwe.mitre.org/data/definitions/1021.html
222,Truncation of Security-relevant Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/222.html
223,Omission of Security-relevant Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/223.html
778,Insufficient Logging,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/778.html
224,Obscured Security-relevant Information by Alternate Name,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/224.html
356,Product UI does not Warn User of Unsafe Actions,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/356.html
396,Declaration of Catch for Generic Exception,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/396.html
397,Declaration of Throws for Generic Exception,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/397.html
451,User Interface (UI) Misrepresentation of Critical Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/451.html
1007,Insufficient Visual Distinction of Homoglyphs Presented to User,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1007.html
1021,Improper Restriction of Rendered UI Layers or Frames,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1021.html
372,Incomplete Internal State Distinction,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/372.html
400,Uncontrolled Resource Consumption,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/400.html
1235,Incorrect Use of Autoboxing and Unboxing for Performance Critical Operations,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/1235.html
770,Allocation of Resources Without Limits or Throttling,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/770.html
1325,Improperly Controlled Sequential Memory Allocation,770,Allocation of Resources Without Limits or Throttling,https://cwe.mitre.org/data/definitions/1325.html
774,Allocation of File Descriptors or Handles Without Limits or Throttling,770,Allocation of Resources Without Limits or Throttling,https://cwe.mitre.org/data/definitions/774.html
789,Memory Allocation with Excessive Size Value,770,Allocation of Resources Without Limits or Throttling,https://cwe.mitre.org/data/definitions/789.html
1325,Improperly Controlled Sequential Memory Allocation,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/1325.html
774,Allocation of File Descriptors or Handles Without Limits or Throttling,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/774.html
789,Memory Allocation with Excessive Size Value,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/789.html
771,Missing Reference to Active Allocated Resource,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/771.html
773,Missing Reference to Active File Descriptor or Handle,771,Missing Reference to Active Allocated Resource,https://cwe.mitre.org/data/definitions/773.html
773,Missing Reference to Active File Descriptor or Handle,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/773.html
779,Logging of Excessive Data,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/779.html
920,Improper Restriction of Power Consumption,400,Uncontrolled Resource Consumption,https://cwe.mitre.org/data/definitions/920.html
1235,Incorrect Use of Autoboxing and Unboxing for Performance Critical Operations,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1235.html
770,Allocation of Resources Without Limits or Throttling,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/770.html
1325,Improperly Controlled Sequential Memory Allocation,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1325.html
774,Allocation of File Descriptors or Handles Without Limits or Throttling,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/774.html
789,Memory Allocation with Excessive Size Value,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/789.html
771,Missing Reference to Active Allocated Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/771.html
773,Missing Reference to Active File Descriptor or Handle,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/773.html
779,Logging of Excessive Data,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/779.html
920,Improper Restriction of Power Consumption,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/920.html
404,Improper Resource Shutdown or Release,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/404.html
1266,Improper Scrubbing of Sensitive Data from Decommissioned Device,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/1266.html
262,Not Using Password Aging,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/262.html
263,Password Aging with Long Expiration,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/263.html
299,Improper Check for Certificate Revocation,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/299.html
370,Missing Check for Certificate Revocation after Initial Check,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/370.html
459,Incomplete Cleanup,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/459.html
226,Sensitive Information in Resource Not Removed Before Reuse,459,Incomplete Cleanup,https://cwe.mitre.org/data/definitions/226.html
1239,Improper Zeroization of Hardware Register,226,Sensitive Information in Resource Not Removed Before Reuse,https://cwe.mitre.org/data/definitions/1239.html
1272,Sensitive Information Uncleared Before Debug/Power State Transition,226,Sensitive Information in Resource Not Removed Before Reuse,https://cwe.mitre.org/data/definitions/1272.html
1301,Insufficient or Incomplete Data Removal within Hardware Component,226,Sensitive Information in Resource Not Removed Before Reuse,https://cwe.mitre.org/data/definitions/1301.html
1330,Remanent Data Readable after Memory Erase,1301,Insufficient or Incomplete Data Removal within Hardware Component,https://cwe.mitre.org/data/definitions/1330.html
1330,Remanent Data Readable after Memory Erase,226,Sensitive Information in Resource Not Removed Before Reuse,https://cwe.mitre.org/data/definitions/1330.html
244,Improper Clearing of Heap Memory Before Release ('Heap Inspection'),226,Sensitive Information in Resource Not Removed Before Reuse,https://cwe.mitre.org/data/definitions/244.html
1239,Improper Zeroization of Hardware Register,459,Incomplete Cleanup,https://cwe.mitre.org/data/definitions/1239.html
1272,Sensitive Information Uncleared Before Debug/Power State Transition,459,Incomplete Cleanup,https://cwe.mitre.org/data/definitions/1272.html
1301,Insufficient or Incomplete Data Removal within Hardware Component,459,Incomplete Cleanup,https://cwe.mitre.org/data/definitions/1301.html
1330,Remanent Data Readable after Memory Erase,459,Incomplete Cleanup,https://cwe.mitre.org/data/definitions/1330.html
244,Improper Clearing of Heap Memory Before Release ('Heap Inspection'),459,Incomplete Cleanup,https://cwe.mitre.org/data/definitions/244.html
460,Improper Cleanup on Thrown Exception,459,Incomplete Cleanup,https://cwe.mitre.org/data/definitions/460.html
568,finalize() Method Without super.finalize(),459,Incomplete Cleanup,https://cwe.mitre.org/data/definitions/568.html
226,Sensitive Information in Resource Not Removed Before Reuse,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/226.html
1239,Improper Zeroization of Hardware Register,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/1239.html
1272,Sensitive Information Uncleared Before Debug/Power State Transition,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/1272.html
1301,Insufficient or Incomplete Data Removal within Hardware Component,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/1301.html
1330,Remanent Data Readable after Memory Erase,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/1330.html
244,Improper Clearing of Heap Memory Before Release ('Heap Inspection'),404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/244.html
460,Improper Cleanup on Thrown Exception,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/460.html
568,finalize() Method Without super.finalize(),404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/568.html
763,Release of Invalid Pointer or Reference,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/763.html
761,Free of Pointer not at Start of Buffer,763,Release of Invalid Pointer or Reference,https://cwe.mitre.org/data/definitions/761.html
762,Mismatched Memory Management Routines,763,Release of Invalid Pointer or Reference,https://cwe.mitre.org/data/definitions/762.html
590,Free of Memory not on the Heap,762,Mismatched Memory Management Routines,https://cwe.mitre.org/data/definitions/590.html
590,Free of Memory not on the Heap,763,Release of Invalid Pointer or Reference,https://cwe.mitre.org/data/definitions/590.html
761,Free of Pointer not at Start of Buffer,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/761.html
762,Mismatched Memory Management Routines,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/762.html
590,Free of Memory not on the Heap,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/590.html
772,Missing Release of Resource after Effective Lifetime,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/772.html
1091,Use of Object without Invoking Destructor Method,772,Missing Release of Resource after Effective Lifetime,https://cwe.mitre.org/data/definitions/1091.html
401,Missing Release of Memory after Effective Lifetime,772,Missing Release of Resource after Effective Lifetime,https://cwe.mitre.org/data/definitions/401.html
775,Missing Release of File Descriptor or Handle after Effective Lifetime,772,Missing Release of Resource after Effective Lifetime,https://cwe.mitre.org/data/definitions/775.html
1091,Use of Object without Invoking Destructor Method,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/1091.html
401,Missing Release of Memory after Effective Lifetime,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/401.html
775,Missing Release of File Descriptor or Handle after Effective Lifetime,404,Improper Resource Shutdown or Release,https://cwe.mitre.org/data/definitions/775.html
1266,Improper Scrubbing of Sensitive Data from Decommissioned Device,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1266.html
262,Not Using Password Aging,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/262.html
263,Password Aging with Long Expiration,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/263.html
299,Improper Check for Certificate Revocation,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/299.html
370,Missing Check for Certificate Revocation after Initial Check,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/370.html
459,Incomplete Cleanup,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/459.html
226,Sensitive Information in Resource Not Removed Before Reuse,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/226.html
1239,Improper Zeroization of Hardware Register,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1239.html
1272,Sensitive Information Uncleared Before Debug/Power State Transition,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1272.html
1301,Insufficient or Incomplete Data Removal within Hardware Component,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1301.html
1330,Remanent Data Readable after Memory Erase,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1330.html
244,Improper Clearing of Heap Memory Before Release ('Heap Inspection'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/244.html
460,Improper Cleanup on Thrown Exception,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/460.html
568,finalize() Method Without super.finalize(),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/568.html
763,Release of Invalid Pointer or Reference,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/763.html
761,Free of Pointer not at Start of Buffer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/761.html
762,Mismatched Memory Management Routines,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/762.html
590,Free of Memory not on the Heap,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/590.html
772,Missing Release of Resource after Effective Lifetime,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/772.html
1091,Use of Object without Invoking Destructor Method,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1091.html
401,Missing Release of Memory after Effective Lifetime,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/401.html
775,Missing Release of File Descriptor or Handle after Effective Lifetime,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/775.html
405,Asymmetric Resource Consumption (Amplification),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/405.html
1050,Excessive Platform Resource Consumption within a Loop,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1050.html
1072,Data Resource Access without Use of Connection Pooling,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1072.html
1073,Non-SQL Invokable Control Element with Excessive Number of Data Resource Accesses,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1073.html
1084,Invokable Control Element with Excessive File or Data Access Operations,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1084.html
1089,Large Data Table with Excessive Number of Indices,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1089.html
1094,Excessive Index Range Scan for a Data Resource,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1094.html
1176,Inefficient CPU Computation,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1176.html
1042,Static Member Data Element outside of a Singleton Class Element,1176,Inefficient CPU Computation,https://cwe.mitre.org/data/definitions/1042.html
1046,Creation of Immutable Text Using String Concatenation,1176,Inefficient CPU Computation,https://cwe.mitre.org/data/definitions/1046.html
1049,Excessive Data Query Operations in a Large Data Table,1176,Inefficient CPU Computation,https://cwe.mitre.org/data/definitions/1049.html
1063,Creation of Class Instance within a Static Code Block,1176,Inefficient CPU Computation,https://cwe.mitre.org/data/definitions/1063.html
1067,Excessive Execution of Sequential Searches of Data Resource,1176,Inefficient CPU Computation,https://cwe.mitre.org/data/definitions/1067.html
1042,Static Member Data Element outside of a Singleton Class Element,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1042.html
1046,Creation of Immutable Text Using String Concatenation,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1046.html
1049,Excessive Data Query Operations in a Large Data Table,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1049.html
1063,Creation of Class Instance within a Static Code Block,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1063.html
1067,Excessive Execution of Sequential Searches of Data Resource,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1067.html
406,Insufficient Control of Network Message Volume (Network Amplification),405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/406.html
407,Inefficient Algorithmic Complexity,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/407.html
1333,Inefficient Regular Expression Complexity,407,Inefficient Algorithmic Complexity,https://cwe.mitre.org/data/definitions/1333.html
1333,Inefficient Regular Expression Complexity,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/1333.html
408,Incorrect Behavior Order: Early Amplification,405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/408.html
409,Improper Handling of Highly Compressed Data (Data Amplification),405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/409.html
776,Improper Restriction of Recursive Entity References in DTDs ('XML Entity Expansion'),409,Improper Handling of Highly Compressed Data (Data Amplification),https://cwe.mitre.org/data/definitions/776.html
776,Improper Restriction of Recursive Entity References in DTDs ('XML Entity Expansion'),405,Asymmetric Resource Consumption (Amplification),https://cwe.mitre.org/data/definitions/776.html
1050,Excessive Platform Resource Consumption within a Loop,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1050.html
1072,Data Resource Access without Use of Connection Pooling,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1072.html
1073,Non-SQL Invokable Control Element with Excessive Number of Data Resource Accesses,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1073.html
1084,Invokable Control Element with Excessive File or Data Access Operations,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1084.html
1089,Large Data Table with Excessive Number of Indices,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1089.html
1094,Excessive Index Range Scan for a Data Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1094.html
1176,Inefficient CPU Computation,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1176.html
1042,Static Member Data Element outside of a Singleton Class Element,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1042.html
1046,Creation of Immutable Text Using String Concatenation,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1046.html
1049,Excessive Data Query Operations in a Large Data Table,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1049.html
1063,Creation of Class Instance within a Static Code Block,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1063.html
1067,Excessive Execution of Sequential Searches of Data Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1067.html
406,Insufficient Control of Network Message Volume (Network Amplification),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/406.html
407,Inefficient Algorithmic Complexity,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/407.html
1333,Inefficient Regular Expression Complexity,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1333.html
408,Incorrect Behavior Order: Early Amplification,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/408.html
409,Improper Handling of Highly Compressed Data (Data Amplification),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/409.html
776,Improper Restriction of Recursive Entity References in DTDs ('XML Entity Expansion'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/776.html
410,Insufficient Resource Pool,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/410.html
471,Modification of Assumed-Immutable Data (MAID),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/471.html
291,Reliance on IP Address for Authentication,471,Modification of Assumed-Immutable Data (MAID),https://cwe.mitre.org/data/definitions/291.html
472,External Control of Assumed-Immutable Web Parameter,471,Modification of Assumed-Immutable Data (MAID),https://cwe.mitre.org/data/definitions/472.html
473,PHP External Variable Modification,471,Modification of Assumed-Immutable Data (MAID),https://cwe.mitre.org/data/definitions/473.html
607,Public Static Final Field References Mutable Object,471,Modification of Assumed-Immutable Data (MAID),https://cwe.mitre.org/data/definitions/607.html
291,Reliance on IP Address for Authentication,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/291.html
472,External Control of Assumed-Immutable Web Parameter,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/472.html
473,PHP External Variable Modification,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/473.html
607,Public Static Final Field References Mutable Object,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/607.html
487,Reliance on Package-level Scope,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/487.html
495,Private Data Structure Returned From A Public Method,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/495.html
496,Public Data Assigned to Private Array-Typed Field,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/496.html
501,Trust Boundary Violation,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/501.html
580,clone() Method Without super.clone(),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/580.html
610,Externally Controlled Reference to a Resource in Another Sphere,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/610.html
15,External Control of System or Configuration Setting,610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/15.html
384,Session Fixation,610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/384.html
441,Unintended Proxy or Intermediary ('Confused Deputy'),610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/441.html
1021,Improper Restriction of Rendered UI Layers or Frames,441,Unintended Proxy or Intermediary ('Confused Deputy'),https://cwe.mitre.org/data/definitions/1021.html
918,Server-Side Request Forgery (SSRF),441,Unintended Proxy or Intermediary ('Confused Deputy'),https://cwe.mitre.org/data/definitions/918.html
1021,Improper Restriction of Rendered UI Layers or Frames,610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/1021.html
918,Server-Side Request Forgery (SSRF),610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/918.html
470,Use of Externally-Controlled Input to Select Classes or Code ('Unsafe Reflection'),610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/470.html
601,URL Redirection to Untrusted Site ('Open Redirect'),610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/601.html
611,Improper Restriction of XML External Entity Reference,610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/611.html
73,External Control of File Name or Path,610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/73.html
114,Process Control,73,External Control of File Name or Path,https://cwe.mitre.org/data/definitions/114.html
114,Process Control,610,Externally Controlled Reference to a Resource in Another Sphere,https://cwe.mitre.org/data/definitions/114.html
15,External Control of System or Configuration Setting,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/15.html
384,Session Fixation,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/384.html
441,Unintended Proxy or Intermediary ('Confused Deputy'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/441.html
918,Server-Side Request Forgery (SSRF),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/918.html
470,Use of Externally-Controlled Input to Select Classes or Code ('Unsafe Reflection'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/470.html
601,URL Redirection to Untrusted Site ('Open Redirect'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/601.html
611,Improper Restriction of XML External Entity Reference,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/611.html
73,External Control of File Name or Path,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/73.html
114,Process Control,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/114.html
662,Improper Synchronization,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/662.html
1058,Invokable Control Element in Multi-Thread Context with non-Final Static Storable or Member Element,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/1058.html
663,Use of a Non-reentrant Function in a Concurrent Context,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/663.html
479,Signal Handler Use of a Non-reentrant Function,663,Use of a Non-reentrant Function in a Concurrent Context,https://cwe.mitre.org/data/definitions/479.html
558,Use of getlogin() in Multithreaded Application,663,Use of a Non-reentrant Function in a Concurrent Context,https://cwe.mitre.org/data/definitions/558.html
479,Signal Handler Use of a Non-reentrant Function,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/479.html
558,Use of getlogin() in Multithreaded Application,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/558.html
667,Improper Locking,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/667.html
1232,Improper Lock Behavior After Power State Transition,667,Improper Locking,https://cwe.mitre.org/data/definitions/1232.html
1233,Improper Hardware Lock Protection for Security Sensitive Controls,667,Improper Locking,https://cwe.mitre.org/data/definitions/1233.html
1234,Hardware Internal or Debug Modes Allow Override of Locks,667,Improper Locking,https://cwe.mitre.org/data/definitions/1234.html
412,Unrestricted Externally Accessible Lock,667,Improper Locking,https://cwe.mitre.org/data/definitions/412.html
413,Improper Resource Locking,667,Improper Locking,https://cwe.mitre.org/data/definitions/413.html
591,Sensitive Data Storage in Improperly Locked Memory,413,Improper Resource Locking,https://cwe.mitre.org/data/definitions/591.html
591,Sensitive Data Storage in Improperly Locked Memory,667,Improper Locking,https://cwe.mitre.org/data/definitions/591.html
414,Missing Lock Check,667,Improper Locking,https://cwe.mitre.org/data/definitions/414.html
609,Double-Checked Locking,667,Improper Locking,https://cwe.mitre.org/data/definitions/609.html
764,Multiple Locks of a Critical Resource,667,Improper Locking,https://cwe.mitre.org/data/definitions/764.html
765,Multiple Unlocks of a Critical Resource,667,Improper Locking,https://cwe.mitre.org/data/definitions/765.html
832,Unlock of a Resource that is not Locked,667,Improper Locking,https://cwe.mitre.org/data/definitions/832.html
833,Deadlock,667,Improper Locking,https://cwe.mitre.org/data/definitions/833.html
1232,Improper Lock Behavior After Power State Transition,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/1232.html
1233,Improper Hardware Lock Protection for Security Sensitive Controls,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/1233.html
1234,Hardware Internal or Debug Modes Allow Override of Locks,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/1234.html
412,Unrestricted Externally Accessible Lock,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/412.html
413,Improper Resource Locking,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/413.html
591,Sensitive Data Storage in Improperly Locked Memory,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/591.html
414,Missing Lock Check,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/414.html
609,Double-Checked Locking,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/609.html
764,Multiple Locks of a Critical Resource,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/764.html
765,Multiple Unlocks of a Critical Resource,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/765.html
832,Unlock of a Resource that is not Locked,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/832.html
833,Deadlock,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/833.html
820,Missing Synchronization,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/820.html
1096,Singleton Class Instance Creation without Proper Locking or Synchronization,820,Missing Synchronization,https://cwe.mitre.org/data/definitions/1096.html
543,Use of Singleton Pattern Without Synchronization in a Multithreaded Context,820,Missing Synchronization,https://cwe.mitre.org/data/definitions/543.html
567,Unsynchronized Access to Shared Data in a Multithreaded Context,820,Missing Synchronization,https://cwe.mitre.org/data/definitions/567.html
1096,Singleton Class Instance Creation without Proper Locking or Synchronization,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/1096.html
543,Use of Singleton Pattern Without Synchronization in a Multithreaded Context,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/543.html
567,Unsynchronized Access to Shared Data in a Multithreaded Context,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/567.html
821,Incorrect Synchronization,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/821.html
1088,Synchronous Access of Remote Resource without Timeout,821,Incorrect Synchronization,https://cwe.mitre.org/data/definitions/1088.html
1264,Hardware Logic with Insecure De-Synchronization between Control and Data Channels,821,Incorrect Synchronization,https://cwe.mitre.org/data/definitions/1264.html
572,Call to Thread run() instead of start(),821,Incorrect Synchronization,https://cwe.mitre.org/data/definitions/572.html
574,EJB Bad Practices: Use of Synchronization Primitives,821,Incorrect Synchronization,https://cwe.mitre.org/data/definitions/574.html
1088,Synchronous Access of Remote Resource without Timeout,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/1088.html
1264,Hardware Logic with Insecure De-Synchronization between Control and Data Channels,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/1264.html
572,Call to Thread run() instead of start(),662,Improper Synchronization,https://cwe.mitre.org/data/definitions/572.html
574,EJB Bad Practices: Use of Synchronization Primitives,662,Improper Synchronization,https://cwe.mitre.org/data/definitions/574.html
1058,Invokable Control Element in Multi-Thread Context with non-Final Static Storable or Member Element,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1058.html
663,Use of a Non-reentrant Function in a Concurrent Context,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/663.html
479,Signal Handler Use of a Non-reentrant Function,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/479.html
558,Use of getlogin() in Multithreaded Application,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/558.html
667,Improper Locking,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/667.html
1232,Improper Lock Behavior After Power State Transition,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1232.html
1233,Improper Hardware Lock Protection for Security Sensitive Controls,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1233.html
1234,Hardware Internal or Debug Modes Allow Override of Locks,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1234.html
412,Unrestricted Externally Accessible Lock,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/412.html
413,Improper Resource Locking,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/413.html
591,Sensitive Data Storage in Improperly Locked Memory,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/591.html
414,Missing Lock Check,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/414.html
609,Double-Checked Locking,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/609.html
764,Multiple Locks of a Critical Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/764.html
765,Multiple Unlocks of a Critical Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/765.html
832,Unlock of a Resource that is not Locked,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/832.html
833,Deadlock,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/833.html
820,Missing Synchronization,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/820.html
1096,Singleton Class Instance Creation without Proper Locking or Synchronization,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1096.html
543,Use of Singleton Pattern Without Synchronization in a Multithreaded Context,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/543.html
567,Unsynchronized Access to Shared Data in a Multithreaded Context,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/567.html
821,Incorrect Synchronization,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/821.html
1088,Synchronous Access of Remote Resource without Timeout,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1088.html
1264,Hardware Logic with Insecure De-Synchronization between Control and Data Channels,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1264.html
572,Call to Thread run() instead of start(),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/572.html
574,EJB Bad Practices: Use of Synchronization Primitives,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/574.html
665,Improper Initialization,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/665.html
1051,Initialization with Hard-Coded Network Resource Configuration Data,665,Improper Initialization,https://cwe.mitre.org/data/definitions/1051.html
1052,Excessive Use of Hard-Coded Literals in Initialization,665,Improper Initialization,https://cwe.mitre.org/data/definitions/1052.html
1188,Insecure Default Initialization of Resource,665,Improper Initialization,https://cwe.mitre.org/data/definitions/1188.html
453,Insecure Default Variable Initialization,1188,Insecure Default Initialization of Resource,https://cwe.mitre.org/data/definitions/453.html
453,Insecure Default Variable Initialization,665,Improper Initialization,https://cwe.mitre.org/data/definitions/453.html
1221,Incorrect Register Defaults or Module Parameters,665,Improper Initialization,https://cwe.mitre.org/data/definitions/1221.html
1271,Uninitialized Value on Reset for Registers Holding Security Settings,665,Improper Initialization,https://cwe.mitre.org/data/definitions/1271.html
1279,Cryptographic Operations are run Before Supporting Units are Ready,665,Improper Initialization,https://cwe.mitre.org/data/definitions/1279.html
454,External Initialization of Trusted Variables or Data Stores,665,Improper Initialization,https://cwe.mitre.org/data/definitions/454.html
455,Non-exit on Failed Initialization,665,Improper Initialization,https://cwe.mitre.org/data/definitions/455.html
770,Allocation of Resources Without Limits or Throttling,665,Improper Initialization,https://cwe.mitre.org/data/definitions/770.html
1325,Improperly Controlled Sequential Memory Allocation,665,Improper Initialization,https://cwe.mitre.org/data/definitions/1325.html
774,Allocation of File Descriptors or Handles Without Limits or Throttling,665,Improper Initialization,https://cwe.mitre.org/data/definitions/774.html
789,Memory Allocation with Excessive Size Value,665,Improper Initialization,https://cwe.mitre.org/data/definitions/789.html
908,Use of Uninitialized Resource,665,Improper Initialization,https://cwe.mitre.org/data/definitions/908.html
457,Use of Uninitialized Variable,908,Use of Uninitialized Resource,https://cwe.mitre.org/data/definitions/457.html
457,Use of Uninitialized Variable,665,Improper Initialization,https://cwe.mitre.org/data/definitions/457.html
909,Missing Initialization of Resource,665,Improper Initialization,https://cwe.mitre.org/data/definitions/909.html
456,Missing Initialization of a Variable,909,Missing Initialization of Resource,https://cwe.mitre.org/data/definitions/456.html
456,Missing Initialization of a Variable,665,Improper Initialization,https://cwe.mitre.org/data/definitions/456.html
1051,Initialization with Hard-Coded Network Resource Configuration Data,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1051.html
1052,Excessive Use of Hard-Coded Literals in Initialization,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1052.html
1188,Insecure Default Initialization of Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1188.html
453,Insecure Default Variable Initialization,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/453.html
1221,Incorrect Register Defaults or Module Parameters,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1221.html
1271,Uninitialized Value on Reset for Registers Holding Security Settings,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1271.html
1279,Cryptographic Operations are run Before Supporting Units are Ready,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1279.html
454,External Initialization of Trusted Variables or Data Stores,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/454.html
455,Non-exit on Failed Initialization,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/455.html
908,Use of Uninitialized Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/908.html
457,Use of Uninitialized Variable,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/457.html
909,Missing Initialization of Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/909.html
456,Missing Initialization of a Variable,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/456.html
666,Operation on Resource in Wrong Phase of Lifetime,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/666.html
415,Double Free,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/415.html
593,Authentication Bypass: OpenSSL CTX Object Modified after SSL Objects are Created,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/593.html
605,Multiple Binds to the Same Port,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/605.html
672,Operation on a Resource after Expiration or Release,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/672.html
298,Improper Validation of Certificate Expiration,672,Operation on a Resource after Expiration or Release,https://cwe.mitre.org/data/definitions/298.html
324,Use of a Key Past its Expiration Date,672,Operation on a Resource after Expiration or Release,https://cwe.mitre.org/data/definitions/324.html
613,Insufficient Session Expiration,672,Operation on a Resource after Expiration or Release,https://cwe.mitre.org/data/definitions/613.html
825,Expired Pointer Dereference,672,Operation on a Resource after Expiration or Release,https://cwe.mitre.org/data/definitions/825.html
415,Double Free,672,Operation on a Resource after Expiration or Release,https://cwe.mitre.org/data/definitions/415.html
416,Use After Free,672,Operation on a Resource after Expiration or Release,https://cwe.mitre.org/data/definitions/416.html
910,Use of Expired File Descriptor,672,Operation on a Resource after Expiration or Release,https://cwe.mitre.org/data/definitions/910.html
298,Improper Validation of Certificate Expiration,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/298.html
324,Use of a Key Past its Expiration Date,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/324.html
613,Insufficient Session Expiration,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/613.html
825,Expired Pointer Dereference,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/825.html
416,Use After Free,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/416.html
910,Use of Expired File Descriptor,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/910.html
826,Premature Release of Resource During Expected Lifetime,666,Operation on Resource in Wrong Phase of Lifetime,https://cwe.mitre.org/data/definitions/826.html
593,Authentication Bypass: OpenSSL CTX Object Modified after SSL Objects are Created,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/593.html
605,Multiple Binds to the Same Port,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/605.html
672,Operation on a Resource after Expiration or Release,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/672.html
298,Improper Validation of Certificate Expiration,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/298.html
324,Use of a Key Past its Expiration Date,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/324.html
613,Insufficient Session Expiration,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/613.html
910,Use of Expired File Descriptor,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/910.html
826,Premature Release of Resource During Expected Lifetime,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/826.html
668,Exposure of Resource to Wrong Sphere,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/668.html
1189,Improper Isolation of Shared Resources on System-on-a-Chip (SoC),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1189.html
1303,Non-Transparent Sharing of Microarchitectural Resources,1189,Improper Isolation of Shared Resources on System-on-a-Chip (SoC),https://cwe.mitre.org/data/definitions/1303.html
1303,Non-Transparent Sharing of Microarchitectural Resources,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1303.html
1282,Assumed-Immutable Data is Stored in Writable Memory,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1282.html
1327,Binding to an Unrestricted IP Address,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1327.html
134,Use of Externally-Controlled Format String,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/134.html
200,Exposure of Sensitive Information to an Unauthorized Actor,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/200.html
1258,Exposure of Sensitive System Information Due to Uncleared Debug Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/1258.html
1273,Device Unlock Credential Sharing,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/1273.html
1295,Debug Messages Revealing Unnecessary Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/1295.html
201,Insertion of Sensitive Information Into Sent Data,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/201.html
598,Use of GET Request Method With Sensitive Query Strings,201,Insertion of Sensitive Information Into Sent Data,https://cwe.mitre.org/data/definitions/598.html
598,Use of GET Request Method With Sensitive Query Strings,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/598.html
203,Observable Discrepancy,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/203.html
1300,Improper Protection Against Physical Side Channels,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/1300.html
1303,Non-Transparent Sharing of Microarchitectural Resources,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/1303.html
204,Observable Response Discrepancy,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/204.html
205,Observable Behavioral Discrepancy,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/205.html
1255,Comparison Logic is Vulnerable to Power Side-Channel Attacks,205,Observable Behavioral Discrepancy,https://cwe.mitre.org/data/definitions/1255.html
206,Observable Internal Behavioral Discrepancy,205,Observable Behavioral Discrepancy,https://cwe.mitre.org/data/definitions/206.html
207,Observable Behavioral Discrepancy With Equivalent Products,205,Observable Behavioral Discrepancy,https://cwe.mitre.org/data/definitions/207.html
1255,Comparison Logic is Vulnerable to Power Side-Channel Attacks,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/1255.html
206,Observable Internal Behavioral Discrepancy,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/206.html
207,Observable Behavioral Discrepancy With Equivalent Products,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/207.html
208,Observable Timing Discrepancy,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/208.html
1254,Incorrect Comparison Logic Granularity,208,Observable Timing Discrepancy,https://cwe.mitre.org/data/definitions/1254.html
1254,Incorrect Comparison Logic Granularity,203,Observable Discrepancy,https://cwe.mitre.org/data/definitions/1254.html
1300,Improper Protection Against Physical Side Channels,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/1300.html
1303,Non-Transparent Sharing of Microarchitectural Resources,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/1303.html
204,Observable Response Discrepancy,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/204.html
205,Observable Behavioral Discrepancy,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/205.html
1255,Comparison Logic is Vulnerable to Power Side-Channel Attacks,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/1255.html
206,Observable Internal Behavioral Discrepancy,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/206.html
207,Observable Behavioral Discrepancy With Equivalent Products,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/207.html
208,Observable Timing Discrepancy,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/208.html
1254,Incorrect Comparison Logic Granularity,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/1254.html
209,Generation of Error Message Containing Sensitive Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/209.html
210,Self-generated Error Message Containing Sensitive Information,209,Generation of Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/210.html
211,Externally-Generated Error Message Containing Sensitive Information,209,Generation of Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/211.html
535,Exposure of Information Through Shell Error Message,211,Externally-Generated Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/535.html
536,Servlet Runtime Error Message Containing Sensitive Information,211,Externally-Generated Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/536.html
537,Java Runtime Error Message Containing Sensitive Information,211,Externally-Generated Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/537.html
535,Exposure of Information Through Shell Error Message,209,Generation of Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/535.html
536,Servlet Runtime Error Message Containing Sensitive Information,209,Generation of Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/536.html
537,Java Runtime Error Message Containing Sensitive Information,209,Generation of Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/537.html
550,Server-generated Error Message Containing Sensitive Information,209,Generation of Error Message Containing Sensitive Information,https://cwe.mitre.org/data/definitions/550.html
210,Self-generated Error Message Containing Sensitive Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/210.html
211,Externally-Generated Error Message Containing Sensitive Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/211.html
535,Exposure of Information Through Shell Error Message,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/535.html
536,Servlet Runtime Error Message Containing Sensitive Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/536.html
537,Java Runtime Error Message Containing Sensitive Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/537.html
550,Server-generated Error Message Containing Sensitive Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/550.html
213,Exposure of Sensitive Information Due to Incompatible Policies,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/213.html
215,Insertion of Sensitive Information Into Debugging Code,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/215.html
359,Exposure of Private Personal Information to an Unauthorized Actor,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/359.html
497,Exposure of Sensitive System Information to an Unauthorized Control Sphere,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/497.html
214,Invocation of Process Using Visible Sensitive Information,497,Exposure of Sensitive System Information to an Unauthorized Control Sphere,https://cwe.mitre.org/data/definitions/214.html
526,Exposure of Sensitive Information Through Environmental Variables,497,Exposure of Sensitive System Information to an Unauthorized Control Sphere,https://cwe.mitre.org/data/definitions/526.html
548,Exposure of Information Through Directory Listing,497,Exposure of Sensitive System Information to an Unauthorized Control Sphere,https://cwe.mitre.org/data/definitions/548.html
214,Invocation of Process Using Visible Sensitive Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/214.html
526,Exposure of Sensitive Information Through Environmental Variables,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/526.html
548,Exposure of Information Through Directory Listing,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/548.html
538,Insertion of Sensitive Information into Externally-Accessible File or Directory,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/538.html
532,Insertion of Sensitive Information into Log File,538,Insertion of Sensitive Information into Externally-Accessible File or Directory,https://cwe.mitre.org/data/definitions/532.html
540,Inclusion of Sensitive Information in Source Code,538,Insertion of Sensitive Information into Externally-Accessible File or Directory,https://cwe.mitre.org/data/definitions/540.html
531,Inclusion of Sensitive Information in Test Code,540,Inclusion of Sensitive Information in Source Code,https://cwe.mitre.org/data/definitions/531.html
541,Inclusion of Sensitive Information in an Include File,540,Inclusion of Sensitive Information in Source Code,https://cwe.mitre.org/data/definitions/541.html
615,Inclusion of Sensitive Information in Source Code Comments,540,Inclusion of Sensitive Information in Source Code,https://cwe.mitre.org/data/definitions/615.html
531,Inclusion of Sensitive Information in Test Code,538,Insertion of Sensitive Information into Externally-Accessible File or Directory,https://cwe.mitre.org/data/definitions/531.html
541,Inclusion of Sensitive Information in an Include File,538,Insertion of Sensitive Information into Externally-Accessible File or Directory,https://cwe.mitre.org/data/definitions/541.html
615,Inclusion of Sensitive Information in Source Code Comments,538,Insertion of Sensitive Information into Externally-Accessible File or Directory,https://cwe.mitre.org/data/definitions/615.html
651,Exposure of WSDL File Containing Sensitive Information,538,Insertion of Sensitive Information into Externally-Accessible File or Directory,https://cwe.mitre.org/data/definitions/651.html
532,Insertion of Sensitive Information into Log File,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/532.html
540,Inclusion of Sensitive Information in Source Code,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/540.html
531,Inclusion of Sensitive Information in Test Code,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/531.html
541,Inclusion of Sensitive Information in an Include File,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/541.html
615,Inclusion of Sensitive Information in Source Code Comments,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/615.html
651,Exposure of WSDL File Containing Sensitive Information,200,Exposure of Sensitive Information to an Unauthorized Actor,https://cwe.mitre.org/data/definitions/651.html
1258,Exposure of Sensitive System Information Due to Uncleared Debug Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1258.html
1273,Device Unlock Credential Sharing,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1273.html
1295,Debug Messages Revealing Unnecessary Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1295.html
201,Insertion of Sensitive Information Into Sent Data,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/201.html
598,Use of GET Request Method With Sensitive Query Strings,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/598.html
203,Observable Discrepancy,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/203.html
1300,Improper Protection Against Physical Side Channels,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1300.html
204,Observable Response Discrepancy,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/204.html
205,Observable Behavioral Discrepancy,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/205.html
1255,Comparison Logic is Vulnerable to Power Side-Channel Attacks,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1255.html
206,Observable Internal Behavioral Discrepancy,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/206.html
207,Observable Behavioral Discrepancy With Equivalent Products,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/207.html
208,Observable Timing Discrepancy,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/208.html
1254,Incorrect Comparison Logic Granularity,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1254.html
209,Generation of Error Message Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/209.html
210,Self-generated Error Message Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/210.html
211,Externally-Generated Error Message Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/211.html
535,Exposure of Information Through Shell Error Message,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/535.html
536,Servlet Runtime Error Message Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/536.html
537,Java Runtime Error Message Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/537.html
550,Server-generated Error Message Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/550.html
213,Exposure of Sensitive Information Due to Incompatible Policies,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/213.html
215,Insertion of Sensitive Information Into Debugging Code,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/215.html
359,Exposure of Private Personal Information to an Unauthorized Actor,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/359.html
497,Exposure of Sensitive System Information to an Unauthorized Control Sphere,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/497.html
214,Invocation of Process Using Visible Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/214.html
526,Exposure of Sensitive Information Through Environmental Variables,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/526.html
548,Exposure of Information Through Directory Listing,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/548.html
538,Insertion of Sensitive Information into Externally-Accessible File or Directory,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/538.html
532,Insertion of Sensitive Information into Log File,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/532.html
540,Inclusion of Sensitive Information in Source Code,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/540.html
531,Inclusion of Sensitive Information in Test Code,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/531.html
541,Inclusion of Sensitive Information in an Include File,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/541.html
615,Inclusion of Sensitive Information in Source Code Comments,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/615.html
651,Exposure of WSDL File Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/651.html
22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/22.html
23,Relative Path Traversal,22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/23.html
24,Path Traversal: '../filedir',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/24.html
25,Path Traversal: '/../filedir',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/25.html
26,Path Traversal: '/dir/../filename',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/26.html
27,Path Traversal: 'dir/../../filename',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/27.html
28,Path Traversal: '..\filedir',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/28.html
29,Path Traversal: '\..\filename',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/29.html
30,Path Traversal: '\dir\..\filename',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/30.html
31,Path Traversal: 'dir\..\..\filename',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/31.html
32,Path Traversal: '...' (Triple Dot),23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/32.html
33,Path Traversal: '....' (Multiple Dot),23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/33.html
34,Path Traversal: '....//',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/34.html
35,Path Traversal: '.../...//',23,Relative Path Traversal,https://cwe.mitre.org/data/definitions/35.html
24,Path Traversal: '../filedir',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/24.html
25,Path Traversal: '/../filedir',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/25.html
26,Path Traversal: '/dir/../filename',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/26.html
27,Path Traversal: 'dir/../../filename',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/27.html
28,Path Traversal: '..\filedir',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/28.html
29,Path Traversal: '\..\filename',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/29.html
30,Path Traversal: '\dir\..\filename',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/30.html
31,Path Traversal: 'dir\..\..\filename',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/31.html
32,Path Traversal: '...' (Triple Dot),22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/32.html
33,Path Traversal: '....' (Multiple Dot),22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/33.html
34,Path Traversal: '....//',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/34.html
35,Path Traversal: '.../...//',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/35.html
36,Absolute Path Traversal,22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/36.html
37,Path Traversal: '/absolute/pathname/here',36,Absolute Path Traversal,https://cwe.mitre.org/data/definitions/37.html
38,Path Traversal: '\absolute\pathname\here',36,Absolute Path Traversal,https://cwe.mitre.org/data/definitions/38.html
39,Path Traversal: 'C:dirname',36,Absolute Path Traversal,https://cwe.mitre.org/data/definitions/39.html
40,Path Traversal: '\\UNC\share\name\' (Windows UNC Share),36,Absolute Path Traversal,https://cwe.mitre.org/data/definitions/40.html
37,Path Traversal: '/absolute/pathname/here',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/37.html
38,Path Traversal: '\absolute\pathname\here',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/38.html
39,Path Traversal: 'C:dirname',22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/39.html
40,Path Traversal: '\\UNC\share\name\' (Windows UNC Share),22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),https://cwe.mitre.org/data/definitions/40.html
23,Relative Path Traversal,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/23.html
24,Path Traversal: '../filedir',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/24.html
25,Path Traversal: '/../filedir',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/25.html
26,Path Traversal: '/dir/../filename',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/26.html
27,Path Traversal: 'dir/../../filename',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/27.html
28,Path Traversal: '..\filedir',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/28.html
29,Path Traversal: '\..\filename',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/29.html
30,Path Traversal: '\dir\..\filename',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/30.html
31,Path Traversal: 'dir\..\..\filename',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/31.html
32,Path Traversal: '...' (Triple Dot),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/32.html
33,Path Traversal: '....' (Multiple Dot),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/33.html
34,Path Traversal: '....//',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/34.html
35,Path Traversal: '.../...//',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/35.html
36,Absolute Path Traversal,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/36.html
37,Path Traversal: '/absolute/pathname/here',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/37.html
38,Path Traversal: '\absolute\pathname\here',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/38.html
39,Path Traversal: 'C:dirname',668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/39.html
40,Path Traversal: '\\UNC\share\name\' (Windows UNC Share),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/40.html
374,Passing Mutable Objects to an Untrusted Method,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/374.html
375,Returning a Mutable Object to an Untrusted Caller,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/375.html
377,Insecure Temporary File,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/377.html
378,Creation of Temporary File With Insecure Permissions,377,Insecure Temporary File,https://cwe.mitre.org/data/definitions/378.html
379,Creation of Temporary File in Directory with Insecure Permissions,377,Insecure Temporary File,https://cwe.mitre.org/data/definitions/379.html
378,Creation of Temporary File With Insecure Permissions,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/378.html
379,Creation of Temporary File in Directory with Insecure Permissions,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/379.html
402,Transmission of Private Resources into a New Sphere ('Resource Leak'),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/402.html
403,Exposure of File Descriptor to Unintended Control Sphere ('File Descriptor Leak'),402,Transmission of Private Resources into a New Sphere ('Resource Leak'),https://cwe.mitre.org/data/definitions/403.html
619,Dangling Database Cursor ('Cursor Injection'),402,Transmission of Private Resources into a New Sphere ('Resource Leak'),https://cwe.mitre.org/data/definitions/619.html
403,Exposure of File Descriptor to Unintended Control Sphere ('File Descriptor Leak'),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/403.html
619,Dangling Database Cursor ('Cursor Injection'),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/619.html
427,Uncontrolled Search Path Element,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/427.html
428,Unquoted Search Path or Element,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/428.html
488,Exposure of Data Element to Wrong Session,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/488.html
491,Public cloneable() Method Without Final ('Object Hijack'),668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/491.html
492,Use of Inner Class Containing Sensitive Data,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/492.html
493,Critical Public Variable Without Final Modifier,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/493.html
500,Public Static Field Not Marked Final,493,Critical Public Variable Without Final Modifier,https://cwe.mitre.org/data/definitions/500.html
500,Public Static Field Not Marked Final,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/500.html
498,Cloneable Class Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/498.html
499,Serializable Class Containing Sensitive Data,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/499.html
522,Insufficiently Protected Credentials,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/522.html
256,Plaintext Storage of a Password,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/256.html
257,Storing Passwords in a Recoverable Format,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/257.html
260,Password in Configuration File,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/260.html
13,ASP.NET Misconfiguration: Password in Configuration File,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/13.html
258,Empty Password in Configuration File,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/258.html
555,J2EE Misconfiguration: Plaintext Password in Configuration File,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/555.html
523,Unprotected Transport of Credentials,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/523.html
549,Missing Password Field Masking,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/549.html
524,Use of Cache Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/524.html
525,Use of Web Browser Cache Containing Sensitive Information,524,Use of Cache Containing Sensitive Information,https://cwe.mitre.org/data/definitions/525.html
525,Use of Web Browser Cache Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/525.html
552,Files or Directories Accessible to External Parties,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/552.html
219,Storage of File with Sensitive Data Under Web Root,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/219.html
433,Unparsed Raw Web Content Delivery,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/433.html
220,Storage of File With Sensitive Data Under FTP Root,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/220.html
527,Exposure of Version-Control Repository to an Unauthorized Control Sphere,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/527.html
528,Exposure of Core Dump File to an Unauthorized Control Sphere,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/528.html
529,Exposure of Access Control List Files to an Unauthorized Control Sphere,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/529.html
530,Exposure of Backup File to an Unauthorized Control Sphere,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/530.html
539,Use of Persistent Cookies Containing Sensitive Information,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/539.html
553,Command Shell in Externally Accessible Directory,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/553.html
582,"Array Declared Public, Final, and Static",668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/582.html
583,finalize() Method Declared Public,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/583.html
608,Struts: Non-private Field in ActionForm Class,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/608.html
642,External Control of Critical State Data,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/642.html
15,External Control of System or Configuration Setting,642,External Control of Critical State Data,https://cwe.mitre.org/data/definitions/15.html
426,Untrusted Search Path,642,External Control of Critical State Data,https://cwe.mitre.org/data/definitions/426.html
472,External Control of Assumed-Immutable Web Parameter,642,External Control of Critical State Data,https://cwe.mitre.org/data/definitions/472.html
565,Reliance on Cookies without Validation and Integrity Checking,642,External Control of Critical State Data,https://cwe.mitre.org/data/definitions/565.html
784,Reliance on Cookies without Validation and Integrity Checking in a Security Decision,565,Reliance on Cookies without Validation and Integrity Checking,https://cwe.mitre.org/data/definitions/784.html
784,Reliance on Cookies without Validation and Integrity Checking in a Security Decision,642,External Control of Critical State Data,https://cwe.mitre.org/data/definitions/784.html
73,External Control of File Name or Path,642,External Control of Critical State Data,https://cwe.mitre.org/data/definitions/73.html
114,Process Control,642,External Control of Critical State Data,https://cwe.mitre.org/data/definitions/114.html
15,External Control of System or Configuration Setting,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/15.html
426,Untrusted Search Path,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/426.html
472,External Control of Assumed-Immutable Web Parameter,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/472.html
565,Reliance on Cookies without Validation and Integrity Checking,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/565.html
784,Reliance on Cookies without Validation and Integrity Checking in a Security Decision,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/784.html
73,External Control of File Name or Path,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/73.html
114,Process Control,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/114.html
732,Incorrect Permission Assignment for Critical Resource,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/732.html
1004,Sensitive Cookie Without 'HttpOnly' Flag,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/1004.html
276,Incorrect Default Permissions,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/276.html
277,Insecure Inherited Permissions,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/277.html
278,Insecure Preserved Inherited Permissions,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/278.html
279,Incorrect Execution-Assigned Permissions,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/279.html
281,Improper Preservation of Permissions,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/281.html
767,Access to Critical Private Variable via Public Method,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/767.html
8,J2EE Misconfiguration: Entity Bean Declared Remote,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/8.html
927,Use of Implicit Intent for Sensitive Communication,668,Exposure of Resource to Wrong Sphere,https://cwe.mitre.org/data/definitions/927.html
1189,Improper Isolation of Shared Resources on System-on-a-Chip (SoC),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1189.html
1303,Non-Transparent Sharing of Microarchitectural Resources,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1303.html
1282,Assumed-Immutable Data is Stored in Writable Memory,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1282.html
1327,Binding to an Unrestricted IP Address,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1327.html
134,Use of Externally-Controlled Format String,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/134.html
200,Exposure of Sensitive Information to an Unauthorized Actor,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/200.html
1258,Exposure of Sensitive System Information Due to Uncleared Debug Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1258.html
1273,Device Unlock Credential Sharing,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1273.html
1295,Debug Messages Revealing Unnecessary Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1295.html
201,Insertion of Sensitive Information Into Sent Data,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/201.html
598,Use of GET Request Method With Sensitive Query Strings,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/598.html
203,Observable Discrepancy,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/203.html
1300,Improper Protection Against Physical Side Channels,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1300.html
204,Observable Response Discrepancy,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/204.html
205,Observable Behavioral Discrepancy,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/205.html
1255,Comparison Logic is Vulnerable to Power Side-Channel Attacks,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1255.html
206,Observable Internal Behavioral Discrepancy,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/206.html
207,Observable Behavioral Discrepancy With Equivalent Products,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/207.html
208,Observable Timing Discrepancy,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/208.html
1254,Incorrect Comparison Logic Granularity,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1254.html
209,Generation of Error Message Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/209.html
210,Self-generated Error Message Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/210.html
211,Externally-Generated Error Message Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/211.html
535,Exposure of Information Through Shell Error Message,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/535.html
536,Servlet Runtime Error Message Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/536.html
537,Java Runtime Error Message Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/537.html
550,Server-generated Error Message Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/550.html
213,Exposure of Sensitive Information Due to Incompatible Policies,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/213.html
215,Insertion of Sensitive Information Into Debugging Code,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/215.html
359,Exposure of Private Personal Information to an Unauthorized Actor,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/359.html
497,Exposure of Sensitive System Information to an Unauthorized Control Sphere,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/497.html
214,Invocation of Process Using Visible Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/214.html
526,Exposure of Sensitive Information Through Environmental Variables,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/526.html
548,Exposure of Information Through Directory Listing,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/548.html
538,Insertion of Sensitive Information into Externally-Accessible File or Directory,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/538.html
532,Insertion of Sensitive Information into Log File,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/532.html
540,Inclusion of Sensitive Information in Source Code,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/540.html
531,Inclusion of Sensitive Information in Test Code,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/531.html
541,Inclusion of Sensitive Information in an Include File,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/541.html
615,Inclusion of Sensitive Information in Source Code Comments,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/615.html
651,Exposure of WSDL File Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/651.html
22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/22.html
23,Relative Path Traversal,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/23.html
24,Path Traversal: '../filedir',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/24.html
25,Path Traversal: '/../filedir',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/25.html
26,Path Traversal: '/dir/../filename',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/26.html
27,Path Traversal: 'dir/../../filename',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/27.html
28,Path Traversal: '..\filedir',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/28.html
29,Path Traversal: '\..\filename',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/29.html
30,Path Traversal: '\dir\..\filename',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/30.html
31,Path Traversal: 'dir\..\..\filename',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/31.html
32,Path Traversal: '...' (Triple Dot),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/32.html
33,Path Traversal: '....' (Multiple Dot),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/33.html
34,Path Traversal: '....//',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/34.html
35,Path Traversal: '.../...//',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/35.html
36,Absolute Path Traversal,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/36.html
37,Path Traversal: '/absolute/pathname/here',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/37.html
38,Path Traversal: '\absolute\pathname\here',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/38.html
39,Path Traversal: 'C:dirname',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/39.html
40,Path Traversal: '\\UNC\share\name\' (Windows UNC Share),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/40.html
374,Passing Mutable Objects to an Untrusted Method,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/374.html
375,Returning a Mutable Object to an Untrusted Caller,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/375.html
377,Insecure Temporary File,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/377.html
378,Creation of Temporary File With Insecure Permissions,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/378.html
379,Creation of Temporary File in Directory with Insecure Permissions,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/379.html
402,Transmission of Private Resources into a New Sphere ('Resource Leak'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/402.html
403,Exposure of File Descriptor to Unintended Control Sphere ('File Descriptor Leak'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/403.html
619,Dangling Database Cursor ('Cursor Injection'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/619.html
427,Uncontrolled Search Path Element,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/427.html
428,Unquoted Search Path or Element,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/428.html
488,Exposure of Data Element to Wrong Session,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/488.html
491,Public cloneable() Method Without Final ('Object Hijack'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/491.html
492,Use of Inner Class Containing Sensitive Data,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/492.html
493,Critical Public Variable Without Final Modifier,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/493.html
500,Public Static Field Not Marked Final,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/500.html
498,Cloneable Class Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/498.html
499,Serializable Class Containing Sensitive Data,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/499.html
522,Insufficiently Protected Credentials,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/522.html
256,Plaintext Storage of a Password,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/256.html
257,Storing Passwords in a Recoverable Format,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/257.html
260,Password in Configuration File,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/260.html
13,ASP.NET Misconfiguration: Password in Configuration File,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/13.html
258,Empty Password in Configuration File,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/258.html
555,J2EE Misconfiguration: Plaintext Password in Configuration File,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/555.html
523,Unprotected Transport of Credentials,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/523.html
549,Missing Password Field Masking,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/549.html
524,Use of Cache Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/524.html
525,Use of Web Browser Cache Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/525.html
552,Files or Directories Accessible to External Parties,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/552.html
219,Storage of File with Sensitive Data Under Web Root,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/219.html
433,Unparsed Raw Web Content Delivery,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/433.html
220,Storage of File With Sensitive Data Under FTP Root,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/220.html
527,Exposure of Version-Control Repository to an Unauthorized Control Sphere,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/527.html
528,Exposure of Core Dump File to an Unauthorized Control Sphere,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/528.html
529,Exposure of Access Control List Files to an Unauthorized Control Sphere,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/529.html
530,Exposure of Backup File to an Unauthorized Control Sphere,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/530.html
539,Use of Persistent Cookies Containing Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/539.html
553,Command Shell in Externally Accessible Directory,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/553.html
582,"Array Declared Public, Final, and Static",664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/582.html
583,finalize() Method Declared Public,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/583.html
608,Struts: Non-private Field in ActionForm Class,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/608.html
642,External Control of Critical State Data,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/642.html
426,Untrusted Search Path,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/426.html
565,Reliance on Cookies without Validation and Integrity Checking,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/565.html
784,Reliance on Cookies without Validation and Integrity Checking in a Security Decision,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/784.html
732,Incorrect Permission Assignment for Critical Resource,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/732.html
1004,Sensitive Cookie Without 'HttpOnly' Flag,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1004.html
276,Incorrect Default Permissions,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/276.html
277,Insecure Inherited Permissions,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/277.html
278,Insecure Preserved Inherited Permissions,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/278.html
279,Incorrect Execution-Assigned Permissions,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/279.html
281,Improper Preservation of Permissions,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/281.html
767,Access to Critical Private Variable via Public Method,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/767.html
8,J2EE Misconfiguration: Entity Bean Declared Remote,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/8.html
927,Use of Implicit Intent for Sensitive Communication,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/927.html
669,Incorrect Resource Transfer Between Spheres,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/669.html
212,Improper Removal of Sensitive Information Before Storage or Transfer,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/212.html
1258,Exposure of Sensitive System Information Due to Uncleared Debug Information,212,Improper Removal of Sensitive Information Before Storage or Transfer,https://cwe.mitre.org/data/definitions/1258.html
226,Sensitive Information in Resource Not Removed Before Reuse,212,Improper Removal of Sensitive Information Before Storage or Transfer,https://cwe.mitre.org/data/definitions/226.html
1239,Improper Zeroization of Hardware Register,212,Improper Removal of Sensitive Information Before Storage or Transfer,https://cwe.mitre.org/data/definitions/1239.html
1272,Sensitive Information Uncleared Before Debug/Power State Transition,212,Improper Removal of Sensitive Information Before Storage or Transfer,https://cwe.mitre.org/data/definitions/1272.html
1301,Insufficient or Incomplete Data Removal within Hardware Component,212,Improper Removal of Sensitive Information Before Storage or Transfer,https://cwe.mitre.org/data/definitions/1301.html
1330,Remanent Data Readable after Memory Erase,212,Improper Removal of Sensitive Information Before Storage or Transfer,https://cwe.mitre.org/data/definitions/1330.html
244,Improper Clearing of Heap Memory Before Release ('Heap Inspection'),212,Improper Removal of Sensitive Information Before Storage or Transfer,https://cwe.mitre.org/data/definitions/244.html
1258,Exposure of Sensitive System Information Due to Uncleared Debug Information,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/1258.html
226,Sensitive Information in Resource Not Removed Before Reuse,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/226.html
1239,Improper Zeroization of Hardware Register,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/1239.html
1272,Sensitive Information Uncleared Before Debug/Power State Transition,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/1272.html
1301,Insufficient or Incomplete Data Removal within Hardware Component,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/1301.html
1330,Remanent Data Readable after Memory Erase,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/1330.html
244,Improper Clearing of Heap Memory Before Release ('Heap Inspection'),669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/244.html
243,Creation of chroot Jail Without Changing Working Directory,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/243.html
434,Unrestricted Upload of File with Dangerous Type,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/434.html
494,Download of Code Without Integrity Check,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/494.html
602,Client-Side Enforcement of Server-Side Security,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/602.html
565,Reliance on Cookies without Validation and Integrity Checking,602,Client-Side Enforcement of Server-Side Security,https://cwe.mitre.org/data/definitions/565.html
784,Reliance on Cookies without Validation and Integrity Checking in a Security Decision,602,Client-Side Enforcement of Server-Side Security,https://cwe.mitre.org/data/definitions/784.html
603,Use of Client-Side Authentication,602,Client-Side Enforcement of Server-Side Security,https://cwe.mitre.org/data/definitions/603.html
565,Reliance on Cookies without Validation and Integrity Checking,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/565.html
784,Reliance on Cookies without Validation and Integrity Checking in a Security Decision,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/784.html
603,Use of Client-Side Authentication,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/603.html
829,Inclusion of Functionality from Untrusted Control Sphere,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/829.html
827,Improper Control of Document Type Definition,829,Inclusion of Functionality from Untrusted Control Sphere,https://cwe.mitre.org/data/definitions/827.html
830,Inclusion of Web Functionality from an Untrusted Source,829,Inclusion of Functionality from Untrusted Control Sphere,https://cwe.mitre.org/data/definitions/830.html
98,Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion'),829,Inclusion of Functionality from Untrusted Control Sphere,https://cwe.mitre.org/data/definitions/98.html
827,Improper Control of Document Type Definition,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/827.html
830,Inclusion of Web Functionality from an Untrusted Source,669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/830.html
98,Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion'),669,Incorrect Resource Transfer Between Spheres,https://cwe.mitre.org/data/definitions/98.html
212,Improper Removal of Sensitive Information Before Storage or Transfer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/212.html
243,Creation of chroot Jail Without Changing Working Directory,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/243.html
434,Unrestricted Upload of File with Dangerous Type,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/434.html
494,Download of Code Without Integrity Check,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/494.html
602,Client-Side Enforcement of Server-Side Security,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/602.html
603,Use of Client-Side Authentication,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/603.html
829,Inclusion of Functionality from Untrusted Control Sphere,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/829.html
827,Improper Control of Document Type Definition,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/827.html
830,Inclusion of Web Functionality from an Untrusted Source,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/830.html
98,Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/98.html
673,External Influence of Sphere Definition,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/673.html
426,Untrusted Search Path,673,External Influence of Sphere Definition,https://cwe.mitre.org/data/definitions/426.html
704,Incorrect Type Conversion or Cast,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/704.html
588,Attempt to Access Child of a Non-structure Pointer,704,Incorrect Type Conversion or Cast,https://cwe.mitre.org/data/definitions/588.html
681,Incorrect Conversion between Numeric Types,704,Incorrect Type Conversion or Cast,https://cwe.mitre.org/data/definitions/681.html
192,Integer Coercion Error,681,Incorrect Conversion between Numeric Types,https://cwe.mitre.org/data/definitions/192.html
194,Unexpected Sign Extension,681,Incorrect Conversion between Numeric Types,https://cwe.mitre.org/data/definitions/194.html
195,Signed to Unsigned Conversion Error,681,Incorrect Conversion between Numeric Types,https://cwe.mitre.org/data/definitions/195.html
196,Unsigned to Signed Conversion Error,681,Incorrect Conversion between Numeric Types,https://cwe.mitre.org/data/definitions/196.html
197,Numeric Truncation Error,681,Incorrect Conversion between Numeric Types,https://cwe.mitre.org/data/definitions/197.html
192,Integer Coercion Error,704,Incorrect Type Conversion or Cast,https://cwe.mitre.org/data/definitions/192.html
194,Unexpected Sign Extension,704,Incorrect Type Conversion or Cast,https://cwe.mitre.org/data/definitions/194.html
195,Signed to Unsigned Conversion Error,704,Incorrect Type Conversion or Cast,https://cwe.mitre.org/data/definitions/195.html
196,Unsigned to Signed Conversion Error,704,Incorrect Type Conversion or Cast,https://cwe.mitre.org/data/definitions/196.html
197,Numeric Truncation Error,704,Incorrect Type Conversion or Cast,https://cwe.mitre.org/data/definitions/197.html
843,Access of Resource Using Incompatible Type ('Type Confusion'),704,Incorrect Type Conversion or Cast,https://cwe.mitre.org/data/definitions/843.html
588,Attempt to Access Child of a Non-structure Pointer,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/588.html
681,Incorrect Conversion between Numeric Types,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/681.html
192,Integer Coercion Error,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/192.html
194,Unexpected Sign Extension,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/194.html
195,Signed to Unsigned Conversion Error,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/195.html
196,Unsigned to Signed Conversion Error,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/196.html
197,Numeric Truncation Error,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/197.html
843,Access of Resource Using Incompatible Type ('Type Confusion'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/843.html
706,Use of Incorrectly-Resolved Name or Reference,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/706.html
178,Improper Handling of Case Sensitivity,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/178.html
22,Improper Limitation of a Pathname to a Restricted Directory ('Path Traversal'),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/22.html
23,Relative Path Traversal,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/23.html
24,Path Traversal: '../filedir',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/24.html
25,Path Traversal: '/../filedir',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/25.html
26,Path Traversal: '/dir/../filename',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/26.html
27,Path Traversal: 'dir/../../filename',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/27.html
28,Path Traversal: '..\filedir',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/28.html
29,Path Traversal: '\..\filename',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/29.html
30,Path Traversal: '\dir\..\filename',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/30.html
31,Path Traversal: 'dir\..\..\filename',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/31.html
32,Path Traversal: '...' (Triple Dot),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/32.html
33,Path Traversal: '....' (Multiple Dot),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/33.html
34,Path Traversal: '....//',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/34.html
35,Path Traversal: '.../...//',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/35.html
36,Absolute Path Traversal,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/36.html
37,Path Traversal: '/absolute/pathname/here',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/37.html
38,Path Traversal: '\absolute\pathname\here',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/38.html
39,Path Traversal: 'C:dirname',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/39.html
40,Path Traversal: '\\UNC\share\name\' (Windows UNC Share),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/40.html
386,Symbolic Name not Mapping to Correct Object,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/386.html
41,Improper Resolution of Path Equivalence,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/41.html
42,Path Equivalence: 'filename.' (Trailing Dot),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/42.html
43,Path Equivalence: 'filename....' (Multiple Trailing Dot),42,Path Equivalence: 'filename.' (Trailing Dot),https://cwe.mitre.org/data/definitions/43.html
43,Path Equivalence: 'filename....' (Multiple Trailing Dot),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/43.html
44,Path Equivalence: 'file.name' (Internal Dot),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/44.html
45,Path Equivalence: 'file...name' (Multiple Internal Dot),44,Path Equivalence: 'file.name' (Internal Dot),https://cwe.mitre.org/data/definitions/45.html
45,Path Equivalence: 'file...name' (Multiple Internal Dot),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/45.html
46,Path Equivalence: 'filename ' (Trailing Space),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/46.html
47,Path Equivalence: ' filename' (Leading Space),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/47.html
48,Path Equivalence: 'file name' (Internal Whitespace),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/48.html
49,Path Equivalence: 'filename/' (Trailing Slash),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/49.html
50,Path Equivalence: '//multiple/leading/slash',41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/50.html
51,Path Equivalence: '/multiple//internal/slash',41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/51.html
52,Path Equivalence: '/multiple/trailing/slash//',41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/52.html
53,Path Equivalence: '\multiple\\internal\backslash',41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/53.html
54,Path Equivalence: 'filedir\' (Trailing Backslash),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/54.html
55,Path Equivalence: '/./' (Single Dot Directory),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/55.html
56,Path Equivalence: 'filedir*' (Wildcard),41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/56.html
57,Path Equivalence: 'fakedir/../realdir/filename',41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/57.html
58,Path Equivalence: Windows 8.3 Filename,41,Improper Resolution of Path Equivalence,https://cwe.mitre.org/data/definitions/58.html
42,Path Equivalence: 'filename.' (Trailing Dot),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/42.html
43,Path Equivalence: 'filename....' (Multiple Trailing Dot),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/43.html
44,Path Equivalence: 'file.name' (Internal Dot),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/44.html
45,Path Equivalence: 'file...name' (Multiple Internal Dot),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/45.html
46,Path Equivalence: 'filename ' (Trailing Space),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/46.html
47,Path Equivalence: ' filename' (Leading Space),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/47.html
48,Path Equivalence: 'file name' (Internal Whitespace),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/48.html
49,Path Equivalence: 'filename/' (Trailing Slash),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/49.html
50,Path Equivalence: '//multiple/leading/slash',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/50.html
51,Path Equivalence: '/multiple//internal/slash',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/51.html
52,Path Equivalence: '/multiple/trailing/slash//',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/52.html
53,Path Equivalence: '\multiple\\internal\backslash',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/53.html
54,Path Equivalence: 'filedir\' (Trailing Backslash),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/54.html
55,Path Equivalence: '/./' (Single Dot Directory),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/55.html
56,Path Equivalence: 'filedir*' (Wildcard),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/56.html
57,Path Equivalence: 'fakedir/../realdir/filename',706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/57.html
58,Path Equivalence: Windows 8.3 Filename,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/58.html
59,Improper Link Resolution Before File Access ('Link Following'),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/59.html
61,UNIX Symbolic Link (Symlink) Following,59,Improper Link Resolution Before File Access ('Link Following'),https://cwe.mitre.org/data/definitions/61.html
62,UNIX Hard Link,59,Improper Link Resolution Before File Access ('Link Following'),https://cwe.mitre.org/data/definitions/62.html
64,Windows Shortcut Following (.LNK),59,Improper Link Resolution Before File Access ('Link Following'),https://cwe.mitre.org/data/definitions/64.html
65,Windows Hard Link,59,Improper Link Resolution Before File Access ('Link Following'),https://cwe.mitre.org/data/definitions/65.html
61,UNIX Symbolic Link (Symlink) Following,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/61.html
62,UNIX Hard Link,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/62.html
64,Windows Shortcut Following (.LNK),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/64.html
65,Windows Hard Link,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/65.html
66,Improper Handling of File Names that Identify Virtual Resources,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/66.html
67,Improper Handling of Windows Device Names,66,Improper Handling of File Names that Identify Virtual Resources,https://cwe.mitre.org/data/definitions/67.html
69,Improper Handling of Windows ::DATA Alternate Data Stream,66,Improper Handling of File Names that Identify Virtual Resources,https://cwe.mitre.org/data/definitions/69.html
72,Improper Handling of Apple HFS+ Alternate Data Stream Path,66,Improper Handling of File Names that Identify Virtual Resources,https://cwe.mitre.org/data/definitions/72.html
67,Improper Handling of Windows Device Names,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/67.html
69,Improper Handling of Windows ::DATA Alternate Data Stream,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/69.html
72,Improper Handling of Apple HFS+ Alternate Data Stream Path,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/72.html
827,Improper Control of Document Type Definition,706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/827.html
98,Improper Control of Filename for Include/Require Statement in PHP Program ('PHP Remote File Inclusion'),706,Use of Incorrectly-Resolved Name or Reference,https://cwe.mitre.org/data/definitions/98.html
178,Improper Handling of Case Sensitivity,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/178.html
386,Symbolic Name not Mapping to Correct Object,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/386.html
41,Improper Resolution of Path Equivalence,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/41.html
42,Path Equivalence: 'filename.' (Trailing Dot),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/42.html
43,Path Equivalence: 'filename....' (Multiple Trailing Dot),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/43.html
44,Path Equivalence: 'file.name' (Internal Dot),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/44.html
45,Path Equivalence: 'file...name' (Multiple Internal Dot),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/45.html
46,Path Equivalence: 'filename ' (Trailing Space),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/46.html
47,Path Equivalence: ' filename' (Leading Space),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/47.html
48,Path Equivalence: 'file name' (Internal Whitespace),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/48.html
49,Path Equivalence: 'filename/' (Trailing Slash),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/49.html
50,Path Equivalence: '//multiple/leading/slash',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/50.html
51,Path Equivalence: '/multiple//internal/slash',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/51.html
52,Path Equivalence: '/multiple/trailing/slash//',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/52.html
53,Path Equivalence: '\multiple\\internal\backslash',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/53.html
54,Path Equivalence: 'filedir\' (Trailing Backslash),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/54.html
55,Path Equivalence: '/./' (Single Dot Directory),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/55.html
56,Path Equivalence: 'filedir*' (Wildcard),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/56.html
57,Path Equivalence: 'fakedir/../realdir/filename',664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/57.html
58,Path Equivalence: Windows 8.3 Filename,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/58.html
59,Improper Link Resolution Before File Access ('Link Following'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/59.html
61,UNIX Symbolic Link (Symlink) Following,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/61.html
62,UNIX Hard Link,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/62.html
64,Windows Shortcut Following (.LNK),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/64.html
65,Windows Hard Link,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/65.html
66,Improper Handling of File Names that Identify Virtual Resources,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/66.html
67,Improper Handling of Windows Device Names,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/67.html
69,Improper Handling of Windows ::DATA Alternate Data Stream,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/69.html
72,Improper Handling of Apple HFS+ Alternate Data Stream Path,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/72.html
749,Exposed Dangerous Method or Function,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/749.html
618,Exposed Unsafe ActiveX Method,749,Exposed Dangerous Method or Function,https://cwe.mitre.org/data/definitions/618.html
782,Exposed IOCTL with Insufficient Access Control,749,Exposed Dangerous Method or Function,https://cwe.mitre.org/data/definitions/782.html
618,Exposed Unsafe ActiveX Method,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/618.html
782,Exposed IOCTL with Insufficient Access Control,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/782.html
911,Improper Update of Reference Count,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/911.html
913,Improper Control of Dynamically-Managed Code Resources,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/913.html
470,Use of Externally-Controlled Input to Select Classes or Code ('Unsafe Reflection'),913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/470.html
502,Deserialization of Untrusted Data,913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/502.html
914,Improper Control of Dynamically-Identified Variables,913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/914.html
621,Variable Extraction Error,914,Improper Control of Dynamically-Identified Variables,https://cwe.mitre.org/data/definitions/621.html
627,Dynamic Variable Evaluation,914,Improper Control of Dynamically-Identified Variables,https://cwe.mitre.org/data/definitions/627.html
621,Variable Extraction Error,913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/621.html
627,Dynamic Variable Evaluation,913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/627.html
915,Improperly Controlled Modification of Dynamically-Determined Object Attributes,913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/915.html
1321,Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution'),915,Improperly Controlled Modification of Dynamically-Determined Object Attributes,https://cwe.mitre.org/data/definitions/1321.html
1321,Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution'),913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/1321.html
94,Improper Control of Generation of Code ('Code Injection'),913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/94.html
1336,Improper Neutralization of Special Elements Used in a Template Engine,94,Improper Control of Generation of Code ('Code Injection'),https://cwe.mitre.org/data/definitions/1336.html
95,Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection'),94,Improper Control of Generation of Code ('Code Injection'),https://cwe.mitre.org/data/definitions/95.html
96,Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection'),94,Improper Control of Generation of Code ('Code Injection'),https://cwe.mitre.org/data/definitions/96.html
97,Improper Neutralization of Server-Side Includes (SSI) Within a Web Page,96,Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection'),https://cwe.mitre.org/data/definitions/97.html
97,Improper Neutralization of Server-Side Includes (SSI) Within a Web Page,94,Improper Control of Generation of Code ('Code Injection'),https://cwe.mitre.org/data/definitions/97.html
1336,Improper Neutralization of Special Elements Used in a Template Engine,913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/1336.html
95,Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection'),913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/95.html
96,Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection'),913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/96.html
97,Improper Neutralization of Server-Side Includes (SSI) Within a Web Page,913,Improper Control of Dynamically-Managed Code Resources,https://cwe.mitre.org/data/definitions/97.html
502,Deserialization of Untrusted Data,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/502.html
914,Improper Control of Dynamically-Identified Variables,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/914.html
621,Variable Extraction Error,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/621.html
627,Dynamic Variable Evaluation,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/627.html
915,Improperly Controlled Modification of Dynamically-Determined Object Attributes,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/915.html
1321,Improperly Controlled Modification of Object Prototype Attributes ('Prototype Pollution'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1321.html
94,Improper Control of Generation of Code ('Code Injection'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/94.html
1336,Improper Neutralization of Special Elements Used in a Template Engine,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/1336.html
95,Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/95.html
96,Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection'),664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/96.html
97,Improper Neutralization of Server-Side Includes (SSI) Within a Web Page,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/97.html
922,Insecure Storage of Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/922.html
312,Cleartext Storage of Sensitive Information,922,Insecure Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/312.html
313,Cleartext Storage in a File or on Disk,312,Cleartext Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/313.html
314,Cleartext Storage in the Registry,312,Cleartext Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/314.html
315,Cleartext Storage of Sensitive Information in a Cookie,312,Cleartext Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/315.html
316,Cleartext Storage of Sensitive Information in Memory,312,Cleartext Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/316.html
317,Cleartext Storage of Sensitive Information in GUI,312,Cleartext Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/317.html
318,Cleartext Storage of Sensitive Information in Executable,312,Cleartext Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/318.html
313,Cleartext Storage in a File or on Disk,922,Insecure Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/313.html
314,Cleartext Storage in the Registry,922,Insecure Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/314.html
315,Cleartext Storage of Sensitive Information in a Cookie,922,Insecure Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/315.html
316,Cleartext Storage of Sensitive Information in Memory,922,Insecure Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/316.html
317,Cleartext Storage of Sensitive Information in GUI,922,Insecure Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/317.html
318,Cleartext Storage of Sensitive Information in Executable,922,Insecure Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/318.html
921,Storage of Sensitive Data in a Mechanism without Access Control,922,Insecure Storage of Sensitive Information,https://cwe.mitre.org/data/definitions/921.html
312,Cleartext Storage of Sensitive Information,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/312.html
313,Cleartext Storage in a File or on Disk,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/313.html
314,Cleartext Storage in the Registry,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/314.html
315,Cleartext Storage of Sensitive Information in a Cookie,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/315.html
316,Cleartext Storage of Sensitive Information in Memory,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/316.html
317,Cleartext Storage of Sensitive Information in GUI,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/317.html
318,Cleartext Storage of Sensitive Information in Executable,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/318.html
921,Storage of Sensitive Data in a Mechanism without Access Control,664,Improper Control of a Resource Through its Lifetime,https://cwe.mitre.org/data/definitions/921.html
682,Incorrect Calculation,,,https://cwe.mitre.org/data/definitions/682.html
128,Wrap-around Error,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/128.html
131,Incorrect Calculation of Buffer Size,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/131.html
1335,Incorrect Bitwise Shift of Integer,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/1335.html
1339,Insufficient Precision or Accuracy of a Real Number,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/1339.html
135,Incorrect Calculation of Multi-Byte String Length,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/135.html
190,Integer Overflow or Wraparound,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/190.html
191,Integer Underflow (Wrap or Wraparound),682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/191.html
193,Off-by-one Error,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/193.html
369,Divide By Zero,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/369.html
467,Use of sizeof() on a Pointer Type,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/467.html
468,Incorrect Pointer Scaling,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/468.html
469,Use of Pointer Subtraction to Determine Size,682,Incorrect Calculation,https://cwe.mitre.org/data/definitions/469.html
691,Insufficient Control Flow Management,,,https://cwe.mitre.org/data/definitions/691.html
1265,Unintended Reentrant Invocation of Non-reentrant Code Via Nested Calls,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1265.html
1281,Sequence of Processor Instructions Leads to Unexpected Behavior,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1281.html
362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/362.html
1223,Race Condition for Write-Once Attributes,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/1223.html
1298,Hardware Logic Contains Race Conditions,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/1298.html
364,Signal Handler Race Condition,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/364.html
432,Dangerous Signal Handler not Disabled During Sensitive Operations,364,Signal Handler Race Condition,https://cwe.mitre.org/data/definitions/432.html
828,Signal Handler with Functionality that is not Asynchronous-Safe,364,Signal Handler Race Condition,https://cwe.mitre.org/data/definitions/828.html
479,Signal Handler Use of a Non-reentrant Function,828,Signal Handler with Functionality that is not Asynchronous-Safe,https://cwe.mitre.org/data/definitions/479.html
479,Signal Handler Use of a Non-reentrant Function,364,Signal Handler Race Condition,https://cwe.mitre.org/data/definitions/479.html
831,Signal Handler Function Associated with Multiple Signals,364,Signal Handler Race Condition,https://cwe.mitre.org/data/definitions/831.html
432,Dangerous Signal Handler not Disabled During Sensitive Operations,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/432.html
828,Signal Handler with Functionality that is not Asynchronous-Safe,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/828.html
479,Signal Handler Use of a Non-reentrant Function,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/479.html
831,Signal Handler Function Associated with Multiple Signals,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/831.html
366,Race Condition within a Thread,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/366.html
367,Time-of-check Time-of-use (TOCTOU) Race Condition,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/367.html
363,Race Condition Enabling Link Following,367,Time-of-check Time-of-use (TOCTOU) Race Condition,https://cwe.mitre.org/data/definitions/363.html
365,Race Condition in Switch,367,Time-of-check Time-of-use (TOCTOU) Race Condition,https://cwe.mitre.org/data/definitions/365.html
363,Race Condition Enabling Link Following,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/363.html
365,Race Condition in Switch,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/365.html
368,Context Switching Race Condition,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/368.html
421,Race Condition During Access to Alternate Channel,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/421.html
689,Permission Race Condition During Resource Copy,362,Concurrent Execution using Shared Resource with Improper Synchronization ('Race Condition'),https://cwe.mitre.org/data/definitions/689.html
1223,Race Condition for Write-Once Attributes,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1223.html
1298,Hardware Logic Contains Race Conditions,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1298.html
364,Signal Handler Race Condition,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/364.html
432,Dangerous Signal Handler not Disabled During Sensitive Operations,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/432.html
828,Signal Handler with Functionality that is not Asynchronous-Safe,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/828.html
479,Signal Handler Use of a Non-reentrant Function,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/479.html
831,Signal Handler Function Associated with Multiple Signals,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/831.html
366,Race Condition within a Thread,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/366.html
367,Time-of-check Time-of-use (TOCTOU) Race Condition,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/367.html
363,Race Condition Enabling Link Following,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/363.html
365,Race Condition in Switch,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/365.html
368,Context Switching Race Condition,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/368.html
421,Race Condition During Access to Alternate Channel,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/421.html
689,Permission Race Condition During Resource Copy,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/689.html
430,Deployment of Wrong Handler,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/430.html
431,Missing Handler,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/431.html
623,Unsafe ActiveX Control Marked Safe For Scripting,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/623.html
662,Improper Synchronization,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/662.html
1058,Invokable Control Element in Multi-Thread Context with non-Final Static Storable or Member Element,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1058.html
663,Use of a Non-reentrant Function in a Concurrent Context,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/663.html
558,Use of getlogin() in Multithreaded Application,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/558.html
667,Improper Locking,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/667.html
1232,Improper Lock Behavior After Power State Transition,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1232.html
1233,Improper Hardware Lock Protection for Security Sensitive Controls,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1233.html
1234,Hardware Internal or Debug Modes Allow Override of Locks,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1234.html
412,Unrestricted Externally Accessible Lock,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/412.html
413,Improper Resource Locking,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/413.html
591,Sensitive Data Storage in Improperly Locked Memory,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/591.html
414,Missing Lock Check,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/414.html
609,Double-Checked Locking,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/609.html
764,Multiple Locks of a Critical Resource,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/764.html
765,Multiple Unlocks of a Critical Resource,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/765.html
832,Unlock of a Resource that is not Locked,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/832.html
833,Deadlock,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/833.html
820,Missing Synchronization,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/820.html
1096,Singleton Class Instance Creation without Proper Locking or Synchronization,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1096.html
543,Use of Singleton Pattern Without Synchronization in a Multithreaded Context,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/543.html
567,Unsynchronized Access to Shared Data in a Multithreaded Context,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/567.html
821,Incorrect Synchronization,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/821.html
1088,Synchronous Access of Remote Resource without Timeout,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1088.html
1264,Hardware Logic with Insecure De-Synchronization between Control and Data Channels,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1264.html
572,Call to Thread run() instead of start(),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/572.html
574,EJB Bad Practices: Use of Synchronization Primitives,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/574.html
670,Always-Incorrect Control Flow Implementation,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/670.html
480,Use of Incorrect Operator,670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/480.html
481,Assigning instead of Comparing,480,Use of Incorrect Operator,https://cwe.mitre.org/data/definitions/481.html
482,Comparing instead of Assigning,480,Use of Incorrect Operator,https://cwe.mitre.org/data/definitions/482.html
597,Use of Wrong Operator in String Comparison,480,Use of Incorrect Operator,https://cwe.mitre.org/data/definitions/597.html
481,Assigning instead of Comparing,670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/481.html
482,Comparing instead of Assigning,670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/482.html
597,Use of Wrong Operator in String Comparison,670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/597.html
483,Incorrect Block Delimitation,670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/483.html
484,Omitted Break Statement in Switch,670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/484.html
617,Reachable Assertion,670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/617.html
698,Execution After Redirect (EAR),670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/698.html
783,Operator Precedence Logic Error,670,Always-Incorrect Control Flow Implementation,https://cwe.mitre.org/data/definitions/783.html
480,Use of Incorrect Operator,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/480.html
481,Assigning instead of Comparing,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/481.html
482,Comparing instead of Assigning,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/482.html
597,Use of Wrong Operator in String Comparison,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/597.html
483,Incorrect Block Delimitation,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/483.html
484,Omitted Break Statement in Switch,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/484.html
617,Reachable Assertion,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/617.html
698,Execution After Redirect (EAR),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/698.html
783,Operator Precedence Logic Error,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/783.html
674,Uncontrolled Recursion,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/674.html
776,Improper Restriction of Recursive Entity References in DTDs ('XML Entity Expansion'),674,Uncontrolled Recursion,https://cwe.mitre.org/data/definitions/776.html
776,Improper Restriction of Recursive Entity References in DTDs ('XML Entity Expansion'),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/776.html
696,Incorrect Behavior Order,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/696.html
1190,DMA Device Enabled Too Early in Boot Phase,696,Incorrect Behavior Order,https://cwe.mitre.org/data/definitions/1190.html
1193,Power-On of Untrusted Execution Core Before Enabling Fabric Access Control,696,Incorrect Behavior Order,https://cwe.mitre.org/data/definitions/1193.html
1280,Access Control Check Implemented After Asset is Accessed,696,Incorrect Behavior Order,https://cwe.mitre.org/data/definitions/1280.html
179,Incorrect Behavior Order: Early Validation,696,Incorrect Behavior Order,https://cwe.mitre.org/data/definitions/179.html
180,Incorrect Behavior Order: Validate Before Canonicalize,179,Incorrect Behavior Order: Early Validation,https://cwe.mitre.org/data/definitions/180.html
181,Incorrect Behavior Order: Validate Before Filter,179,Incorrect Behavior Order: Early Validation,https://cwe.mitre.org/data/definitions/181.html
180,Incorrect Behavior Order: Validate Before Canonicalize,696,Incorrect Behavior Order,https://cwe.mitre.org/data/definitions/180.html
181,Incorrect Behavior Order: Validate Before Filter,696,Incorrect Behavior Order,https://cwe.mitre.org/data/definitions/181.html
408,Incorrect Behavior Order: Early Amplification,696,Incorrect Behavior Order,https://cwe.mitre.org/data/definitions/408.html
551,Incorrect Behavior Order: Authorization Before Parsing and Canonicalization,696,Incorrect Behavior Order,https://cwe.mitre.org/data/definitions/551.html
1190,DMA Device Enabled Too Early in Boot Phase,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1190.html
1193,Power-On of Untrusted Execution Core Before Enabling Fabric Access Control,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1193.html
1280,Access Control Check Implemented After Asset is Accessed,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1280.html
179,Incorrect Behavior Order: Early Validation,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/179.html
180,Incorrect Behavior Order: Validate Before Canonicalize,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/180.html
181,Incorrect Behavior Order: Validate Before Filter,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/181.html
408,Incorrect Behavior Order: Early Amplification,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/408.html
551,Incorrect Behavior Order: Authorization Before Parsing and Canonicalization,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/551.html
705,Incorrect Control Flow Scoping,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/705.html
248,Uncaught Exception,705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/248.html
600,Uncaught Exception in Servlet ,248,Uncaught Exception,https://cwe.mitre.org/data/definitions/600.html
600,Uncaught Exception in Servlet ,705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/600.html
382,J2EE Bad Practices: Use of System.exit(),705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/382.html
395,Use of NullPointerException Catch to Detect NULL Pointer Dereference,705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/395.html
396,Declaration of Catch for Generic Exception,705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/396.html
397,Declaration of Throws for Generic Exception,705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/397.html
455,Non-exit on Failed Initialization,705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/455.html
584,Return Inside Finally Block,705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/584.html
698,Execution After Redirect (EAR),705,Incorrect Control Flow Scoping,https://cwe.mitre.org/data/definitions/698.html
248,Uncaught Exception,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/248.html
600,Uncaught Exception in Servlet ,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/600.html
382,J2EE Bad Practices: Use of System.exit(),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/382.html
395,Use of NullPointerException Catch to Detect NULL Pointer Dereference,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/395.html
396,Declaration of Catch for Generic Exception,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/396.html
397,Declaration of Throws for Generic Exception,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/397.html
455,Non-exit on Failed Initialization,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/455.html
584,Return Inside Finally Block,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/584.html
749,Exposed Dangerous Method or Function,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/749.html
618,Exposed Unsafe ActiveX Method,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/618.html
782,Exposed IOCTL with Insufficient Access Control,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/782.html
768,Incorrect Short Circuit Evaluation,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/768.html
799,Improper Control of Interaction Frequency,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/799.html
307,Improper Restriction of Excessive Authentication Attempts,799,Improper Control of Interaction Frequency,https://cwe.mitre.org/data/definitions/307.html
837,"Improper Enforcement of a Single, Unique Action",799,Improper Control of Interaction Frequency,https://cwe.mitre.org/data/definitions/837.html
307,Improper Restriction of Excessive Authentication Attempts,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/307.html
837,"Improper Enforcement of a Single, Unique Action",691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/837.html
834,Excessive Iteration,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/834.html
1322,"Use of Blocking Code in Single-threaded, Non-blocking Context",834,Excessive Iteration,https://cwe.mitre.org/data/definitions/1322.html
835,Loop with Unreachable Exit Condition ('Infinite Loop'),834,Excessive Iteration,https://cwe.mitre.org/data/definitions/835.html
1322,"Use of Blocking Code in Single-threaded, Non-blocking Context",691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1322.html
835,Loop with Unreachable Exit Condition ('Infinite Loop'),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/835.html
841,Improper Enforcement of Behavioral Workflow,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/841.html
94,Improper Control of Generation of Code ('Code Injection'),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/94.html
1336,Improper Neutralization of Special Elements Used in a Template Engine,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/1336.html
95,Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection'),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/95.html
96,Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection'),691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/96.html
97,Improper Neutralization of Server-Side Includes (SSI) Within a Web Page,691,Insufficient Control Flow Management,https://cwe.mitre.org/data/definitions/97.html
693,Protection Mechanism Failure,,,https://cwe.mitre.org/data/definitions/693.html
1039,Automated Recognition Mechanism with Inadequate Detection or Handling of Adversarial Input Perturbations,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1039.html
1248,Semiconductor Defects in Hardware Logic with Security-Sensitive Implications,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1248.html
1253,Incorrect Selection of Fuse Values,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1253.html
1269,Product Released in Non-Release Configuration,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1269.html
1278,Missing Protection Against Hardware Reverse Engineering Using Integrated Circuit (IC) Imaging Techniques,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1278.html
1291,Public Key Re-Use for Signing both Debug and Production Code,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1291.html
1318,Missing Support for Security Features in On-chip Fabrics or Buses,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1318.html
1319,Improper Protection against Electromagnetic Fault Injection (EM-FI),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1319.html
1326,Missing Immutable Root of Trust in Hardware,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1326.html
1332,Insufficient Protection Against Instruction Skipping Via Fault Injection,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1332.html
1338,Improper Protections Against Hardware Overheating,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1338.html
182,Collapse of Data into Unsafe Value,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/182.html
184,Incomplete List of Disallowed Inputs,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/184.html
311,Missing Encryption of Sensitive Data,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/311.html
312,Cleartext Storage of Sensitive Information,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/312.html
313,Cleartext Storage in a File or on Disk,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/313.html
314,Cleartext Storage in the Registry,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/314.html
315,Cleartext Storage of Sensitive Information in a Cookie,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/315.html
316,Cleartext Storage of Sensitive Information in Memory,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/316.html
317,Cleartext Storage of Sensitive Information in GUI,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/317.html
318,Cleartext Storage of Sensitive Information in Executable,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/318.html
319,Cleartext Transmission of Sensitive Information,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/319.html
5,J2EE Misconfiguration: Data Transmission Without Encryption,319,Cleartext Transmission of Sensitive Information,https://cwe.mitre.org/data/definitions/5.html
5,J2EE Misconfiguration: Data Transmission Without Encryption,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/5.html
614,Sensitive Cookie in HTTPS Session Without 'Secure' Attribute,311,Missing Encryption of Sensitive Data,https://cwe.mitre.org/data/definitions/614.html
312,Cleartext Storage of Sensitive Information,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/312.html
313,Cleartext Storage in a File or on Disk,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/313.html
314,Cleartext Storage in the Registry,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/314.html
315,Cleartext Storage of Sensitive Information in a Cookie,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/315.html
316,Cleartext Storage of Sensitive Information in Memory,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/316.html
317,Cleartext Storage of Sensitive Information in GUI,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/317.html
318,Cleartext Storage of Sensitive Information in Executable,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/318.html
319,Cleartext Transmission of Sensitive Information,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/319.html
5,J2EE Misconfiguration: Data Transmission Without Encryption,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/5.html
614,Sensitive Cookie in HTTPS Session Without 'Secure' Attribute,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/614.html
326,Inadequate Encryption Strength,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/326.html
261,Weak Encoding for Password,326,Inadequate Encryption Strength,https://cwe.mitre.org/data/definitions/261.html
328,Reversible One-Way Hash,326,Inadequate Encryption Strength,https://cwe.mitre.org/data/definitions/328.html
261,Weak Encoding for Password,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/261.html
328,Reversible One-Way Hash,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/328.html
327,Use of a Broken or Risky Cryptographic Algorithm,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/327.html
1240,Use of a Risky Cryptographic Primitive,327,Use of a Broken or Risky Cryptographic Algorithm,https://cwe.mitre.org/data/definitions/1240.html
328,Reversible One-Way Hash,327,Use of a Broken or Risky Cryptographic Algorithm,https://cwe.mitre.org/data/definitions/328.html
780,Use of RSA Algorithm without OAEP,327,Use of a Broken or Risky Cryptographic Algorithm,https://cwe.mitre.org/data/definitions/780.html
916,Use of Password Hash With Insufficient Computational Effort,327,Use of a Broken or Risky Cryptographic Algorithm,https://cwe.mitre.org/data/definitions/916.html
759,Use of a One-Way Hash without a Salt,916,Use of Password Hash With Insufficient Computational Effort,https://cwe.mitre.org/data/definitions/759.html
760,Use of a One-Way Hash with a Predictable Salt,916,Use of Password Hash With Insufficient Computational Effort,https://cwe.mitre.org/data/definitions/760.html
759,Use of a One-Way Hash without a Salt,327,Use of a Broken or Risky Cryptographic Algorithm,https://cwe.mitre.org/data/definitions/759.html
760,Use of a One-Way Hash with a Predictable Salt,327,Use of a Broken or Risky Cryptographic Algorithm,https://cwe.mitre.org/data/definitions/760.html
1240,Use of a Risky Cryptographic Primitive,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1240.html
780,Use of RSA Algorithm without OAEP,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/780.html
916,Use of Password Hash With Insufficient Computational Effort,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/916.html
759,Use of a One-Way Hash without a Salt,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/759.html
760,Use of a One-Way Hash with a Predictable Salt,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/760.html
330,Use of Insufficiently Random Values,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/330.html
1204,Generation of Weak Initialization Vector (IV),330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/1204.html
329,Generation of Predictable IV with CBC Mode,1204,Generation of Weak Initialization Vector (IV),https://cwe.mitre.org/data/definitions/329.html
329,Generation of Predictable IV with CBC Mode,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/329.html
1241,Use of Predictable Algorithm in Random Number Generator,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/1241.html
331,Insufficient Entropy,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/331.html
332,Insufficient Entropy in PRNG,331,Insufficient Entropy,https://cwe.mitre.org/data/definitions/332.html
333,Improper Handling of Insufficient Entropy in TRNG,331,Insufficient Entropy,https://cwe.mitre.org/data/definitions/333.html
332,Insufficient Entropy in PRNG,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/332.html
333,Improper Handling of Insufficient Entropy in TRNG,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/333.html
334,Small Space of Random Values,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/334.html
6,J2EE Misconfiguration: Insufficient Session-ID Length,334,Small Space of Random Values,https://cwe.mitre.org/data/definitions/6.html
6,J2EE Misconfiguration: Insufficient Session-ID Length,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/6.html
335,Incorrect Usage of Seeds in Pseudo-Random Number Generator (PRNG),330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/335.html
336,Same Seed in Pseudo-Random Number Generator (PRNG),335,Incorrect Usage of Seeds in Pseudo-Random Number Generator (PRNG),https://cwe.mitre.org/data/definitions/336.html
337,Predictable Seed in Pseudo-Random Number Generator (PRNG),335,Incorrect Usage of Seeds in Pseudo-Random Number Generator (PRNG),https://cwe.mitre.org/data/definitions/337.html
339,Small Seed Space in PRNG,335,Incorrect Usage of Seeds in Pseudo-Random Number Generator (PRNG),https://cwe.mitre.org/data/definitions/339.html
336,Same Seed in Pseudo-Random Number Generator (PRNG),330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/336.html
337,Predictable Seed in Pseudo-Random Number Generator (PRNG),330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/337.html
339,Small Seed Space in PRNG,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/339.html
338,Use of Cryptographically Weak Pseudo-Random Number Generator (PRNG),330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/338.html
340,Generation of Predictable Numbers or Identifiers,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/340.html
341,Predictable from Observable State,340,Generation of Predictable Numbers or Identifiers,https://cwe.mitre.org/data/definitions/341.html
342,Predictable Exact Value from Previous Values,340,Generation of Predictable Numbers or Identifiers,https://cwe.mitre.org/data/definitions/342.html
343,Predictable Value Range from Previous Values,340,Generation of Predictable Numbers or Identifiers,https://cwe.mitre.org/data/definitions/343.html
341,Predictable from Observable State,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/341.html
342,Predictable Exact Value from Previous Values,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/342.html
343,Predictable Value Range from Previous Values,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/343.html
344,Use of Invariant Value in Dynamically Changing Context,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/344.html
323,"Reusing a Nonce, Key Pair in Encryption",344,Use of Invariant Value in Dynamically Changing Context,https://cwe.mitre.org/data/definitions/323.html
587,Assignment of a Fixed Address to a Pointer,344,Use of Invariant Value in Dynamically Changing Context,https://cwe.mitre.org/data/definitions/587.html
798,Use of Hard-coded Credentials,344,Use of Invariant Value in Dynamically Changing Context,https://cwe.mitre.org/data/definitions/798.html
259,Use of Hard-coded Password,344,Use of Invariant Value in Dynamically Changing Context,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,344,Use of Invariant Value in Dynamically Changing Context,https://cwe.mitre.org/data/definitions/321.html
323,"Reusing a Nonce, Key Pair in Encryption",330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/323.html
587,Assignment of a Fixed Address to a Pointer,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/587.html
798,Use of Hard-coded Credentials,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/798.html
259,Use of Hard-coded Password,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/321.html
804,Guessable CAPTCHA,330,Use of Insufficiently Random Values,https://cwe.mitre.org/data/definitions/804.html
1204,Generation of Weak Initialization Vector (IV),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1204.html
329,Generation of Predictable IV with CBC Mode,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/329.html
1241,Use of Predictable Algorithm in Random Number Generator,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1241.html
331,Insufficient Entropy,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/331.html
332,Insufficient Entropy in PRNG,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/332.html
333,Improper Handling of Insufficient Entropy in TRNG,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/333.html
334,Small Space of Random Values,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/334.html
6,J2EE Misconfiguration: Insufficient Session-ID Length,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/6.html
335,Incorrect Usage of Seeds in Pseudo-Random Number Generator (PRNG),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/335.html
336,Same Seed in Pseudo-Random Number Generator (PRNG),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/336.html
337,Predictable Seed in Pseudo-Random Number Generator (PRNG),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/337.html
339,Small Seed Space in PRNG,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/339.html
338,Use of Cryptographically Weak Pseudo-Random Number Generator (PRNG),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/338.html
340,Generation of Predictable Numbers or Identifiers,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/340.html
341,Predictable from Observable State,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/341.html
342,Predictable Exact Value from Previous Values,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/342.html
343,Predictable Value Range from Previous Values,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/343.html
344,Use of Invariant Value in Dynamically Changing Context,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/344.html
323,"Reusing a Nonce, Key Pair in Encryption",693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/323.html
587,Assignment of a Fixed Address to a Pointer,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/587.html
798,Use of Hard-coded Credentials,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/798.html
259,Use of Hard-coded Password,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/321.html
804,Guessable CAPTCHA,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/804.html
345,Insufficient Verification of Data Authenticity,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/345.html
1293,Missing Source Correlation of Multiple Independent Data,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/1293.html
346,Origin Validation Error,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/346.html
347,Improper Verification of Cryptographic Signature,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/347.html
348,Use of Less Trusted Source,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/348.html
349,Acceptance of Extraneous Untrusted Data With Trusted Data,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/349.html
351,Insufficient Type Distinction,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/351.html
352,Cross-Site Request Forgery (CSRF),345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/352.html
353,Missing Support for Integrity Check,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/353.html
354,Improper Validation of Integrity Check Value,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/354.html
360,Trust of System Event Data,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/360.html
422,Unprotected Windows Messaging Channel ('Shatter'),360,Trust of System Event Data,https://cwe.mitre.org/data/definitions/422.html
422,Unprotected Windows Messaging Channel ('Shatter'),345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/422.html
494,Download of Code Without Integrity Check,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/494.html
616,Incomplete Identification of Uploaded File Variables (PHP),345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/616.html
646,Reliance on File Name or Extension of Externally-Supplied File,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/646.html
649,Reliance on Obfuscation or Encryption of Security-Relevant Inputs without Integrity Checking,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/649.html
924,Improper Enforcement of Message Integrity During Transmission in a Communication Channel,345,Insufficient Verification of Data Authenticity,https://cwe.mitre.org/data/definitions/924.html
1293,Missing Source Correlation of Multiple Independent Data,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1293.html
346,Origin Validation Error,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/346.html
347,Improper Verification of Cryptographic Signature,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/347.html
348,Use of Less Trusted Source,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/348.html
349,Acceptance of Extraneous Untrusted Data With Trusted Data,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/349.html
351,Insufficient Type Distinction,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/351.html
352,Cross-Site Request Forgery (CSRF),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/352.html
353,Missing Support for Integrity Check,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/353.html
354,Improper Validation of Integrity Check Value,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/354.html
360,Trust of System Event Data,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/360.html
422,Unprotected Windows Messaging Channel ('Shatter'),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/422.html
494,Download of Code Without Integrity Check,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/494.html
616,Incomplete Identification of Uploaded File Variables (PHP),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/616.html
646,Reliance on File Name or Extension of Externally-Supplied File,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/646.html
649,Reliance on Obfuscation or Encryption of Security-Relevant Inputs without Integrity Checking,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/649.html
924,Improper Enforcement of Message Integrity During Transmission in a Communication Channel,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/924.html
357,Insufficient UI Warning of Dangerous Operations,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/357.html
450,Multiple Interpretations of UI Input,357,Insufficient UI Warning of Dangerous Operations,https://cwe.mitre.org/data/definitions/450.html
450,Multiple Interpretations of UI Input,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/450.html
358,Improperly Implemented Security Check for Standard,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/358.html
424,Improper Protection of Alternate Path,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/424.html
425,Direct Request ('Forced Browsing'),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/425.html
602,Client-Side Enforcement of Server-Side Security,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/602.html
565,Reliance on Cookies without Validation and Integrity Checking,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/565.html
784,Reliance on Cookies without Validation and Integrity Checking in a Security Decision,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/784.html
603,Use of Client-Side Authentication,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/603.html
653,Insufficient Compartmentalization,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/653.html
1331,Improper Isolation of Shared Resources in Network On Chip,653,Insufficient Compartmentalization,https://cwe.mitre.org/data/definitions/1331.html
1331,Improper Isolation of Shared Resources in Network On Chip,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/1331.html
654,Reliance on a Single Factor in a Security Decision,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/654.html
308,Use of Single-factor Authentication,654,Reliance on a Single Factor in a Security Decision,https://cwe.mitre.org/data/definitions/308.html
309,Use of Password System for Primary Authentication,654,Reliance on a Single Factor in a Security Decision,https://cwe.mitre.org/data/definitions/309.html
308,Use of Single-factor Authentication,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/308.html
309,Use of Password System for Primary Authentication,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/309.html
655,Insufficient Psychological Acceptability,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/655.html
656,Reliance on Security Through Obscurity,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/656.html
757,Selection of Less-Secure Algorithm During Negotiation ('Algorithm Downgrade'),693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/757.html
778,Insufficient Logging,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/778.html
807,Reliance on Untrusted Inputs in a Security Decision,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/807.html
302,Authentication Bypass by Assumed-Immutable Data,807,Reliance on Untrusted Inputs in a Security Decision,https://cwe.mitre.org/data/definitions/302.html
350,Reliance on Reverse DNS Resolution for a Security-Critical Action,807,Reliance on Untrusted Inputs in a Security Decision,https://cwe.mitre.org/data/definitions/350.html
784,Reliance on Cookies without Validation and Integrity Checking in a Security Decision,807,Reliance on Untrusted Inputs in a Security Decision,https://cwe.mitre.org/data/definitions/784.html
302,Authentication Bypass by Assumed-Immutable Data,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/302.html
350,Reliance on Reverse DNS Resolution for a Security-Critical Action,693,Protection Mechanism Failure,https://cwe.mitre.org/data/definitions/350.html
697,Incorrect Comparison,,,https://cwe.mitre.org/data/definitions/697.html
1023,Incomplete Comparison with Missing Factors,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/1023.html
184,Incomplete List of Disallowed Inputs,1023,Incomplete Comparison with Missing Factors,https://cwe.mitre.org/data/definitions/184.html
187,Partial String Comparison,1023,Incomplete Comparison with Missing Factors,https://cwe.mitre.org/data/definitions/187.html
478,Missing Default Case in Switch Statement,1023,Incomplete Comparison with Missing Factors,https://cwe.mitre.org/data/definitions/478.html
839,Numeric Range Comparison Without Minimum Check,1023,Incomplete Comparison with Missing Factors,https://cwe.mitre.org/data/definitions/839.html
184,Incomplete List of Disallowed Inputs,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/184.html
187,Partial String Comparison,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/187.html
478,Missing Default Case in Switch Statement,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/478.html
839,Numeric Range Comparison Without Minimum Check,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/839.html
1024,Comparison of Incompatible Types,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/1024.html
1025,Comparison Using Wrong Factors,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/1025.html
486,Comparison of Classes by Name,1025,Comparison Using Wrong Factors,https://cwe.mitre.org/data/definitions/486.html
595,Comparison of Object References Instead of Object Contents,1025,Comparison Using Wrong Factors,https://cwe.mitre.org/data/definitions/595.html
597,Use of Wrong Operator in String Comparison,595,Comparison of Object References Instead of Object Contents,https://cwe.mitre.org/data/definitions/597.html
597,Use of Wrong Operator in String Comparison,1025,Comparison Using Wrong Factors,https://cwe.mitre.org/data/definitions/597.html
486,Comparison of Classes by Name,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/486.html
595,Comparison of Object References Instead of Object Contents,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/595.html
597,Use of Wrong Operator in String Comparison,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/597.html
1039,Automated Recognition Mechanism with Inadequate Detection or Handling of Adversarial Input Perturbations,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/1039.html
1077,Floating Point Comparison with Incorrect Operator,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/1077.html
1254,Incorrect Comparison Logic Granularity,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/1254.html
183,Permissive List of Allowed Inputs,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/183.html
942,Permissive Cross-domain Policy with Untrusted Domains,183,Permissive List of Allowed Inputs,https://cwe.mitre.org/data/definitions/942.html
942,Permissive Cross-domain Policy with Untrusted Domains,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/942.html
185,Incorrect Regular Expression,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/185.html
1333,Inefficient Regular Expression Complexity,185,Incorrect Regular Expression,https://cwe.mitre.org/data/definitions/1333.html
186,Overly Restrictive Regular Expression,185,Incorrect Regular Expression,https://cwe.mitre.org/data/definitions/186.html
625,Permissive Regular Expression,185,Incorrect Regular Expression,https://cwe.mitre.org/data/definitions/625.html
777,Regular Expression without Anchors,625,Permissive Regular Expression,https://cwe.mitre.org/data/definitions/777.html
777,Regular Expression without Anchors,185,Incorrect Regular Expression,https://cwe.mitre.org/data/definitions/777.html
1333,Inefficient Regular Expression Complexity,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/1333.html
186,Overly Restrictive Regular Expression,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/186.html
625,Permissive Regular Expression,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/625.html
777,Regular Expression without Anchors,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/777.html
581,Object Model Violation: Just One of Equals and Hashcode Defined,697,Incorrect Comparison,https://cwe.mitre.org/data/definitions/581.html
703,Improper Check or Handling of Exceptional Conditions,,,https://cwe.mitre.org/data/definitions/703.html
1247,Missing or Improperly Implemented Protection Against Voltage and Clock Glitches,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/1247.html
1351,Improper Handling of Hardware Behavior in Exceptionally Cold Environments,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/1351.html
166,Improper Handling of Missing Special Element,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/166.html
167,Improper Handling of Additional Special Element,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/167.html
168,Improper Handling of Inconsistent Special Elements,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/168.html
228,Improper Handling of Syntactically Invalid Structure,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/228.html
229,Improper Handling of Values,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/229.html
230,Improper Handling of Missing Values,229,Improper Handling of Values,https://cwe.mitre.org/data/definitions/230.html
231,Improper Handling of Extra Values,229,Improper Handling of Values,https://cwe.mitre.org/data/definitions/231.html
232,Improper Handling of Undefined Values,229,Improper Handling of Values,https://cwe.mitre.org/data/definitions/232.html
230,Improper Handling of Missing Values,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/230.html
231,Improper Handling of Extra Values,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/231.html
232,Improper Handling of Undefined Values,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/232.html
233,Improper Handling of Parameters,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/233.html
234,Failure to Handle Missing Parameter,233,Improper Handling of Parameters,https://cwe.mitre.org/data/definitions/234.html
235,Improper Handling of Extra Parameters,233,Improper Handling of Parameters,https://cwe.mitre.org/data/definitions/235.html
236,Improper Handling of Undefined Parameters,233,Improper Handling of Parameters,https://cwe.mitre.org/data/definitions/236.html
234,Failure to Handle Missing Parameter,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/234.html
235,Improper Handling of Extra Parameters,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/235.html
236,Improper Handling of Undefined Parameters,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/236.html
237,Improper Handling of Structural Elements,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/237.html
238,Improper Handling of Incomplete Structural Elements,237,Improper Handling of Structural Elements,https://cwe.mitre.org/data/definitions/238.html
239,Failure to Handle Incomplete Element,237,Improper Handling of Structural Elements,https://cwe.mitre.org/data/definitions/239.html
240,Improper Handling of Inconsistent Structural Elements,237,Improper Handling of Structural Elements,https://cwe.mitre.org/data/definitions/240.html
130,Improper Handling of Length Parameter Inconsistency,240,Improper Handling of Inconsistent Structural Elements,https://cwe.mitre.org/data/definitions/130.html
130,Improper Handling of Length Parameter Inconsistency,237,Improper Handling of Structural Elements,https://cwe.mitre.org/data/definitions/130.html
238,Improper Handling of Incomplete Structural Elements,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/238.html
239,Failure to Handle Incomplete Element,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/239.html
240,Improper Handling of Inconsistent Structural Elements,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/240.html
130,Improper Handling of Length Parameter Inconsistency,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/130.html
241,Improper Handling of Unexpected Data Type,228,Improper Handling of Syntactically Invalid Structure,https://cwe.mitre.org/data/definitions/241.html
229,Improper Handling of Values,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/229.html
230,Improper Handling of Missing Values,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/230.html
231,Improper Handling of Extra Values,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/231.html
232,Improper Handling of Undefined Values,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/232.html
233,Improper Handling of Parameters,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/233.html
234,Failure to Handle Missing Parameter,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/234.html
235,Improper Handling of Extra Parameters,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/235.html
236,Improper Handling of Undefined Parameters,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/236.html
237,Improper Handling of Structural Elements,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/237.html
238,Improper Handling of Incomplete Structural Elements,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/238.html
239,Failure to Handle Incomplete Element,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/239.html
240,Improper Handling of Inconsistent Structural Elements,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/240.html
130,Improper Handling of Length Parameter Inconsistency,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/130.html
241,Improper Handling of Unexpected Data Type,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/241.html
248,Uncaught Exception,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/248.html
600,Uncaught Exception in Servlet ,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/600.html
274,Improper Handling of Insufficient Privileges,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/274.html
333,Improper Handling of Insufficient Entropy in TRNG,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/333.html
392,Missing Report of Error Condition,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/392.html
393,Return of Wrong Status Code,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/393.html
397,Declaration of Throws for Generic Exception,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/397.html
754,Improper Check for Unusual or Exceptional Conditions,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/754.html
252,Unchecked Return Value,754,Improper Check for Unusual or Exceptional Conditions,https://cwe.mitre.org/data/definitions/252.html
253,Incorrect Check of Function Return Value,754,Improper Check for Unusual or Exceptional Conditions,https://cwe.mitre.org/data/definitions/253.html
273,Improper Check for Dropped Privileges,754,Improper Check for Unusual or Exceptional Conditions,https://cwe.mitre.org/data/definitions/273.html
354,Improper Validation of Integrity Check Value,754,Improper Check for Unusual or Exceptional Conditions,https://cwe.mitre.org/data/definitions/354.html
391,Unchecked Error Condition,754,Improper Check for Unusual or Exceptional Conditions,https://cwe.mitre.org/data/definitions/391.html
394,Unexpected Status Code or Return Value,754,Improper Check for Unusual or Exceptional Conditions,https://cwe.mitre.org/data/definitions/394.html
476,NULL Pointer Dereference,754,Improper Check for Unusual or Exceptional Conditions,https://cwe.mitre.org/data/definitions/476.html
690,Unchecked Return Value to NULL Pointer Dereference,476,NULL Pointer Dereference,https://cwe.mitre.org/data/definitions/690.html
690,Unchecked Return Value to NULL Pointer Dereference,754,Improper Check for Unusual or Exceptional Conditions,https://cwe.mitre.org/data/definitions/690.html
252,Unchecked Return Value,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/252.html
253,Incorrect Check of Function Return Value,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/253.html
273,Improper Check for Dropped Privileges,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/273.html
354,Improper Validation of Integrity Check Value,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/354.html
391,Unchecked Error Condition,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/391.html
394,Unexpected Status Code or Return Value,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/394.html
476,NULL Pointer Dereference,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/476.html
690,Unchecked Return Value to NULL Pointer Dereference,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/690.html
755,Improper Handling of Exceptional Conditions,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/755.html
1261,Improper Handling of Single Event Upsets,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/1261.html
209,Generation of Error Message Containing Sensitive Information,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/209.html
210,Self-generated Error Message Containing Sensitive Information,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/210.html
211,Externally-Generated Error Message Containing Sensitive Information,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/211.html
535,Exposure of Information Through Shell Error Message,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/535.html
536,Servlet Runtime Error Message Containing Sensitive Information,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/536.html
537,Java Runtime Error Message Containing Sensitive Information,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/537.html
550,Server-generated Error Message Containing Sensitive Information,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/550.html
280,Improper Handling of Insufficient Permissions or Privileges ,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/280.html
390,Detection of Error Condition Without Action,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/390.html
395,Use of NullPointerException Catch to Detect NULL Pointer Dereference,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/395.html
396,Declaration of Catch for Generic Exception,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/396.html
460,Improper Cleanup on Thrown Exception,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/460.html
544,Missing Standardized Error Handling Mechanism,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/544.html
636,Not Failing Securely ('Failing Open'),755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/636.html
455,Non-exit on Failed Initialization,636,Not Failing Securely ('Failing Open'),https://cwe.mitre.org/data/definitions/455.html
455,Non-exit on Failed Initialization,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/455.html
756,Missing Custom Error Page,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/756.html
12,ASP.NET Misconfiguration: Missing Custom Error Page,756,Missing Custom Error Page,https://cwe.mitre.org/data/definitions/12.html
7,J2EE Misconfiguration: Missing Custom Error Page,756,Missing Custom Error Page,https://cwe.mitre.org/data/definitions/7.html
12,ASP.NET Misconfiguration: Missing Custom Error Page,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/12.html
7,J2EE Misconfiguration: Missing Custom Error Page,755,Improper Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/7.html
1261,Improper Handling of Single Event Upsets,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/1261.html
209,Generation of Error Message Containing Sensitive Information,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/209.html
210,Self-generated Error Message Containing Sensitive Information,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/210.html
211,Externally-Generated Error Message Containing Sensitive Information,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/211.html
535,Exposure of Information Through Shell Error Message,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/535.html
536,Servlet Runtime Error Message Containing Sensitive Information,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/536.html
537,Java Runtime Error Message Containing Sensitive Information,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/537.html
550,Server-generated Error Message Containing Sensitive Information,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/550.html
280,Improper Handling of Insufficient Permissions or Privileges ,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/280.html
390,Detection of Error Condition Without Action,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/390.html
395,Use of NullPointerException Catch to Detect NULL Pointer Dereference,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/395.html
396,Declaration of Catch for Generic Exception,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/396.html
460,Improper Cleanup on Thrown Exception,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/460.html
544,Missing Standardized Error Handling Mechanism,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/544.html
636,Not Failing Securely ('Failing Open'),703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/636.html
455,Non-exit on Failed Initialization,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/455.html
756,Missing Custom Error Page,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/756.html
12,ASP.NET Misconfiguration: Missing Custom Error Page,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/12.html
7,J2EE Misconfiguration: Missing Custom Error Page,703,Improper Check or Handling of Exceptional Conditions,https://cwe.mitre.org/data/definitions/7.html
707,Improper Neutralization,,,https://cwe.mitre.org/data/definitions/707.html
116,Improper Encoding or Escaping of Output,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/116.html
117,Improper Output Neutralization for Logs,116,Improper Encoding or Escaping of Output,https://cwe.mitre.org/data/definitions/117.html
644,Improper Neutralization of HTTP Headers for Scripting Syntax,116,Improper Encoding or Escaping of Output,https://cwe.mitre.org/data/definitions/644.html
838,Inappropriate Encoding for Output Context,116,Improper Encoding or Escaping of Output,https://cwe.mitre.org/data/definitions/838.html
117,Improper Output Neutralization for Logs,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/117.html
644,Improper Neutralization of HTTP Headers for Scripting Syntax,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/644.html
838,Inappropriate Encoding for Output Context,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/838.html
138,Improper Neutralization of Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/138.html
140,Improper Neutralization of Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/140.html
141,Improper Neutralization of Parameter/Argument Delimiters,140,Improper Neutralization of Delimiters,https://cwe.mitre.org/data/definitions/141.html
142,Improper Neutralization of Value Delimiters,140,Improper Neutralization of Delimiters,https://cwe.mitre.org/data/definitions/142.html
143,Improper Neutralization of Record Delimiters,140,Improper Neutralization of Delimiters,https://cwe.mitre.org/data/definitions/143.html
144,Improper Neutralization of Line Delimiters,140,Improper Neutralization of Delimiters,https://cwe.mitre.org/data/definitions/144.html
145,Improper Neutralization of Section Delimiters,140,Improper Neutralization of Delimiters,https://cwe.mitre.org/data/definitions/145.html
146,Improper Neutralization of Expression/Command Delimiters,140,Improper Neutralization of Delimiters,https://cwe.mitre.org/data/definitions/146.html
141,Improper Neutralization of Parameter/Argument Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/141.html
142,Improper Neutralization of Value Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/142.html
143,Improper Neutralization of Record Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/143.html
144,Improper Neutralization of Line Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/144.html
145,Improper Neutralization of Section Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/145.html
146,Improper Neutralization of Expression/Command Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/146.html
147,Improper Neutralization of Input Terminators,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/147.html
626,Null Byte Interaction Error (Poison Null Byte),147,Improper Neutralization of Input Terminators,https://cwe.mitre.org/data/definitions/626.html
626,Null Byte Interaction Error (Poison Null Byte),138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/626.html
148,Improper Neutralization of Input Leaders,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/148.html
149,Improper Neutralization of Quoting Syntax,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/149.html
150,"Improper Neutralization of Escape, Meta, or Control Sequences",138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/150.html
151,Improper Neutralization of Comment Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/151.html
152,Improper Neutralization of Macro Symbols,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/152.html
153,Improper Neutralization of Substitution Characters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/153.html
154,Improper Neutralization of Variable Name Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/154.html
155,Improper Neutralization of Wildcards or Matching Symbols,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/155.html
56,Path Equivalence: 'filedir*' (Wildcard),155,Improper Neutralization of Wildcards or Matching Symbols,https://cwe.mitre.org/data/definitions/56.html
56,Path Equivalence: 'filedir*' (Wildcard),138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/56.html
156,Improper Neutralization of Whitespace,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/156.html
157,Failure to Sanitize Paired Delimiters,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/157.html
158,Improper Neutralization of Null Byte or NUL Character,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/158.html
159,Improper Handling of Invalid Use of Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/159.html
166,Improper Handling of Missing Special Element,159,Improper Handling of Invalid Use of Special Elements,https://cwe.mitre.org/data/definitions/166.html
167,Improper Handling of Additional Special Element,159,Improper Handling of Invalid Use of Special Elements,https://cwe.mitre.org/data/definitions/167.html
168,Improper Handling of Inconsistent Special Elements,159,Improper Handling of Invalid Use of Special Elements,https://cwe.mitre.org/data/definitions/168.html
166,Improper Handling of Missing Special Element,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/166.html
167,Improper Handling of Additional Special Element,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/167.html
168,Improper Handling of Inconsistent Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/168.html
160,Improper Neutralization of Leading Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/160.html
161,Improper Neutralization of Multiple Leading Special Elements,160,Improper Neutralization of Leading Special Elements,https://cwe.mitre.org/data/definitions/161.html
50,Path Equivalence: '//multiple/leading/slash',161,Improper Neutralization of Multiple Leading Special Elements,https://cwe.mitre.org/data/definitions/50.html
50,Path Equivalence: '//multiple/leading/slash',160,Improper Neutralization of Leading Special Elements,https://cwe.mitre.org/data/definitions/50.html
37,Path Traversal: '/absolute/pathname/here',160,Improper Neutralization of Leading Special Elements,https://cwe.mitre.org/data/definitions/37.html
161,Improper Neutralization of Multiple Leading Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/161.html
50,Path Equivalence: '//multiple/leading/slash',138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/50.html
37,Path Traversal: '/absolute/pathname/here',138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/37.html
162,Improper Neutralization of Trailing Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/162.html
163,Improper Neutralization of Multiple Trailing Special Elements,162,Improper Neutralization of Trailing Special Elements,https://cwe.mitre.org/data/definitions/163.html
43,Path Equivalence: 'filename....' (Multiple Trailing Dot),163,Improper Neutralization of Multiple Trailing Special Elements,https://cwe.mitre.org/data/definitions/43.html
52,Path Equivalence: '/multiple/trailing/slash//',163,Improper Neutralization of Multiple Trailing Special Elements,https://cwe.mitre.org/data/definitions/52.html
43,Path Equivalence: 'filename....' (Multiple Trailing Dot),162,Improper Neutralization of Trailing Special Elements,https://cwe.mitre.org/data/definitions/43.html
52,Path Equivalence: '/multiple/trailing/slash//',162,Improper Neutralization of Trailing Special Elements,https://cwe.mitre.org/data/definitions/52.html
42,Path Equivalence: 'filename.' (Trailing Dot),162,Improper Neutralization of Trailing Special Elements,https://cwe.mitre.org/data/definitions/42.html
46,Path Equivalence: 'filename ' (Trailing Space),162,Improper Neutralization of Trailing Special Elements,https://cwe.mitre.org/data/definitions/46.html
49,Path Equivalence: 'filename/' (Trailing Slash),162,Improper Neutralization of Trailing Special Elements,https://cwe.mitre.org/data/definitions/49.html
54,Path Equivalence: 'filedir\' (Trailing Backslash),162,Improper Neutralization of Trailing Special Elements,https://cwe.mitre.org/data/definitions/54.html
163,Improper Neutralization of Multiple Trailing Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/163.html
43,Path Equivalence: 'filename....' (Multiple Trailing Dot),138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/43.html
52,Path Equivalence: '/multiple/trailing/slash//',138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/52.html
42,Path Equivalence: 'filename.' (Trailing Dot),138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/42.html
46,Path Equivalence: 'filename ' (Trailing Space),138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/46.html
49,Path Equivalence: 'filename/' (Trailing Slash),138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/49.html
54,Path Equivalence: 'filedir\' (Trailing Backslash),138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/54.html
164,Improper Neutralization of Internal Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/164.html
165,Improper Neutralization of Multiple Internal Special Elements,164,Improper Neutralization of Internal Special Elements,https://cwe.mitre.org/data/definitions/165.html
45,Path Equivalence: 'file...name' (Multiple Internal Dot),165,Improper Neutralization of Multiple Internal Special Elements,https://cwe.mitre.org/data/definitions/45.html
53,Path Equivalence: '\multiple\\internal\backslash',165,Improper Neutralization of Multiple Internal Special Elements,https://cwe.mitre.org/data/definitions/53.html
45,Path Equivalence: 'file...name' (Multiple Internal Dot),164,Improper Neutralization of Internal Special Elements,https://cwe.mitre.org/data/definitions/45.html
53,Path Equivalence: '\multiple\\internal\backslash',164,Improper Neutralization of Internal Special Elements,https://cwe.mitre.org/data/definitions/53.html
165,Improper Neutralization of Multiple Internal Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/165.html
45,Path Equivalence: 'file...name' (Multiple Internal Dot),138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/45.html
53,Path Equivalence: '\multiple\\internal\backslash',138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/53.html
464,Addition of Data Structure Sentinel,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/464.html
790,Improper Filtering of Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/790.html
791,Incomplete Filtering of Special Elements,790,Improper Filtering of Special Elements,https://cwe.mitre.org/data/definitions/791.html
792,Incomplete Filtering of One or More Instances of Special Elements,791,Incomplete Filtering of Special Elements,https://cwe.mitre.org/data/definitions/792.html
793,Only Filtering One Instance of a Special Element,792,Incomplete Filtering of One or More Instances of Special Elements,https://cwe.mitre.org/data/definitions/793.html
794,Incomplete Filtering of Multiple Instances of Special Elements,792,Incomplete Filtering of One or More Instances of Special Elements,https://cwe.mitre.org/data/definitions/794.html
793,Only Filtering One Instance of a Special Element,791,Incomplete Filtering of Special Elements,https://cwe.mitre.org/data/definitions/793.html
794,Incomplete Filtering of Multiple Instances of Special Elements,791,Incomplete Filtering of Special Elements,https://cwe.mitre.org/data/definitions/794.html
795,Only Filtering Special Elements at a Specified Location,791,Incomplete Filtering of Special Elements,https://cwe.mitre.org/data/definitions/795.html
796,Only Filtering Special Elements Relative to a Marker,795,Only Filtering Special Elements at a Specified Location,https://cwe.mitre.org/data/definitions/796.html
797,Only Filtering Special Elements at an Absolute Position,795,Only Filtering Special Elements at a Specified Location,https://cwe.mitre.org/data/definitions/797.html
796,Only Filtering Special Elements Relative to a Marker,791,Incomplete Filtering of Special Elements,https://cwe.mitre.org/data/definitions/796.html
797,Only Filtering Special Elements at an Absolute Position,791,Incomplete Filtering of Special Elements,https://cwe.mitre.org/data/definitions/797.html
792,Incomplete Filtering of One or More Instances of Special Elements,790,Improper Filtering of Special Elements,https://cwe.mitre.org/data/definitions/792.html
793,Only Filtering One Instance of a Special Element,790,Improper Filtering of Special Elements,https://cwe.mitre.org/data/definitions/793.html
794,Incomplete Filtering of Multiple Instances of Special Elements,790,Improper Filtering of Special Elements,https://cwe.mitre.org/data/definitions/794.html
795,Only Filtering Special Elements at a Specified Location,790,Improper Filtering of Special Elements,https://cwe.mitre.org/data/definitions/795.html
796,Only Filtering Special Elements Relative to a Marker,790,Improper Filtering of Special Elements,https://cwe.mitre.org/data/definitions/796.html
797,Only Filtering Special Elements at an Absolute Position,790,Improper Filtering of Special Elements,https://cwe.mitre.org/data/definitions/797.html
791,Incomplete Filtering of Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/791.html
792,Incomplete Filtering of One or More Instances of Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/792.html
793,Only Filtering One Instance of a Special Element,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/793.html
794,Incomplete Filtering of Multiple Instances of Special Elements,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/794.html
795,Only Filtering Special Elements at a Specified Location,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/795.html
796,Only Filtering Special Elements Relative to a Marker,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/796.html
797,Only Filtering Special Elements at an Absolute Position,138,Improper Neutralization of Special Elements,https://cwe.mitre.org/data/definitions/797.html
140,Improper Neutralization of Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/140.html
141,Improper Neutralization of Parameter/Argument Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/141.html
142,Improper Neutralization of Value Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/142.html
143,Improper Neutralization of Record Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/143.html
144,Improper Neutralization of Line Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/144.html
145,Improper Neutralization of Section Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/145.html
146,Improper Neutralization of Expression/Command Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/146.html
147,Improper Neutralization of Input Terminators,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/147.html
626,Null Byte Interaction Error (Poison Null Byte),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/626.html
148,Improper Neutralization of Input Leaders,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/148.html
149,Improper Neutralization of Quoting Syntax,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/149.html
150,"Improper Neutralization of Escape, Meta, or Control Sequences",707,Improper Neutralization,https://cwe.mitre.org/data/definitions/150.html
151,Improper Neutralization of Comment Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/151.html
152,Improper Neutralization of Macro Symbols,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/152.html
153,Improper Neutralization of Substitution Characters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/153.html
154,Improper Neutralization of Variable Name Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/154.html
155,Improper Neutralization of Wildcards or Matching Symbols,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/155.html
56,Path Equivalence: 'filedir*' (Wildcard),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/56.html
156,Improper Neutralization of Whitespace,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/156.html
157,Failure to Sanitize Paired Delimiters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/157.html
158,Improper Neutralization of Null Byte or NUL Character,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/158.html
159,Improper Handling of Invalid Use of Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/159.html
166,Improper Handling of Missing Special Element,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/166.html
167,Improper Handling of Additional Special Element,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/167.html
168,Improper Handling of Inconsistent Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/168.html
160,Improper Neutralization of Leading Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/160.html
161,Improper Neutralization of Multiple Leading Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/161.html
50,Path Equivalence: '//multiple/leading/slash',707,Improper Neutralization,https://cwe.mitre.org/data/definitions/50.html
37,Path Traversal: '/absolute/pathname/here',707,Improper Neutralization,https://cwe.mitre.org/data/definitions/37.html
162,Improper Neutralization of Trailing Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/162.html
163,Improper Neutralization of Multiple Trailing Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/163.html
43,Path Equivalence: 'filename....' (Multiple Trailing Dot),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/43.html
52,Path Equivalence: '/multiple/trailing/slash//',707,Improper Neutralization,https://cwe.mitre.org/data/definitions/52.html
42,Path Equivalence: 'filename.' (Trailing Dot),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/42.html
46,Path Equivalence: 'filename ' (Trailing Space),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/46.html
49,Path Equivalence: 'filename/' (Trailing Slash),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/49.html
54,Path Equivalence: 'filedir\' (Trailing Backslash),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/54.html
164,Improper Neutralization of Internal Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/164.html
165,Improper Neutralization of Multiple Internal Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/165.html
45,Path Equivalence: 'file...name' (Multiple Internal Dot),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/45.html
53,Path Equivalence: '\multiple\\internal\backslash',707,Improper Neutralization,https://cwe.mitre.org/data/definitions/53.html
464,Addition of Data Structure Sentinel,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/464.html
790,Improper Filtering of Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/790.html
791,Incomplete Filtering of Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/791.html
792,Incomplete Filtering of One or More Instances of Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/792.html
793,Only Filtering One Instance of a Special Element,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/793.html
794,Incomplete Filtering of Multiple Instances of Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/794.html
795,Only Filtering Special Elements at a Specified Location,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/795.html
796,Only Filtering Special Elements Relative to a Marker,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/796.html
797,Only Filtering Special Elements at an Absolute Position,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/797.html
170,Improper Null Termination,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/170.html
172,Encoding Error,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/172.html
173,Improper Handling of Alternate Encoding,172,Encoding Error,https://cwe.mitre.org/data/definitions/173.html
174,Double Decoding of the Same Data,172,Encoding Error,https://cwe.mitre.org/data/definitions/174.html
175,Improper Handling of Mixed Encoding,172,Encoding Error,https://cwe.mitre.org/data/definitions/175.html
176,Improper Handling of Unicode Encoding,172,Encoding Error,https://cwe.mitre.org/data/definitions/176.html
177,Improper Handling of URL Encoding (Hex Encoding),172,Encoding Error,https://cwe.mitre.org/data/definitions/177.html
173,Improper Handling of Alternate Encoding,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/173.html
174,Double Decoding of the Same Data,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/174.html
175,Improper Handling of Mixed Encoding,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/175.html
176,Improper Handling of Unicode Encoding,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/176.html
177,Improper Handling of URL Encoding (Hex Encoding),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/177.html
20,Improper Input Validation,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/20.html
1173,Improper Use of Validation Framework,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/1173.html
102,Struts: Duplicate Validation Forms,1173,Improper Use of Validation Framework,https://cwe.mitre.org/data/definitions/102.html
105,Struts: Form Field Without Validator,1173,Improper Use of Validation Framework,https://cwe.mitre.org/data/definitions/105.html
106,Struts: Plug-in Framework not in Use,1173,Improper Use of Validation Framework,https://cwe.mitre.org/data/definitions/106.html
108,Struts: Unvalidated Action Form,1173,Improper Use of Validation Framework,https://cwe.mitre.org/data/definitions/108.html
109,Struts: Validator Turned Off,1173,Improper Use of Validation Framework,https://cwe.mitre.org/data/definitions/109.html
1174,ASP.NET Misconfiguration: Improper Model Validation,1173,Improper Use of Validation Framework,https://cwe.mitre.org/data/definitions/1174.html
554,ASP.NET Misconfiguration: Not Using Input Validation Framework,1173,Improper Use of Validation Framework,https://cwe.mitre.org/data/definitions/554.html
102,Struts: Duplicate Validation Forms,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/102.html
105,Struts: Form Field Without Validator,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/105.html
106,Struts: Plug-in Framework not in Use,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/106.html
108,Struts: Unvalidated Action Form,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/108.html
109,Struts: Validator Turned Off,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/109.html
1174,ASP.NET Misconfiguration: Improper Model Validation,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/1174.html
554,ASP.NET Misconfiguration: Not Using Input Validation Framework,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/554.html
1284,Improper Validation of Specified Quantity in Input,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/1284.html
606,Unchecked Input for Loop Condition,1284,Improper Validation of Specified Quantity in Input,https://cwe.mitre.org/data/definitions/606.html
789,Memory Allocation with Excessive Size Value,1284,Improper Validation of Specified Quantity in Input,https://cwe.mitre.org/data/definitions/789.html
606,Unchecked Input for Loop Condition,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/606.html
789,Memory Allocation with Excessive Size Value,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/789.html
1285,"Improper Validation of Specified Index, Position, or Offset in Input",20,Improper Input Validation,https://cwe.mitre.org/data/definitions/1285.html
129,Improper Validation of Array Index,1285,"Improper Validation of Specified Index, Position, or Offset in Input",https://cwe.mitre.org/data/definitions/129.html
781,Improper Address Validation in IOCTL with METHOD_NEITHER I/O Control Code,1285,"Improper Validation of Specified Index, Position, or Offset in Input",https://cwe.mitre.org/data/definitions/781.html
129,Improper Validation of Array Index,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/129.html
781,Improper Address Validation in IOCTL with METHOD_NEITHER I/O Control Code,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/781.html
1286,Improper Validation of Syntactic Correctness of Input,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/1286.html
112,Missing XML Validation,1286,Improper Validation of Syntactic Correctness of Input,https://cwe.mitre.org/data/definitions/112.html
112,Missing XML Validation,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/112.html
1287,Improper Validation of Specified Type of Input,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/1287.html
1288,Improper Validation of Consistency within Input,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/1288.html
1289,Improper Validation of Unsafe Equivalence in Input,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/1289.html
179,Incorrect Behavior Order: Early Validation,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/179.html
180,Incorrect Behavior Order: Validate Before Canonicalize,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/180.html
181,Incorrect Behavior Order: Validate Before Filter,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/181.html
622,Improper Validation of Function Hook Arguments,20,Improper Input Validation,https://cwe.mitre.org/data/definitions/622.html
1173,Improper Use of Validation Framework,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1173.html
102,Struts: Duplicate Validation Forms,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/102.html
105,Struts: Form Field Without Validator,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/105.html
106,Struts: Plug-in Framework not in Use,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/106.html
108,Struts: Unvalidated Action Form,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/108.html
109,Struts: Validator Turned Off,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/109.html
1174,ASP.NET Misconfiguration: Improper Model Validation,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1174.html
554,ASP.NET Misconfiguration: Not Using Input Validation Framework,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/554.html
1284,Improper Validation of Specified Quantity in Input,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1284.html
606,Unchecked Input for Loop Condition,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/606.html
789,Memory Allocation with Excessive Size Value,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/789.html
1285,"Improper Validation of Specified Index, Position, or Offset in Input",707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1285.html
129,Improper Validation of Array Index,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/129.html
781,Improper Address Validation in IOCTL with METHOD_NEITHER I/O Control Code,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/781.html
1286,Improper Validation of Syntactic Correctness of Input,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1286.html
112,Missing XML Validation,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/112.html
1287,Improper Validation of Specified Type of Input,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1287.html
1288,Improper Validation of Consistency within Input,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1288.html
1289,Improper Validation of Unsafe Equivalence in Input,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1289.html
179,Incorrect Behavior Order: Early Validation,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/179.html
180,Incorrect Behavior Order: Validate Before Canonicalize,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/180.html
181,Incorrect Behavior Order: Validate Before Filter,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/181.html
622,Improper Validation of Function Hook Arguments,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/622.html
228,Improper Handling of Syntactically Invalid Structure,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/228.html
229,Improper Handling of Values,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/229.html
230,Improper Handling of Missing Values,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/230.html
231,Improper Handling of Extra Values,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/231.html
232,Improper Handling of Undefined Values,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/232.html
233,Improper Handling of Parameters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/233.html
234,Failure to Handle Missing Parameter,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/234.html
235,Improper Handling of Extra Parameters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/235.html
236,Improper Handling of Undefined Parameters,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/236.html
237,Improper Handling of Structural Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/237.html
238,Improper Handling of Incomplete Structural Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/238.html
239,Failure to Handle Incomplete Element,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/239.html
240,Improper Handling of Inconsistent Structural Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/240.html
130,Improper Handling of Length Parameter Inconsistency,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/130.html
241,Improper Handling of Unexpected Data Type,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/241.html
463,Deletion of Data Structure Sentinel,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/463.html
74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/74.html
1236,Improper Neutralization of Formula Elements in a CSV File,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/1236.html
75,Failure to Sanitize Special Elements into a Different Plane (Special Element Injection),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/75.html
76,Improper Neutralization of Equivalent Special Elements,75,Failure to Sanitize Special Elements into a Different Plane (Special Element Injection),https://cwe.mitre.org/data/definitions/76.html
76,Improper Neutralization of Equivalent Special Elements,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/76.html
77,Improper Neutralization of Special Elements used in a Command ('Command Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/77.html
624,Executable Regular Expression Error,77,Improper Neutralization of Special Elements used in a Command ('Command Injection'),https://cwe.mitre.org/data/definitions/624.html
78,Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection'),77,Improper Neutralization of Special Elements used in a Command ('Command Injection'),https://cwe.mitre.org/data/definitions/78.html
88,Improper Neutralization of Argument Delimiters in a Command ('Argument Injection'),77,Improper Neutralization of Special Elements used in a Command ('Command Injection'),https://cwe.mitre.org/data/definitions/88.html
917,Improper Neutralization of Special Elements used in an Expression Language Statement ('Expression Language Injection'),77,Improper Neutralization of Special Elements used in a Command ('Command Injection'),https://cwe.mitre.org/data/definitions/917.html
624,Executable Regular Expression Error,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/624.html
78,Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/78.html
88,Improper Neutralization of Argument Delimiters in a Command ('Argument Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/88.html
917,Improper Neutralization of Special Elements used in an Expression Language Statement ('Expression Language Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/917.html
79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/79.html
692,Incomplete Denylist to Cross-Site Scripting,79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/692.html
80,Improper Neutralization of Script-Related HTML Tags in a Web Page (Basic XSS),79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/80.html
81,Improper Neutralization of Script in an Error Message Web Page,79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/81.html
83,Improper Neutralization of Script in Attributes in a Web Page,79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/83.html
82,Improper Neutralization of Script in Attributes of IMG Tags in a Web Page,83,Improper Neutralization of Script in Attributes in a Web Page,https://cwe.mitre.org/data/definitions/82.html
82,Improper Neutralization of Script in Attributes of IMG Tags in a Web Page,79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/82.html
84,Improper Neutralization of Encoded URI Schemes in a Web Page,79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/84.html
85,Doubled Character XSS Manipulations,79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/85.html
86,Improper Neutralization of Invalid Characters in Identifiers in Web Pages,79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/86.html
87,Improper Neutralization of Alternate XSS Syntax,79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),https://cwe.mitre.org/data/definitions/87.html
692,Incomplete Denylist to Cross-Site Scripting,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/692.html
80,Improper Neutralization of Script-Related HTML Tags in a Web Page (Basic XSS),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/80.html
81,Improper Neutralization of Script in an Error Message Web Page,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/81.html
83,Improper Neutralization of Script in Attributes in a Web Page,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/83.html
82,Improper Neutralization of Script in Attributes of IMG Tags in a Web Page,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/82.html
84,Improper Neutralization of Encoded URI Schemes in a Web Page,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/84.html
85,Doubled Character XSS Manipulations,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/85.html
86,Improper Neutralization of Invalid Characters in Identifiers in Web Pages,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/86.html
87,Improper Neutralization of Alternate XSS Syntax,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/87.html
91,XML Injection (aka Blind XPath Injection),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/91.html
643,Improper Neutralization of Data within XPath Expressions ('XPath Injection'),91,XML Injection (aka Blind XPath Injection),https://cwe.mitre.org/data/definitions/643.html
652,Improper Neutralization of Data within XQuery Expressions ('XQuery Injection'),91,XML Injection (aka Blind XPath Injection),https://cwe.mitre.org/data/definitions/652.html
643,Improper Neutralization of Data within XPath Expressions ('XPath Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/643.html
652,Improper Neutralization of Data within XQuery Expressions ('XQuery Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/652.html
93,Improper Neutralization of CRLF Sequences ('CRLF Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/93.html
113,Improper Neutralization of CRLF Sequences in HTTP Headers ('HTTP Response Splitting'),93,Improper Neutralization of CRLF Sequences ('CRLF Injection'),https://cwe.mitre.org/data/definitions/113.html
113,Improper Neutralization of CRLF Sequences in HTTP Headers ('HTTP Response Splitting'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/113.html
94,Improper Control of Generation of Code ('Code Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/94.html
1336,Improper Neutralization of Special Elements Used in a Template Engine,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/1336.html
95,Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/95.html
96,Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/96.html
97,Improper Neutralization of Server-Side Includes (SSI) Within a Web Page,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/97.html
943,Improper Neutralization of Special Elements in Data Query Logic,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/943.html
643,Improper Neutralization of Data within XPath Expressions ('XPath Injection'),943,Improper Neutralization of Special Elements in Data Query Logic,https://cwe.mitre.org/data/definitions/643.html
652,Improper Neutralization of Data within XQuery Expressions ('XQuery Injection'),943,Improper Neutralization of Special Elements in Data Query Logic,https://cwe.mitre.org/data/definitions/652.html
89,Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection'),943,Improper Neutralization of Special Elements in Data Query Logic,https://cwe.mitre.org/data/definitions/89.html
564,SQL Injection: Hibernate,89,Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection'),https://cwe.mitre.org/data/definitions/564.html
564,SQL Injection: Hibernate,943,Improper Neutralization of Special Elements in Data Query Logic,https://cwe.mitre.org/data/definitions/564.html
90,Improper Neutralization of Special Elements used in an LDAP Query ('LDAP Injection'),943,Improper Neutralization of Special Elements in Data Query Logic,https://cwe.mitre.org/data/definitions/90.html
89,Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/89.html
564,SQL Injection: Hibernate,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/564.html
90,Improper Neutralization of Special Elements used in an LDAP Query ('LDAP Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/90.html
99,Improper Control of Resource Identifiers ('Resource Injection'),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/99.html
641,Improper Restriction of Names for Files and Other Resources,99,Improper Control of Resource Identifiers ('Resource Injection'),https://cwe.mitre.org/data/definitions/641.html
694,Use of Multiple Resources with Duplicate Identifier,99,Improper Control of Resource Identifiers ('Resource Injection'),https://cwe.mitre.org/data/definitions/694.html
102,Struts: Duplicate Validation Forms,694,Use of Multiple Resources with Duplicate Identifier,https://cwe.mitre.org/data/definitions/102.html
462,Duplicate Key in Associative List (Alist),694,Use of Multiple Resources with Duplicate Identifier,https://cwe.mitre.org/data/definitions/462.html
102,Struts: Duplicate Validation Forms,99,Improper Control of Resource Identifiers ('Resource Injection'),https://cwe.mitre.org/data/definitions/102.html
462,Duplicate Key in Associative List (Alist),99,Improper Control of Resource Identifiers ('Resource Injection'),https://cwe.mitre.org/data/definitions/462.html
914,Improper Control of Dynamically-Identified Variables,99,Improper Control of Resource Identifiers ('Resource Injection'),https://cwe.mitre.org/data/definitions/914.html
621,Variable Extraction Error,99,Improper Control of Resource Identifiers ('Resource Injection'),https://cwe.mitre.org/data/definitions/621.html
627,Dynamic Variable Evaluation,99,Improper Control of Resource Identifiers ('Resource Injection'),https://cwe.mitre.org/data/definitions/627.html
641,Improper Restriction of Names for Files and Other Resources,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/641.html
694,Use of Multiple Resources with Duplicate Identifier,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/694.html
102,Struts: Duplicate Validation Forms,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/102.html
462,Duplicate Key in Associative List (Alist),74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/462.html
914,Improper Control of Dynamically-Identified Variables,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/914.html
621,Variable Extraction Error,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/621.html
627,Dynamic Variable Evaluation,74,Improper Neutralization of Special Elements in Output Used by a Downstream Component ('Injection'),https://cwe.mitre.org/data/definitions/627.html
1236,Improper Neutralization of Formula Elements in a CSV File,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1236.html
75,Failure to Sanitize Special Elements into a Different Plane (Special Element Injection),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/75.html
76,Improper Neutralization of Equivalent Special Elements,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/76.html
77,Improper Neutralization of Special Elements used in a Command ('Command Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/77.html
624,Executable Regular Expression Error,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/624.html
78,Improper Neutralization of Special Elements used in an OS Command ('OS Command Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/78.html
88,Improper Neutralization of Argument Delimiters in a Command ('Argument Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/88.html
917,Improper Neutralization of Special Elements used in an Expression Language Statement ('Expression Language Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/917.html
79,Improper Neutralization of Input During Web Page Generation ('Cross-site Scripting'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/79.html
692,Incomplete Denylist to Cross-Site Scripting,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/692.html
80,Improper Neutralization of Script-Related HTML Tags in a Web Page (Basic XSS),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/80.html
81,Improper Neutralization of Script in an Error Message Web Page,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/81.html
83,Improper Neutralization of Script in Attributes in a Web Page,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/83.html
82,Improper Neutralization of Script in Attributes of IMG Tags in a Web Page,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/82.html
84,Improper Neutralization of Encoded URI Schemes in a Web Page,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/84.html
85,Doubled Character XSS Manipulations,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/85.html
86,Improper Neutralization of Invalid Characters in Identifiers in Web Pages,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/86.html
87,Improper Neutralization of Alternate XSS Syntax,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/87.html
91,XML Injection (aka Blind XPath Injection),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/91.html
643,Improper Neutralization of Data within XPath Expressions ('XPath Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/643.html
652,Improper Neutralization of Data within XQuery Expressions ('XQuery Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/652.html
93,Improper Neutralization of CRLF Sequences ('CRLF Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/93.html
113,Improper Neutralization of CRLF Sequences in HTTP Headers ('HTTP Response Splitting'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/113.html
94,Improper Control of Generation of Code ('Code Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/94.html
1336,Improper Neutralization of Special Elements Used in a Template Engine,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/1336.html
95,Improper Neutralization of Directives in Dynamically Evaluated Code ('Eval Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/95.html
96,Improper Neutralization of Directives in Statically Saved Code ('Static Code Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/96.html
97,Improper Neutralization of Server-Side Includes (SSI) Within a Web Page,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/97.html
943,Improper Neutralization of Special Elements in Data Query Logic,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/943.html
89,Improper Neutralization of Special Elements used in an SQL Command ('SQL Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/89.html
564,SQL Injection: Hibernate,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/564.html
90,Improper Neutralization of Special Elements used in an LDAP Query ('LDAP Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/90.html
99,Improper Control of Resource Identifiers ('Resource Injection'),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/99.html
641,Improper Restriction of Names for Files and Other Resources,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/641.html
694,Use of Multiple Resources with Duplicate Identifier,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/694.html
462,Duplicate Key in Associative List (Alist),707,Improper Neutralization,https://cwe.mitre.org/data/definitions/462.html
914,Improper Control of Dynamically-Identified Variables,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/914.html
621,Variable Extraction Error,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/621.html
627,Dynamic Variable Evaluation,707,Improper Neutralization,https://cwe.mitre.org/data/definitions/627.html
710,Improper Adherence to Coding Standards,,,https://cwe.mitre.org/data/definitions/710.html
1041,Use of Redundant Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1041.html
1044,Architecture with Number of Horizontal Layers Outside of Expected Range,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1044.html
1048,Invokable Control Element with Large Number of Outward Calls,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1048.html
1059,Incomplete Documentation,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1059.html
1053,Missing Documentation for Design,1059,Incomplete Documentation,https://cwe.mitre.org/data/definitions/1053.html
1110,Incomplete Design Documentation,1059,Incomplete Documentation,https://cwe.mitre.org/data/definitions/1110.html
1111,Incomplete I/O Documentation,1059,Incomplete Documentation,https://cwe.mitre.org/data/definitions/1111.html
1112,Incomplete Documentation of Program Execution,1059,Incomplete Documentation,https://cwe.mitre.org/data/definitions/1112.html
1118,Insufficient Documentation of Error Handling Techniques,1059,Incomplete Documentation,https://cwe.mitre.org/data/definitions/1118.html
1053,Missing Documentation for Design,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1053.html
1110,Incomplete Design Documentation,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1110.html
1111,Incomplete I/O Documentation,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1111.html
1112,Incomplete Documentation of Program Execution,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1112.html
1118,Insufficient Documentation of Error Handling Techniques,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1118.html
1061,Insufficient Encapsulation,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1061.html
1054,Invocation of a Control Element at an Unnecessarily Deep Horizontal Layer,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/1054.html
1057,Data Access Operations Outside of Expected Data Manager Component,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/1057.html
1062,Parent Class with References to Child Class,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/1062.html
1083,Data Access from Outside Expected Data Manager Component,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/1083.html
1090,Method Containing Access of a Member Element from Another Class,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/1090.html
1100,Insufficient Isolation of System-Dependent Functions,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/1100.html
1105,Insufficient Encapsulation of Machine-Dependent Functionality,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/1105.html
188,Reliance on Data/Memory Layout,1105,Insufficient Encapsulation of Machine-Dependent Functionality,https://cwe.mitre.org/data/definitions/188.html
198,Use of Incorrect Byte Ordering,1105,Insufficient Encapsulation of Machine-Dependent Functionality,https://cwe.mitre.org/data/definitions/198.html
188,Reliance on Data/Memory Layout,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/188.html
198,Use of Incorrect Byte Ordering,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/198.html
766,Critical Data Element Declared Public,1061,Insufficient Encapsulation,https://cwe.mitre.org/data/definitions/766.html
1054,Invocation of a Control Element at an Unnecessarily Deep Horizontal Layer,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1054.html
1057,Data Access Operations Outside of Expected Data Manager Component,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1057.html
1062,Parent Class with References to Child Class,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1062.html
1083,Data Access from Outside Expected Data Manager Component,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1083.html
1090,Method Containing Access of a Member Element from Another Class,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1090.html
1100,Insufficient Isolation of System-Dependent Functions,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1100.html
1105,Insufficient Encapsulation of Machine-Dependent Functionality,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1105.html
188,Reliance on Data/Memory Layout,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/188.html
198,Use of Incorrect Byte Ordering,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/198.html
766,Critical Data Element Declared Public,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/766.html
1065,Runtime Resource Management Control Element in a Component Built to Run on Application Servers,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1065.html
1066,Missing Serialization Control Element,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1066.html
1068,Inconsistency Between Implementation and Documented Design,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1068.html
107,Struts: Unused Validation Form,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/107.html
1070,Serializable Data Element Containing non-Serializable Item Elements,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1070.html
1076,Insufficient Adherence to Expected Conventions,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1076.html
1045,Parent Class with a Virtual Destructor and a Child Class without a Virtual Destructor,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1045.html
1078,Inappropriate Source Code Style or Formatting,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1078.html
1085,Invokable Control Element with Excessive Volume of Commented-out Code,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1085.html
1099,Inconsistent Naming Conventions for Identifiers,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1099.html
1106,Insufficient Use of Symbolic Constants,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1106.html
1107,Insufficient Isolation of Symbolic Constant Definitions,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1107.html
1109,Use of Same Variable for Multiple Purposes,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1109.html
1113,Inappropriate Comment Style,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1113.html
1114,Inappropriate Whitespace Style,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1114.html
1115,Source Code Element without Standard Prologue,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1115.html
1116,Inaccurate Comments,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1116.html
1117,Callable with Insufficient Behavioral Summary,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/1117.html
546,Suspicious Comment,1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/546.html
547,"Use of Hard-coded, Security-relevant Constants",1078,Inappropriate Source Code Style or Formatting,https://cwe.mitre.org/data/definitions/547.html
1085,Invokable Control Element with Excessive Volume of Commented-out Code,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1085.html
1099,Inconsistent Naming Conventions for Identifiers,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1099.html
1106,Insufficient Use of Symbolic Constants,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1106.html
1107,Insufficient Isolation of Symbolic Constant Definitions,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1107.html
1109,Use of Same Variable for Multiple Purposes,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1109.html
1113,Inappropriate Comment Style,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1113.html
1114,Inappropriate Whitespace Style,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1114.html
1115,Source Code Element without Standard Prologue,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1115.html
1116,Inaccurate Comments,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1116.html
1117,Callable with Insufficient Behavioral Summary,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1117.html
546,Suspicious Comment,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/546.html
547,"Use of Hard-coded, Security-relevant Constants",1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/547.html
1079,Parent Class without Virtual Destructor Method,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1079.html
1082,Class Instance Self Destruction Control Element,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1082.html
1087,Class with Virtual Method without a Virtual Destructor,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1087.html
1091,Use of Object without Invoking Destructor Method,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1091.html
1097,Persistent Storable Data Element without Associated Comparison Control Element,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1097.html
1098,Data Element containing Pointer Item without Proper Copy Control Element,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1098.html
1108,Excessive Reliance on Global Variables,1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/1108.html
586,Explicit Call to Finalize(),1076,Insufficient Adherence to Expected Conventions,https://cwe.mitre.org/data/definitions/586.html
1045,Parent Class with a Virtual Destructor and a Child Class without a Virtual Destructor,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1045.html
1078,Inappropriate Source Code Style or Formatting,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1078.html
1085,Invokable Control Element with Excessive Volume of Commented-out Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1085.html
1099,Inconsistent Naming Conventions for Identifiers,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1099.html
1106,Insufficient Use of Symbolic Constants,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1106.html
1107,Insufficient Isolation of Symbolic Constant Definitions,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1107.html
1109,Use of Same Variable for Multiple Purposes,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1109.html
1113,Inappropriate Comment Style,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1113.html
1114,Inappropriate Whitespace Style,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1114.html
1115,Source Code Element without Standard Prologue,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1115.html
1116,Inaccurate Comments,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1116.html
1117,Callable with Insufficient Behavioral Summary,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1117.html
546,Suspicious Comment,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/546.html
547,"Use of Hard-coded, Security-relevant Constants",710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/547.html
1079,Parent Class without Virtual Destructor Method,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1079.html
1082,Class Instance Self Destruction Control Element,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1082.html
1087,Class with Virtual Method without a Virtual Destructor,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1087.html
1091,Use of Object without Invoking Destructor Method,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1091.html
1097,Persistent Storable Data Element without Associated Comparison Control Element,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1097.html
1098,Data Element containing Pointer Item without Proper Copy Control Element,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1098.html
1108,Excessive Reliance on Global Variables,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1108.html
586,Explicit Call to Finalize(),710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/586.html
1092,Use of Same Invokable Control Element in Multiple Architectural Layers,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1092.html
1093,Excessively Complex Data Representation,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1093.html
1043,Data Element Aggregating an Excessively Large Number of Non-Primitive Elements,1093,Excessively Complex Data Representation,https://cwe.mitre.org/data/definitions/1043.html
1055,Multiple Inheritance from Concrete Classes,1093,Excessively Complex Data Representation,https://cwe.mitre.org/data/definitions/1055.html
1074,Class with Excessively Deep Inheritance,1093,Excessively Complex Data Representation,https://cwe.mitre.org/data/definitions/1074.html
1086,Class with Excessive Number of Child Classes,1093,Excessively Complex Data Representation,https://cwe.mitre.org/data/definitions/1086.html
1043,Data Element Aggregating an Excessively Large Number of Non-Primitive Elements,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1043.html
1055,Multiple Inheritance from Concrete Classes,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1055.html
1074,Class with Excessively Deep Inheritance,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1074.html
1086,Class with Excessive Number of Child Classes,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1086.html
110,Struts: Validator Without Form Field,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/110.html
1101,Reliance on Runtime Component in Generated Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1101.html
1104,Use of Unmaintained Third Party Components,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1104.html
1120,Excessive Code Complexity,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1120.html
1047,Modules with Circular Dependencies,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1047.html
1056,Invokable Control Element with Variadic Parameters,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1056.html
1060,Excessive Number of Inefficient Server-Side Data Accesses,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1060.html
1064,Invokable Control Element with Signature Containing an Excessive Number of Parameters,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1064.html
1075,Unconditional Control Flow Transfer outside of Switch Block,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1075.html
1080,Source Code File with Excessive Number of Lines of Code,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1080.html
1095,Loop Condition Value Update within the Loop,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1095.html
1119,Excessive Use of Unconditional Branching,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1119.html
1121,Excessive McCabe Cyclomatic Complexity,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1121.html
1122,Excessive Halstead Complexity,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1122.html
1123,Excessive Use of Self-Modifying Code,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1123.html
1124,Excessively Deep Nesting,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1124.html
1125,Excessive Attack Surface,1120,Excessive Code Complexity,https://cwe.mitre.org/data/definitions/1125.html
1047,Modules with Circular Dependencies,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1047.html
1056,Invokable Control Element with Variadic Parameters,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1056.html
1060,Excessive Number of Inefficient Server-Side Data Accesses,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1060.html
1064,Invokable Control Element with Signature Containing an Excessive Number of Parameters,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1064.html
1075,Unconditional Control Flow Transfer outside of Switch Block,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1075.html
1080,Source Code File with Excessive Number of Lines of Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1080.html
1095,Loop Condition Value Update within the Loop,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1095.html
1119,Excessive Use of Unconditional Branching,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1119.html
1121,Excessive McCabe Cyclomatic Complexity,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1121.html
1122,Excessive Halstead Complexity,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1122.html
1123,Excessive Use of Self-Modifying Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1123.html
1124,Excessively Deep Nesting,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1124.html
1125,Excessive Attack Surface,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1125.html
1126,Declaration of Variable with Unnecessarily Wide Scope,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1126.html
1127,Compilation with Insufficient Warnings or Errors,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1127.html
1164,Irrelevant Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1164.html
1071,Empty Code Block,1164,Irrelevant Code,https://cwe.mitre.org/data/definitions/1071.html
1069,Empty Exception Block,1071,Empty Code Block,https://cwe.mitre.org/data/definitions/1069.html
585,Empty Synchronized Block,1071,Empty Code Block,https://cwe.mitre.org/data/definitions/585.html
1069,Empty Exception Block,1164,Irrelevant Code,https://cwe.mitre.org/data/definitions/1069.html
585,Empty Synchronized Block,1164,Irrelevant Code,https://cwe.mitre.org/data/definitions/585.html
561,Dead Code,1164,Irrelevant Code,https://cwe.mitre.org/data/definitions/561.html
563,Assignment to Variable without Use,1164,Irrelevant Code,https://cwe.mitre.org/data/definitions/563.html
1071,Empty Code Block,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1071.html
1069,Empty Exception Block,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1069.html
585,Empty Synchronized Block,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/585.html
561,Dead Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/561.html
563,Assignment to Variable without Use,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/563.html
1177,Use of Prohibited Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1177.html
242,Use of Inherently Dangerous Function,1177,Use of Prohibited Code,https://cwe.mitre.org/data/definitions/242.html
676,Use of Potentially Dangerous Function,1177,Use of Prohibited Code,https://cwe.mitre.org/data/definitions/676.html
785,Use of Path Manipulation Function without Maximum-sized Buffer,676,Use of Potentially Dangerous Function,https://cwe.mitre.org/data/definitions/785.html
785,Use of Path Manipulation Function without Maximum-sized Buffer,1177,Use of Prohibited Code,https://cwe.mitre.org/data/definitions/785.html
242,Use of Inherently Dangerous Function,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/242.html
676,Use of Potentially Dangerous Function,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/676.html
785,Use of Path Manipulation Function without Maximum-sized Buffer,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/785.html
1209,Failure to Disable Reserved Bits,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1209.html
476,NULL Pointer Dereference,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/476.html
690,Unchecked Return Value to NULL Pointer Dereference,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/690.html
477,Use of Obsolete Function,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/477.html
484,Omitted Break Statement in Switch,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/484.html
489,Active Debug Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/489.html
11,ASP.NET Misconfiguration: Creating Debug Binary,489,Active Debug Code,https://cwe.mitre.org/data/definitions/11.html
11,ASP.NET Misconfiguration: Creating Debug Binary,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/11.html
506,Embedded Malicious Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/506.html
507,Trojan Horse,506,Embedded Malicious Code,https://cwe.mitre.org/data/definitions/507.html
508,Non-Replicating Malicious Code,507,Trojan Horse,https://cwe.mitre.org/data/definitions/508.html
509,Replicating Malicious Code (Virus or Worm),507,Trojan Horse,https://cwe.mitre.org/data/definitions/509.html
508,Non-Replicating Malicious Code,506,Embedded Malicious Code,https://cwe.mitre.org/data/definitions/508.html
509,Replicating Malicious Code (Virus or Worm),506,Embedded Malicious Code,https://cwe.mitre.org/data/definitions/509.html
510,Trapdoor,506,Embedded Malicious Code,https://cwe.mitre.org/data/definitions/510.html
511,Logic/Time Bomb,506,Embedded Malicious Code,https://cwe.mitre.org/data/definitions/511.html
512,Spyware,506,Embedded Malicious Code,https://cwe.mitre.org/data/definitions/512.html
507,Trojan Horse,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/507.html
508,Non-Replicating Malicious Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/508.html
509,Replicating Malicious Code (Virus or Worm),710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/509.html
510,Trapdoor,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/510.html
511,Logic/Time Bomb,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/511.html
512,Spyware,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/512.html
570,Expression is Always False,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/570.html
571,Expression is Always True,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/571.html
573,Improper Following of Specification by Caller,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/573.html
103,Struts: Incomplete validate() Method Definition,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/103.html
104,Struts: Form Bean Does Not Extend Validation Class,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/104.html
243,Creation of chroot Jail Without Changing Working Directory,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/243.html
253,Incorrect Check of Function Return Value,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/253.html
296,Improper Following of a Certificate's Chain of Trust,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/296.html
304,Missing Critical Step in Authentication,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/304.html
325,Missing Cryptographic Step,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/325.html
329,Generation of Predictable IV with CBC Mode,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/329.html
358,Improperly Implemented Security Check for Standard,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/358.html
475,Undefined Behavior for Input to API,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/475.html
568,finalize() Method Without super.finalize(),573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/568.html
577,EJB Bad Practices: Use of Sockets,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/577.html
578,EJB Bad Practices: Use of Class Loader,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/578.html
579,J2EE Bad Practices: Non-serializable Object Stored in Session,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/579.html
580,clone() Method Without super.clone(),573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/580.html
581,Object Model Violation: Just One of Equals and Hashcode Defined,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/581.html
628,Function Call with Incorrectly Specified Arguments,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/628.html
683,Function Call With Incorrect Order of Arguments,628,Function Call with Incorrectly Specified Arguments,https://cwe.mitre.org/data/definitions/683.html
685,Function Call With Incorrect Number of Arguments,628,Function Call with Incorrectly Specified Arguments,https://cwe.mitre.org/data/definitions/685.html
686,Function Call With Incorrect Argument Type,628,Function Call with Incorrectly Specified Arguments,https://cwe.mitre.org/data/definitions/686.html
687,Function Call With Incorrectly Specified Argument Value,628,Function Call with Incorrectly Specified Arguments,https://cwe.mitre.org/data/definitions/687.html
560,Use of umask() with chmod-style Argument,687,Function Call With Incorrectly Specified Argument Value,https://cwe.mitre.org/data/definitions/560.html
560,Use of umask() with chmod-style Argument,628,Function Call with Incorrectly Specified Arguments,https://cwe.mitre.org/data/definitions/560.html
688,Function Call With Incorrect Variable or Reference as Argument,628,Function Call with Incorrectly Specified Arguments,https://cwe.mitre.org/data/definitions/688.html
683,Function Call With Incorrect Order of Arguments,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/683.html
685,Function Call With Incorrect Number of Arguments,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/685.html
686,Function Call With Incorrect Argument Type,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/686.html
687,Function Call With Incorrectly Specified Argument Value,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/687.html
560,Use of umask() with chmod-style Argument,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/560.html
688,Function Call With Incorrect Variable or Reference as Argument,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/688.html
675,Duplicate Operations on Resource,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/675.html
174,Double Decoding of the Same Data,675,Duplicate Operations on Resource,https://cwe.mitre.org/data/definitions/174.html
415,Double Free,675,Duplicate Operations on Resource,https://cwe.mitre.org/data/definitions/415.html
605,Multiple Binds to the Same Port,675,Duplicate Operations on Resource,https://cwe.mitre.org/data/definitions/605.html
764,Multiple Locks of a Critical Resource,675,Duplicate Operations on Resource,https://cwe.mitre.org/data/definitions/764.html
765,Multiple Unlocks of a Critical Resource,675,Duplicate Operations on Resource,https://cwe.mitre.org/data/definitions/765.html
174,Double Decoding of the Same Data,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/174.html
415,Double Free,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/415.html
605,Multiple Binds to the Same Port,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/605.html
764,Multiple Locks of a Critical Resource,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/764.html
765,Multiple Unlocks of a Critical Resource,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/765.html
694,Use of Multiple Resources with Duplicate Identifier,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/694.html
102,Struts: Duplicate Validation Forms,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/102.html
462,Duplicate Key in Associative List (Alist),573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/462.html
695,Use of Low-Level Functionality,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/695.html
111,Direct Use of Unsafe JNI,695,Use of Low-Level Functionality,https://cwe.mitre.org/data/definitions/111.html
245,J2EE Bad Practices: Direct Management of Connections,695,Use of Low-Level Functionality,https://cwe.mitre.org/data/definitions/245.html
246,J2EE Bad Practices: Direct Use of Sockets,695,Use of Low-Level Functionality,https://cwe.mitre.org/data/definitions/246.html
383,J2EE Bad Practices: Direct Use of Threads,695,Use of Low-Level Functionality,https://cwe.mitre.org/data/definitions/383.html
574,EJB Bad Practices: Use of Synchronization Primitives,695,Use of Low-Level Functionality,https://cwe.mitre.org/data/definitions/574.html
575,EJB Bad Practices: Use of AWT Swing,695,Use of Low-Level Functionality,https://cwe.mitre.org/data/definitions/575.html
576,EJB Bad Practices: Use of Java I/O,695,Use of Low-Level Functionality,https://cwe.mitre.org/data/definitions/576.html
111,Direct Use of Unsafe JNI,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/111.html
245,J2EE Bad Practices: Direct Management of Connections,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/245.html
246,J2EE Bad Practices: Direct Use of Sockets,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/246.html
383,J2EE Bad Practices: Direct Use of Threads,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/383.html
574,EJB Bad Practices: Use of Synchronization Primitives,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/574.html
575,EJB Bad Practices: Use of AWT Swing,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/575.html
576,EJB Bad Practices: Use of Java I/O,573,Improper Following of Specification by Caller,https://cwe.mitre.org/data/definitions/576.html
103,Struts: Incomplete validate() Method Definition,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/103.html
104,Struts: Form Bean Does Not Extend Validation Class,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/104.html
243,Creation of chroot Jail Without Changing Working Directory,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/243.html
253,Incorrect Check of Function Return Value,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/253.html
296,Improper Following of a Certificate's Chain of Trust,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/296.html
304,Missing Critical Step in Authentication,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/304.html
325,Missing Cryptographic Step,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/325.html
329,Generation of Predictable IV with CBC Mode,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/329.html
358,Improperly Implemented Security Check for Standard,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/358.html
475,Undefined Behavior for Input to API,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/475.html
568,finalize() Method Without super.finalize(),710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/568.html
577,EJB Bad Practices: Use of Sockets,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/577.html
578,EJB Bad Practices: Use of Class Loader,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/578.html
579,J2EE Bad Practices: Non-serializable Object Stored in Session,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/579.html
580,clone() Method Without super.clone(),710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/580.html
581,Object Model Violation: Just One of Equals and Hashcode Defined,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/581.html
628,Function Call with Incorrectly Specified Arguments,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/628.html
683,Function Call With Incorrect Order of Arguments,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/683.html
685,Function Call With Incorrect Number of Arguments,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/685.html
686,Function Call With Incorrect Argument Type,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/686.html
687,Function Call With Incorrectly Specified Argument Value,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/687.html
560,Use of umask() with chmod-style Argument,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/560.html
688,Function Call With Incorrect Variable or Reference as Argument,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/688.html
675,Duplicate Operations on Resource,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/675.html
174,Double Decoding of the Same Data,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/174.html
415,Double Free,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/415.html
605,Multiple Binds to the Same Port,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/605.html
764,Multiple Locks of a Critical Resource,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/764.html
765,Multiple Unlocks of a Critical Resource,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/765.html
694,Use of Multiple Resources with Duplicate Identifier,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/694.html
102,Struts: Duplicate Validation Forms,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/102.html
462,Duplicate Key in Associative List (Alist),710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/462.html
695,Use of Low-Level Functionality,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/695.html
111,Direct Use of Unsafe JNI,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/111.html
245,J2EE Bad Practices: Direct Management of Connections,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/245.html
246,J2EE Bad Practices: Direct Use of Sockets,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/246.html
383,J2EE Bad Practices: Direct Use of Threads,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/383.html
574,EJB Bad Practices: Use of Synchronization Primitives,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/574.html
575,EJB Bad Practices: Use of AWT Swing,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/575.html
576,EJB Bad Practices: Use of Java I/O,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/576.html
594,J2EE Framework: Saving Unserializable Objects to Disk,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/594.html
657,Violation of Secure Design Principles,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/657.html
1192,"System-on-Chip (SoC) Using Components without Unique, Immutable Identifiers",657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/1192.html
250,Execution with Unnecessary Privileges,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/250.html
636,Not Failing Securely ('Failing Open'),657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/636.html
455,Non-exit on Failed Initialization,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/455.html
637,Unnecessary Complexity in Protection Mechanism (Not Using 'Economy of Mechanism'),657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/637.html
638,Not Using Complete Mediation,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/638.html
424,Improper Protection of Alternate Path,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/424.html
425,Direct Request ('Forced Browsing'),657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/425.html
653,Insufficient Compartmentalization,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/653.html
1331,Improper Isolation of Shared Resources in Network On Chip,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/1331.html
654,Reliance on a Single Factor in a Security Decision,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/654.html
308,Use of Single-factor Authentication,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/308.html
309,Use of Password System for Primary Authentication,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/309.html
655,Insufficient Psychological Acceptability,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/655.html
656,Reliance on Security Through Obscurity,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/656.html
671,Lack of Administrator Control over Security,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/671.html
447,Unimplemented or Unsupported Feature in UI,671,Lack of Administrator Control over Security,https://cwe.mitre.org/data/definitions/447.html
798,Use of Hard-coded Credentials,671,Lack of Administrator Control over Security,https://cwe.mitre.org/data/definitions/798.html
259,Use of Hard-coded Password,671,Lack of Administrator Control over Security,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,671,Lack of Administrator Control over Security,https://cwe.mitre.org/data/definitions/321.html
447,Unimplemented or Unsupported Feature in UI,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/447.html
798,Use of Hard-coded Credentials,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/798.html
259,Use of Hard-coded Password,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,657,Violation of Secure Design Principles,https://cwe.mitre.org/data/definitions/321.html
1192,"System-on-Chip (SoC) Using Components without Unique, Immutable Identifiers",710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1192.html
250,Execution with Unnecessary Privileges,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/250.html
636,Not Failing Securely ('Failing Open'),710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/636.html
455,Non-exit on Failed Initialization,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/455.html
637,Unnecessary Complexity in Protection Mechanism (Not Using 'Economy of Mechanism'),710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/637.html
638,Not Using Complete Mediation,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/638.html
424,Improper Protection of Alternate Path,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/424.html
425,Direct Request ('Forced Browsing'),710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/425.html
653,Insufficient Compartmentalization,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/653.html
1331,Improper Isolation of Shared Resources in Network On Chip,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1331.html
654,Reliance on a Single Factor in a Security Decision,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/654.html
308,Use of Single-factor Authentication,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/308.html
309,Use of Password System for Primary Authentication,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/309.html
655,Insufficient Psychological Acceptability,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/655.html
656,Reliance on Security Through Obscurity,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/656.html
671,Lack of Administrator Control over Security,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/671.html
447,Unimplemented or Unsupported Feature in UI,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/447.html
798,Use of Hard-coded Credentials,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/798.html
259,Use of Hard-coded Password,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/259.html
321,Use of Hard-coded Cryptographic Key,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/321.html
684,Incorrect Provision of Specified Functionality,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/684.html
1245,Improper Finite State Machines (FSMs) in Hardware Logic,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/1245.html
392,Missing Report of Error Condition,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/392.html
393,Return of Wrong Status Code,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/393.html
440,Expected Behavior Violation,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/440.html
446,UI Discrepancy for Security Feature,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/446.html
447,Unimplemented or Unsupported Feature in UI,446,UI Discrepancy for Security Feature,https://cwe.mitre.org/data/definitions/447.html
448,Obsolete Feature in UI,446,UI Discrepancy for Security Feature,https://cwe.mitre.org/data/definitions/448.html
449,The UI Performs the Wrong Action,446,UI Discrepancy for Security Feature,https://cwe.mitre.org/data/definitions/449.html
447,Unimplemented or Unsupported Feature in UI,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/447.html
448,Obsolete Feature in UI,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/448.html
449,The UI Performs the Wrong Action,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/449.html
451,User Interface (UI) Misrepresentation of Critical Information,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/451.html
1007,Insufficient Visual Distinction of Homoglyphs Presented to User,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/1007.html
1021,Improper Restriction of Rendered UI Layers or Frames,684,Incorrect Provision of Specified Functionality,https://cwe.mitre.org/data/definitions/1021.html
1245,Improper Finite State Machines (FSMs) in Hardware Logic,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1245.html
392,Missing Report of Error Condition,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/392.html
393,Return of Wrong Status Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/393.html
440,Expected Behavior Violation,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/440.html
446,UI Discrepancy for Security Feature,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/446.html
448,Obsolete Feature in UI,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/448.html
449,The UI Performs the Wrong Action,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/449.html
451,User Interface (UI) Misrepresentation of Critical Information,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/451.html
1007,Insufficient Visual Distinction of Homoglyphs Presented to User,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1007.html
1021,Improper Restriction of Rendered UI Layers or Frames,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1021.html
758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/758.html
1038,Insecure Automated Optimizations,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/1038.html
1037,Processor Optimization Removal or Modification of Security-critical Code,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/1037.html
733,Compiler Optimization Removal or Modification of Security-critical Code,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/733.html
14,Compiler Removal of Code to Clear Buffers,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/14.html
1102,Reliance on Machine-Dependent Data Representation,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/1102.html
1103,Use of Platform-Dependent Third Party Components,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/1103.html
1105,Insufficient Encapsulation of Machine-Dependent Functionality,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/1105.html
188,Reliance on Data/Memory Layout,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/188.html
198,Use of Incorrect Byte Ordering,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/198.html
474,Use of Function with Inconsistent Implementations,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/474.html
589,Call to Non-ubiquitous API,474,Use of Function with Inconsistent Implementations,https://cwe.mitre.org/data/definitions/589.html
589,Call to Non-ubiquitous API,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/589.html
562,Return of Stack Variable Address,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/562.html
587,Assignment of a Fixed Address to a Pointer,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/587.html
588,Attempt to Access Child of a Non-structure Pointer,758,"Reliance on Undefined, Unspecified, or Implementation-Defined Behavior",https://cwe.mitre.org/data/definitions/588.html
1038,Insecure Automated Optimizations,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1038.html
1037,Processor Optimization Removal or Modification of Security-critical Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1037.html
733,Compiler Optimization Removal or Modification of Security-critical Code,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/733.html
14,Compiler Removal of Code to Clear Buffers,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/14.html
1102,Reliance on Machine-Dependent Data Representation,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1102.html
1103,Use of Platform-Dependent Third Party Components,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/1103.html
474,Use of Function with Inconsistent Implementations,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/474.html
589,Call to Non-ubiquitous API,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/589.html
562,Return of Stack Variable Address,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/562.html
587,Assignment of a Fixed Address to a Pointer,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/587.html
588,Attempt to Access Child of a Non-structure Pointer,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/588.html
912,Hidden Functionality,710,Improper Adherence to Coding Standards,https://cwe.mitre.org/data/definitions/912.html
