import os
import pandas as pd
from utils.cwe_find import CWEProcessor # Corrected relative import
from utils.diff_analyzer_legacy import analyze_code_diff

def transform_cwe_to_highest_parent(
    input_csv_path: str,
    output_csv_path: str,
    cwe_column_name: str = 'CWE_ID',
    new_parent_column_name: str = 'CWE_Parent'
):
    """
    Reads a CSV file, processes a specified CWE ID column to find the highest parent CWE,
    and saves the result with a new column to an output CSV file.

    Args:
        input_csv_path (str): Path to the input CSV file.
        output_csv_path (str): Path to save the output CSV file.
        cwe_column_name (str, optional): Name of the column containing CWE IDs.
                                         Defaults to 'CWE_ID'.
        new_parent_column_name (str, optional): Name of the new column to store the
                                                highest parent CWE. Defaults to 'CWE_Parent'.
    """
    PREDEFINED_CWES_LIST = sorted([
        "CWE-189", "CWE-254", "CWE-264", "CWE-284", "CWE-310",
        "CWE-399", "CWE-664", "CWE-682", "CWE-691", "CWE-703", "CWE-707"
    ])

    try:
        # Initialize CWEProcessor
        # Assuming cwe_info_1000.csv is accessible via the path in CWEProcessor
        processor = CWEProcessor()
    except FileNotFoundError as e:
        print(f"Error initializing CWEProcessor: {e}")
        print("Please ensure 'utils/assets/cwe_info_1000.csv' is accessible.")
        return
    except Exception as e:
        print(f"An unexpected error occurred during CWEProcessor initialization: {e}")
        return

    try:
        # Read the input CSV
        df = pd.read_csv(input_csv_path)
    except FileNotFoundError:
        print(f"Error: Input CSV file not found at {input_csv_path}")
        return
    except Exception as e:
        print(f"Error reading CSV file {input_csv_path}: {e}")
        return

    if cwe_column_name not in df.columns:
        print(f"Error: CWE column '{cwe_column_name}' not found in the input CSV.")
        print(f"Available columns are: {df.columns.tolist()}")
        return

    def get_highest_parent(cwe_id):
        if pd.isna(cwe_id) or not isinstance(cwe_id, str) or cwe_id.strip() == "":
            return None  # Or return cwe_id, or an empty string, depending on desired behavior for invalid inputs
        
        # The process_cwe method in CWEProcessor already handles cases where
        # the CWE ID might not be in its dictionary, returning (cwe, cwe).
        # The second element is what we need.
        _ancestry_path, highest_parent = processor.process_cwe(str(cwe_id).strip())
        
        if highest_parent not in PREDEFINED_CWES_LIST:
            return 'CWE-0'
        return highest_parent

    # Apply the function to the CWE column
    try:
        df[new_parent_column_name] = df[cwe_column_name].apply(get_highest_parent)
        # Save the modified DataFrame to the output CSV
        df.to_csv(output_csv_path, index=False)
        print(f"Processing complete. Output saved to {output_csv_path}")
    except Exception as e:
        print(f"Error during processing or saving the CSV: {e}")

def split_csv_by_cwe_parent(
    input_processed_csv_path: str,
    dataset_name: str,
    parent_cwe_column_name: str = 'CWE_Parent',
    output_base_dir: str = 'datasets'
):
    """
    Splits a CSV file (already processed to have a parent CWE column)
    into multiple CSV files based on the parent CWE type.

    Args:
        input_processed_csv_path (str): Path to the input CSV file that already
                                        contains a column with pre-calculated parent CWEs.
        dataset_name (str): Name of the dataset, used to create the output subdirectory.
        parent_cwe_column_name (str, optional): Name of the column containing the
                                                pre-calculated parent CWE IDs.
                                                Defaults to 'CWE_Parent'.
        output_base_dir (str, optional): Base directory for output.
                                         Defaults to 'datasets'.
    """
    try:
        df = pd.read_csv(input_processed_csv_path)
    except FileNotFoundError:
        print(f"Error: Input CSV file not found at {input_processed_csv_path}")
        return
    except Exception as e:
        print(f"Error reading CSV file {input_processed_csv_path}: {e}")
        return

    if parent_cwe_column_name not in df.columns:
        print(f"Error: Parent CWE column '{parent_cwe_column_name}' not found in the input CSV.")
        print(f"Available columns are: {df.columns.tolist()}")
        return

    output_path = os.path.join(output_base_dir, dataset_name, 'cwe_split')
    os.makedirs(output_path, exist_ok=True)
    print(f"Output directory created/ensured: {output_path}")

    unique_parent_cwes = df[parent_cwe_column_name].unique()

    for parent_cwe in unique_parent_cwes:
        if pd.isna(parent_cwe): # Handle potential NaN values if any
            print(f"Skipping NaN parent CWE value.")
            continue
        
        # Sanitize parent_cwe to be a valid filename
        # Replace common problematic characters, though CWE IDs are usually fine
        safe_parent_cwe_filename = str(parent_cwe).replace('/', '_').replace('\\', '_')
        
        sub_df = df[df[parent_cwe_column_name] == parent_cwe]
        output_filename = f"{safe_parent_cwe_filename}.csv"
        full_output_path = os.path.join(output_path, output_filename)
        
        try:
            sub_df.to_csv(full_output_path, index=False)
            print(f"Successfully saved: {full_output_path} ({len(sub_df)} rows)")
        except Exception as e:
            print(f"Error saving file {full_output_path}: {e}")

    print(f"CSV splitting process complete for {input_processed_csv_path}.")

def add_headers_and_ids_to_csv(
    input_csv_path: str,
    output_csv_path: str,
    headers: list = ['code_before', 'code_after', 'cwe_type', 'cve_id'],
    id_column_name: str = 'id',
    id_prefix: str = ''  # 虽然用户说不需要前缀，但原代码这里已经是空字符串，保留参数以防未来需要
):
    """
    向CSV文件添加表头并根据行号分配数据ID。

    Args:
        input_csv_path (str): 输入CSV文件路径。
        output_csv_path (str): 输出CSV文件路径。
        headers (list, optional): 最终CSV文件应包含的表头列表(不包含ID列)。
                                 如果原始CSV有表头，这些将被替换。
                                 多余的原始列将被丢弃，不足的列将作为空列添加。
                                 默认为 ['code_before', 'code_after', 'cwe_type', 'cve_id']。
        id_column_name (str, optional): ID列的名称。默认为 'id'。
        id_prefix (str, optional): ID前缀。默认为 '' (此版本不使用前缀)。
    """
    try:
        # 读取输入CSV文件，Pandas会自动尝试将第一行作为表头 (header=0 by default)
        df = pd.read_csv(input_csv_path)

        target_headers = headers.copy()  # 创建副本避免修改原始headers列表
        num_target_headers = len(target_headers)
        
        # 如果原始列多于目标表头，则截断右侧多余的列
        if len(df.columns) > num_target_headers:
            df = df.iloc[:, :num_target_headers]
        
        # 将现有列重命名为目标表头 (按顺序)
        df.columns = target_headers[:len(df.columns)]

        # 如果目标表头数量多于当前列数 (即原始列数少于目标表头数)
        # 则添加缺失的列，并用 pd.NA 填充
        if len(df.columns) < num_target_headers:
            for i in range(len(df.columns), num_target_headers):
                df[target_headers[i]] = pd.NA

        # 添加ID列 (从0开始，无前缀)
        df.insert(0, id_column_name, [i for i in range(len(df))])
        
        # 保存到输出CSV文件
        df.to_csv(output_csv_path, index=False)
        print(f"处理完成。已添加表头和ID列，输出保存到 {output_csv_path}")
        print(f"总行数: {len(df)}")
        print(f"最终列名: {list(df.columns)}")  # 打印最终列名供参考
        
    except FileNotFoundError:
        print(f"错误: 输入CSV文件未找到: {input_csv_path}")
    except Exception as e:
        print(f"处理CSV文件时出错: {e}")

if __name__ == '__main__':
    # Example Usage for transform_cwe_to_highest_parent
    INPUT_CSV = 'test_input.csv'
    OUTPUT_CSV_PROCESSED = 'test_output_with_parents.csv'
    DATASET_NAME_FOR_SPLIT = 'my_test_dataset'

    try:
        pd.DataFrame({
            'CWE_ID': ['CWE-119', 'CWE-20', 'CWE-79', 'CWE-190', 'CWE-12345', None, 'CWE-664', 'CWE-22', 'CWE-416', 'CWE-787'],
            'Other_Data': [1, 2, 3, 4, 5, 6, 7, 8, 9, 10]
        }).to_csv(INPUT_CSV, index=False)
        
        print(f"Created dummy '{INPUT_CSV}' for demonstration.")
        
        transform_cwe_to_highest_parent(INPUT_CSV, OUTPUT_CSV_PROCESSED, cwe_column_name='CWE_ID')
        
        print(f"\nFinished processing '{INPUT_CSV}' to '{OUTPUT_CSV_PROCESSED}'.")
        
        # Example Usage for split_csv_by_cwe_parent
        # This assumes transform_cwe_to_highest_parent ran successfully and created OUTPUT_CSV_PROCESSED
        print(f"\nStarting CSV split for '{OUTPUT_CSV_PROCESSED}'...")
        # Check if the processed file exists before trying to split it
        if os.path.exists(OUTPUT_CSV_PROCESSED):
            split_csv_by_cwe_parent(
                input_processed_csv_path=OUTPUT_CSV_PROCESSED,
                dataset_name=DATASET_NAME_FOR_SPLIT,
                parent_cwe_column_name='CWE_Parent' # This is the default output of transform_cwe_to_highest_parent
            )
            print(f"Splitting complete. Check the '{os.path.join('datasets', DATASET_NAME_FOR_SPLIT, 'cwe_split')}' directory.")
        else:
            print(f"Error: Processed file '{OUTPUT_CSV_PROCESSED}' not found. Skipping split example.")
            
    except Exception as e:
        print(f"Error in example usage: {e}")
