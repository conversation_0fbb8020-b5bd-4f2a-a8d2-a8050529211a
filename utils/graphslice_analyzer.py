import subprocess
import tempfile
import re
import os
from typing import List, Tuple, Set

# 从 slicer.parse_joern_output2 导入 slice_code_from_string
# 假设 slicer 目录与 merge_llm.py 在同一查找路径下，或者已正确安装
# 如果遇到 ModuleNotFoundError，可能需要调整 Python 的模块搜索路径 (sys.path)
# 或者确保 slicer 目录有一个 __init__.py 文件使其成为一个包。
# 为简单起见，我们假设可以直接导入。
try:
    from slicer.parse_joern_output2 import slice_code_from_string
except ImportError as e:
    print(f"无法导入 slice_code_from_string: {e}")
    print("请确保 slicer.parse_joern_output2.py 可访问，并且其依赖项（如 pandas, numpy）已安装。")
    # 定义一个占位符函数，以便脚本的其余部分至少可以被解析
    def slice_code_from_string(source_code: str, target_lines: list[int], slice_type: str = "backward", data_flow_only: bool = False, verbose: bool = False, visualization_output_dir: str = None) -> str:
        print("错误：slice_code_from_string 未成功导入。返回空字符串。")
        return ""

# --- 从 diff_analyzer.py 复制过来的代码 ---
def _get_lines_from_string(code_string: str) -> Tuple[List[str], int]:
    """Splits a string into lines, preserving line endings, and counts them."""
    if not code_string:
        return [], 0
    lines = code_string.splitlines(keepends=True)
    return lines, len(lines)

def parse_diff_output(diff_text: str, total_old_lines: int, total_new_lines: int) -> Tuple[List[int], List[int], List[int], List[Tuple[int, int]]]:
    """
    Parses the output of 'git diff -U0' command.
    Returns:
        - modified_lines_old (list): Line numbers in the old file that were modified.
        - deleted_lines_old (list): Line numbers in the old file that were deleted.
        - added_lines_new (list): Line numbers in the new file that were added.
        - unchanged_mapping (list of tuples): (old_line_num, new_line_num) for unchanged lines.
    """
    modified_lines_old: Set[int] = set()
    deleted_lines_old: Set[int] = set()
    added_lines_new: Set[int] = set()

    hunks_info: List[Tuple[int, int, int, int]] = [] 
    hunk_header_regex = re.compile(r"^@@ -(\d+)(?:,(\d+))? \+(\d+)(?:,(\d+))? @@")

    lines = diff_text.splitlines()
    for line in lines:
        match = hunk_header_regex.match(line)
        if match:
            old_start = int(match.group(1))
            old_len = int(match.group(2)) if match.group(2) is not None else 1
            new_start = int(match.group(3))
            new_len = int(match.group(4)) if match.group(4) is not None else 1
            
            hunks_info.append((old_start, old_len, new_start, new_len))

            if old_len > 0 and new_len > 0: # Modification
                for i in range(old_len):
                    modified_lines_old.add(old_start + i)
                for i in range(new_len): 
                    added_lines_new.add(new_start + i)
            elif old_len > 0 and new_len == 0: # Deletion
                for i in range(old_len):
                    deleted_lines_old.add(old_start + i)
            elif old_len == 0 and new_len > 0: # Addition
                for i in range(new_len):
                    added_lines_new.add(new_start + i)
    
    unchanged_mapping: List[Tuple[int, int]] = []
    hunks_info.sort(key=lambda h: (h[0], h[2]))

    current_old_line = 1
    current_new_line = 1
    hunk_idx = 0

    while current_old_line <= total_old_lines or current_new_line <= total_new_lines:
        processed_by_hunk_logic = False
        if hunk_idx < len(hunks_info):
            h_old_s, h_old_l, h_new_s, h_new_l = hunks_info[hunk_idx]

            if h_old_l == 0: 
                while current_new_line < h_new_s:
                    if current_old_line <= total_old_lines:
                         unchanged_mapping.append((current_old_line, current_new_line))
                         current_old_line += 1
                    current_new_line += 1
                
                current_new_line = h_new_s + h_new_l 
                hunk_idx += 1
                processed_by_hunk_logic = True
            elif current_old_line >= h_old_s and current_old_line < (h_old_s + h_old_l):
                current_old_line = h_old_s + h_old_l 
                current_new_line = h_new_s + h_new_l 
                hunk_idx += 1
                processed_by_hunk_logic = True
            
        if not processed_by_hunk_logic:
            if current_old_line <= total_old_lines and current_new_line <= total_new_lines:
                unchanged_mapping.append((current_old_line, current_new_line))
                current_old_line += 1
                current_new_line += 1
            elif current_old_line <= total_old_lines: 
                current_old_line += 1
            elif current_new_line <= total_new_lines: 
                current_new_line += 1
            else: 
                break
                
    return sorted(list(modified_lines_old)), sorted(list(deleted_lines_old)), sorted(list(added_lines_new)), unchanged_mapping

def analyze_code_diff(old_code_str: str, new_code_str: str) -> Tuple[List[int], List[int]]:
    """
    Analyzes the difference between two code strings.
    Returns:
        - changed_or_deleted_lines_old (List[int]): Line numbers in the old file that were modified or deleted.
        - added_or_modified_lines_new (List[int]): Line numbers in the new file that were added or are the new versions of modified lines.
    """
    old_code_lines, total_old_lines = _get_lines_from_string(old_code_str)
    new_code_lines, total_new_lines = _get_lines_from_string(new_code_str)

    if not old_code_str and not new_code_str:
        return [], []
    if not old_code_str:
        return [], list(range(1, total_new_lines + 1))
    if not new_code_str:
        return list(range(1, total_old_lines + 1)), []

    diff_text = None
    tmp_old_file_path = None
    tmp_new_file_path = None

    try:
        # 使用 delete=False 确保文件在 with 块之外仍然存在，直到显式删除
        # newline='' 用于在 Windows 上正确处理行尾
        with tempfile.NamedTemporaryFile(mode='w+', delete=False, encoding='utf-8', prefix="old_", suffix=".tmp", newline='') as tmp_old_file, \
             tempfile.NamedTemporaryFile(mode='w+', delete=False, encoding='utf-8', prefix="new_", suffix=".tmp", newline='') as tmp_new_file:
            
            tmp_old_file.writelines(old_code_lines)
            tmp_old_file_path = tmp_old_file.name
            tmp_old_file.flush() # 确保内容写入磁盘
            
            tmp_new_file.writelines(new_code_lines)
            tmp_new_file_path = tmp_new_file.name
            tmp_new_file.flush() # 确保内容写入磁盘

        # 在文件关闭后执行 git diff
        # 注意：在某些系统或 git 版本中，直接比较临时文件可能需要确保它们有不同的内容或名称模式
        # 以避免被 git 视为同一文件或被 .gitignore 忽略。
        # 使用 --no-index 明确指示 git 比较文件系统中的文件，而不是仓库中的文件。
        cmd = ["git", "diff", "--no-index", "-U0", tmp_old_file_path, tmp_new_file_path]
        process = subprocess.run(cmd, capture_output=True, text=True, encoding='utf-8', check=False) # check=False 以便手动处理返回码
        
        # git diff 在文件相同时返回0，不同时返回1，错误时返回 >1
        if process.returncode > 1:
            print(f"git diff 命令执行错误 (返回码 {process.returncode}):\n{process.stderr}")
            return [], [] # 或者抛出异常
        diff_text = process.stdout

    finally:
        # 改进临时文件清理逻辑，更安全地处理文件删除
        for tmp_path in [tmp_old_file_path, tmp_new_file_path]:
            if tmp_path and os.path.exists(tmp_path):
                try:
                    os.remove(tmp_path)
                except OSError as e:
                    print(f"警告：删除临时文件 {tmp_path} 失败: {e}")

    if diff_text is None:
        # This case should ideally not be reached if subprocess.run was successful or an error was handled.
        print("未能获取 diff 输出。")
        return [], []
    
    # 改进空差异检测逻辑：如果diff输出为空，说明文件内容相同
    if not diff_text.strip():
        # No textual changes detected by diff
        return [], []

    modified_old, deleted_old, added_new, _ = parse_diff_output(diff_text, total_old_lines, total_new_lines)
    
    changed_or_deleted_lines_old = sorted(list(set(modified_old) | set(deleted_old)))
    # added_new from parse_diff_output already includes lines that are new versions of modified lines
    added_or_modified_lines_new = added_new # This is already sorted from parse_diff_output
    
    return changed_or_deleted_lines_old, added_or_modified_lines_new
# --- diff_analyzer.py 代码复制结束 ---


# --- 主接口函数 ---
def process_code_changes(old_code_str: str, new_code_str: str) -> Tuple[str, str]:
    """
    对于一个修改前的代码和修改后的代码，获取修改前代码中被修改和删除的行号，
    以及修改后的代码中被修改和添加的行号，然后分别根据行号对修改前代码和修改后的代码进行切片。

    Args:
        old_code_str: 修改前的代码字符串。
        new_code_str: 修改后的代码字符串。

    Returns:
        A tuple containing:
        - sliced_old_code (str): 修改前代码的向后切片结果。
        - sliced_new_code (str): 修改后代码的向后切片结果。
    """
    changed_or_deleted_lines_old, added_or_modified_lines_new = analyze_code_diff(old_code_str, new_code_str)

    sliced_old_code = ""
    if changed_or_deleted_lines_old:
        try:
            sliced_old_code,_ = slice_code_from_string(
                source_code=old_code_str,
                target_lines=changed_or_deleted_lines_old,
                slice_type="backward",
                data_flow_only=False,
                verbose=False # 可以设置为 True 进行调试
            )
        except Exception as e:
            print(f"  旧代码切片时发生错误: {e}")
            # 保持 sliced_old_code 为空字符串

    sliced_new_code = ""
    if added_or_modified_lines_new:
        try:
            sliced_new_code,_ = slice_code_from_string(
                source_code=new_code_str,
                target_lines=added_or_modified_lines_new,
                slice_type="backward",
                data_flow_only=False,
                verbose=False # 可以设置为 True 进行调试
            )
        except Exception as e:
            print(f"  新代码切片时发生错误: {e}")
            # 保持 sliced_new_code 为空字符串
    return sliced_old_code, sliced_new_code

if __name__ == "__main__":
    # 示例用法
    old_c_code = """#include <stdio.h>

int main() {
    int a = 10;
    int b = 20;
    int sum = a + b;
    printf("Sum: %d\\n", sum); // 这一行将被修改
    int diff = b - a; // 这一行将被删除
    printf("Diff: %d\\n", diff);
    return 0;
}
"""

    new_c_code = """#include <stdio.h>
#include <stdlib.h> // 新增行

int main() {
    int a = 10;
    int b = 25; // 修改行
    int sum_val = a + b; // 修改行 (变量名和值)
    printf("The Sum is: %d\\n", sum_val); // 修改行
    // diff 相关行被删除
    printf("Hello World\\n"); // 新增行
    return 0;
}
"""
    print("--- 正在处理示例 C 代码 ---")
    print("旧代码:")
    print(old_c_code)
    print("\n新代码:")
    print(new_c_code)
    
    sliced_old, sliced_new = process_code_changes(old_c_code, new_c_code)

    print("\n--- 切片结果 ---")
    print("\n旧代码的向后切片 (基于修改和删除的行):")
    if sliced_old.strip():
        print(sliced_old)
    else:
        print("(无切片结果或切片为空)")

    print("\n新代码的向后切片 (基于添加和修改的行):")
    if sliced_new.strip():
        print(sliced_new)
    else:
        print("(无切片结果或切片为空)")

    print("\n\n--- 示例 2: 无变化 ---")
    no_change_old = """int main() { return 0; }"""
    no_change_new = """int main() { return 0; }"""
    sliced_old_no_change, sliced_new_no_change = process_code_changes(no_change_old, no_change_new)
    print("\n旧代码的向后切片 (无变化):")
    print(sliced_old_no_change if sliced_old_no_change.strip() else "(无切片结果或切片为空)")
    print("\n新代码的向后切片 (无变化):")
    print(sliced_new_no_change if sliced_new_no_change.strip() else "(无切片结果或切片为空)")

    print("\n\n--- 示例 3: 仅添加 ---")
    add_only_old = """int main() { return 0; }"""
    add_only_new = """#include <stdio.h>\nint main() { printf("Hello"); return 0; }"""
    sliced_old_add_only, sliced_new_add_only = process_code_changes(add_only_old, add_only_new)
    print("\n旧代码的向后切片 (仅添加):")
    print(sliced_old_add_only if sliced_old_add_only.strip() else "(无切片结果或切片为空)")
    print("\n新代码的向后切片 (仅添加):")
    print(sliced_new_add_only if sliced_new_add_only.strip() else "(无切片结果或切片为空)")

    print("\n\n--- 示例 4: 仅删除 ---")
    del_only_old = """#include <stdio.h>\nint main() { printf("Hello"); return 0; }"""
    del_only_new = """int main() { return 0; }"""
    sliced_old_del_only, sliced_new_del_only = process_code_changes(del_only_old, del_only_new)
    print("\n旧代码的向后切片 (仅删除):")
    print(sliced_old_del_only if sliced_old_del_only.strip() else "(无切片结果或切片为空)")
    print("\n新代码的向后切片 (仅删除):")
    print(sliced_new_del_only if sliced_new_del_only.strip() else "(无切片结果或切片为空)")