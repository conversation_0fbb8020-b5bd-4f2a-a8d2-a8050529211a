import faiss
from typing import List, Optional, Dict, Tuple, Set # Added Dict, Tuple, Set
import networkx as nx # For graph operations
from utils.graphslice_analyzer import analyze_code_diff # For getting changed lines
# Placeholder for graph kernel library, e.g., from grakel.kernels import ShortestPath
from grakel.kernels import WeisfeilerLehman # Changed from ShortestPath
from grakel import graph_from_networkx
import re
import json # 用于处理LLM输出或构建复杂prompt
import difflib
import csv # Added for CSV processing
import concurrent.futures
import threading
import multiprocessing  # 添加multiprocessing支持真正的并行处理
import enum  # 用于评估结果枚举
import os
import numpy as np  # 用于embedding相似度计算
from sklearn.feature_extraction.text import TfidfVectorizer  # 用于关键变量词相似度
from sklearn.metrics.pairwise import cosine_similarity  # 用于相似度计算
from sklearn.cluster import AgglomerativeClustering  # 用于分层聚类
from slicer.parse_joern_output2 import slice_code_from_string
from utils.cwe_find import CWEProcessor
from vectorbase.query_index import VectorDatabaseQuerier
from models.OpenAI_API import generate_with_OpenAI_model
# from models.Gemini import generate_with_Gemini_model  # 添加Gemini模型用于patch评估
from utils.graphslice_analyzer import analyze_code_diff
import math
import subprocess
import tempfile
from pathlib import Path
csv.field_size_limit(10000000) 

# 评估结果枚举
class EvaluationResult(enum.Enum):
    SYNTACTIC_PATCH_EQUIVALENT = "SynPatchEq"  # 新增：补丁语法等价 (diff内容完全相同)
    SEMANTIC_EQUIVALENT = "SemEq"  # 语义等价
    PLAUSIBLE = "Plausible"        # 合理性
    INCORRECT = "Incorrect"        # 不正确
    UNKNOWN = "Unknown"            # 评估失败或未知

# Root Cause一致性评估结果枚举
class RootCauseConsistency(enum.Enum):
    HIGH = "High"           # 高度一致
    MEDIUM = "Medium"       # 中等一致
    LOW = "Low"             # 低一致性
    INCONSISTENT = "Inconsistent"  # 不一致

# Patch分组增强配置
PATCH_GROUPING_CONFIG = {
    "embedding_similarity_threshold": 0.9,  # embedding相似度阈值 (用于patch内容)
    "keyword_similarity_threshold": 0.7,    # 关键变量词相似度阈值
    "min_group_size": 2,                    # 最小分组大小
    "max_groups": 4,                        # 最大分组数量
    "enhancement_priority_boost": 0.1,      # 增强patch的优先级提升
    "weight_patch_embedding": 0.4,          # 新增：补丁内容embedding的权重
    "weight_keyword_similarity": 0.2,       # 新增：关键词相似度的权重
    "weight_strategy_embedding": 0.4        # 新增：修复策略embedding的权重
}

DEFAULT_OPENAI_PARAMS = {
    # "model_ckpt" is removed as it's now part of model_config
    "temperature": 0,
    "n": 1 # Default 'n' for single completion, can be overridden by specific calls
}

MAIN_CWE_TYPES_FOR_INDEXING = [
    "CWE-189", "CWE-254", "CWE-264", "CWE-284", "CWE-310",
    "CWE-399", "CWE-664", "CWE-682", "CWE-691", "CWE-703", "CWE-707"
]

thread_local_data = threading.local()

def get_thread_local_instances():
    """
    获取当前线程的CWEProcessor和VectorDatabaseQuerier实例
    
    使用懒加载模式：只有在线程首次调用时才初始化实例，
    这样可以避免程序启动时的初始化开销，并确保每个工作线程
    都有自己独立的实例副本。
    """
    if not hasattr(thread_local_data, 'cwe_processor'):
        thread_local_data.cwe_processor = CWEProcessor()
        thread_local_data.vector_querier = VectorDatabaseQuerier(indices_dir="/mnt/projects/unnamed/datasets/ICVul_manual_example/vectorbase/indices")
    
    return thread_local_data.cwe_processor, thread_local_data.vector_querier

def calculate_patch_embedding_similarity(patch1: str, patch2: str, embedder, verbose: bool = False) -> float:
    """
    计算两个patch的embedding相似度
    
    Args:
        patch1: 第一个patch的文本
        patch2: 第二个patch的文本
        embedder: VectorDatabaseQuerier的embedder实例
        verbose: 是否输出详细信息
        
    Returns:
        float: 相似度分数 (0-1)
    """
    try:
        if not patch1.strip() or not patch2.strip():
            return 0.0
        
        # 使用embedder计算embedding
        embedding1 = embedder.encode([patch1])
        embedding2 = embedder.encode([patch2])
        
        # 计算余弦相似度
        similarity = cosine_similarity(embedding1, embedding2)[0][0]
        
        if verbose:
            print(f"      Patch embedding相似度: {similarity:.4f}")
        
        return float(similarity)
        
    except Exception as e:
        if verbose:
            print(f"      错误: 计算patch embedding相似度失败 - {e}")
        return 0.0

def calculate_keyword_similarity(keywords1: List[str], keywords2: List[str], verbose: bool = False) -> float:
    """
    计算两组关键变量的词相似度
    
    Args:
        keywords1: 第一组关键变量列表
        keywords2: 第二组关键变量列表
        verbose: 是否输出详细信息
        
    Returns:
        float: 相似度分数 (0-1)
    """
    try:
        if not keywords1 or not keywords2:
            return 0.0
        
        # 将关键变量列表转换为文本
        text1 = " ".join(keywords1)
        text2 = " ".join(keywords2)
        
        if not text1.strip() or not text2.strip():
            return 0.0
        
        # 使用TF-IDF向量化
        vectorizer = TfidfVectorizer(lowercase=True, token_pattern=r'\b\w+\b')
        try:
            tfidf_matrix = vectorizer.fit_transform([text1, text2])
            similarity = cosine_similarity(tfidf_matrix[0:1], tfidf_matrix[1:2])[0][0]
        except ValueError:
            # 如果词汇表为空，返回0
            return 0.0
        
        if verbose:
            print(f"      关键变量词相似度: {similarity:.4f}")
        
        return float(similarity)
        
    except Exception as e:
        if verbose:
            print(f"      错误: 计算关键变量词相似度失败 - {e}")
        return 0.0

def group_patches_by_similarity(
    repair_suggestions: List[dict],
    embedder,
    config: dict = None,
    verbose: bool = False
) -> List[List[dict]]:
    """
    基于embedding相似度和关键变量词相似度对patch进行分组
    
    Args:
        repair_suggestions: 修复建议列表
        embedder: VectorDatabaseQuerier的embedder实例
        config: 分组配置参数
        verbose: 是否输出详细信息
        
    Returns:
        List[List[dict]]: 分组后的patch列表
    """
    if not repair_suggestions:
        return []
    
    if config is None:
        config = PATCH_GROUPING_CONFIG
    
    # 从配置中读取权重，如果未提供则使用默认值
    weight_patch_embedding = config.get("weight_patch_embedding", 0.5)
    weight_keyword_similarity = config.get("weight_keyword_similarity", 0.2)
    weight_strategy_embedding = config.get("weight_strategy_embedding", 0.3)
    
    if verbose:
        print(f"  开始对 {len(repair_suggestions)} 个patch进行相似度分组 (权重: PatchEmb={weight_patch_embedding}, Keyword={weight_keyword_similarity}, StrategyEmb={weight_strategy_embedding})...")
    
    # 如果patch数量太少，不进行分组
    if len(repair_suggestions) < config["min_group_size"]:
        if verbose:
            print(f"    patch数量 ({len(repair_suggestions)}) 少于最小分组大小 ({config['min_group_size']})，不进行分组")
        return [repair_suggestions]
    
    # 计算所有patch之间的相似度矩阵
    n_patches = len(repair_suggestions)
    similarity_matrix = np.zeros((n_patches, n_patches))
    
    for i in range(n_patches):
        for j in range(i + 1, n_patches):
            patch1 = repair_suggestions[i].get("suggestion_patch", "")
            patch2 = repair_suggestions[j].get("suggestion_patch", "")
            keywords1 = repair_suggestions[i].get("key_variables", [])
            keywords2 = repair_suggestions[j].get("key_variables", [])
            strategy1_text = repair_suggestions[i].get("repair_strategy", "")
            strategy2_text = repair_suggestions[j].get("repair_strategy", "")
            
            # 计算embedding相似度 (针对 patch 内容)
            embedding_sim = calculate_patch_embedding_similarity(
                patch1, patch2, embedder, verbose=False
            )
            
            # 计算关键变量词相似度
            keyword_sim = calculate_keyword_similarity(
                keywords1, keywords2, verbose=False
            )

            # 计算修复策略的embedding相似度
            strategy_sim = calculate_patch_embedding_similarity(
                strategy1_text, strategy2_text, embedder, verbose=False
            )
            
            # 综合相似度 (应用新权重)
            combined_sim = (
                weight_patch_embedding * embedding_sim +
                weight_keyword_similarity * keyword_sim +
                weight_strategy_embedding * strategy_sim
            )
            
            similarity_matrix[i][j] = combined_sim
            similarity_matrix[j][i] = combined_sim
    
    # 将相似度矩阵转换为距离矩阵 (1 - similarity)
    distance_matrix = 1.0 - similarity_matrix
    
    # 使用层次聚类进行分组
    try:
        # 自适应确定聚类数量
        max_clusters = min(config["max_groups"], n_patches)
        best_n_clusters = 1
        best_silhouette = -1
        
        # 尝试不同的聚类数量，选择最佳的
        for n_clusters in range(2, max_clusters + 1):
            clustering = AgglomerativeClustering(
                n_clusters=n_clusters,
                metric='precomputed',
                linkage='average'
            )
            cluster_labels = clustering.fit_predict(distance_matrix)
            
            # 简单的聚类质量评估：检查组内平均相似度
            group_quality = 0.0
            for cluster_id in range(n_clusters):
                cluster_indices = np.where(cluster_labels == cluster_id)[0]
                if len(cluster_indices) >= config["min_group_size"]:
                    # 计算组内平均相似度
                    group_similarities = []
                    for i in cluster_indices:
                        for j in cluster_indices:
                            if i != j:
                                group_similarities.append(similarity_matrix[i][j])
                    if group_similarities:
                        avg_sim = np.mean(group_similarities)
                        group_quality += avg_sim
            
            if group_quality > best_silhouette:
                best_silhouette = group_quality
                best_n_clusters = n_clusters
        
        # 使用最佳聚类数量进行最终聚类
        clustering = AgglomerativeClustering(
            n_clusters=best_n_clusters,
            metric='precomputed',
            linkage='average'
        )
        cluster_labels = clustering.fit_predict(distance_matrix)
        
        # 将patch按聚类结果分组
        groups = []
        for cluster_id in range(best_n_clusters):
            cluster_indices = np.where(cluster_labels == cluster_id)[0]
            if len(cluster_indices) >= config["min_group_size"]:
                group = [repair_suggestions[i] for i in cluster_indices]
                groups.append(group)
            else:
                # 小组合并到最大的组中
                if groups:
                    largest_group_idx = max(range(len(groups)), key=lambda x: len(groups[x]))
                    for i in cluster_indices:
                        groups[largest_group_idx].append(repair_suggestions[i])
                else:
                    # 如果还没有组，创建一个新组
                    group = [repair_suggestions[i] for i in cluster_indices]
                    groups.append(group)
        
        if verbose:
            print(f"    成功分成 {len(groups)} 组:")
            for i, group in enumerate(groups):
                print(f"      组 {i+1}: {len(group)} 个patch")
                # 计算组内平均相似度
                if len(group) > 1:
                    group_similarities = []
                    for j in range(len(group)):
                        for k in range(j + 1, len(group)):
                            patch_j = group[j].get("suggestion_patch", "")
                            patch_k = group[k].get("suggestion_patch", "")
                            keywords_j = group[j].get("key_variables", [])
                            keywords_k = group[k].get("key_variables", [])
                            strategy_j_text = group[j].get("repair_strategy", "")
                            strategy_k_text = group[k].get("repair_strategy", "")
                            
                            embedding_sim_group = calculate_patch_embedding_similarity(
                                patch_j, patch_k, embedder, verbose=False
                            )
                            keyword_sim_group = calculate_keyword_similarity(
                                keywords_j, keywords_k, verbose=False
                            )
                            strategy_sim_group = calculate_patch_embedding_similarity(
                                strategy_j_text, strategy_k_text, embedder, verbose=False
                            )
                            combined_sim_group = (
                                weight_patch_embedding * embedding_sim_group +
                                weight_keyword_similarity * keyword_sim_group +
                                weight_strategy_embedding * strategy_sim_group
                            )
                            group_similarities.append(combined_sim_group)
                    
                    if group_similarities:
                        avg_sim = np.mean(group_similarities)
                        print(f"        组内平均相似度: {avg_sim:.4f}")
        
        return groups
        
    except Exception as e:
        if verbose:
            print(f"    错误: 聚类分组失败 - {e}，返回单个组")
        return [repair_suggestions]

def generate_enhanced_patch_for_group(
    group_patches: List[dict],
    vulnerable_code: str,
    cwe_id: str,
    vulnerable_line_numbers: List[int],
    group_id: int,
    model_config: dict, # 新增: 用于LLM调用的模型配置
    verbose: bool = False
) -> Optional[dict]:
    """
    为一组patch生成增强的安全修复patch
    
    Args:
        group_patches: 同组的patch列表
        vulnerable_code: 原始漏洞代码
        cwe_id: CWE漏洞类型ID
        vulnerable_line_numbers: 漏洞行号列表
        group_id: 组ID
        model_config: 用于LLM调用的模型配置
        verbose: 是否输出详细信息
        
    Returns:
        Optional[dict]: 增强的patch建议，如果生成失败则返回None
    """
    if not group_patches:
        return None
    
    if verbose:
        print(f"    为组 {group_id} 生成增强patch (包含 {len(group_patches)} 个原始patch)...")
    
    # 收集组内所有patch的信息
    all_patches = []
    all_strategies = []
    all_key_variables = set()
    all_root_causes = []
    
    for patch_data in group_patches:
        patch = patch_data.get("suggestion_patch", "")
        strategy = patch_data.get("repair_strategy", "")
        key_vars = patch_data.get("key_variables", [])
        root_cause = patch_data.get("source_root_cause_desc", "")
        
        if patch.strip():
            all_patches.append(patch)
        if strategy.strip():
            all_strategies.append(strategy)
        if key_vars:
            all_key_variables.update(key_vars)
        if root_cause.strip():
            all_root_causes.append(root_cause)
    
    if not all_patches:
        if verbose:
            print(f"      警告: 组 {group_id} 中没有有效的patch")
        return None
    
    # 构建增强prompt
    patches_section = ""
    for i, patch in enumerate(all_patches):
        patches_section += f"\n**Patch {i+1}:**\n```diff\n{patch}\n```\n"
    
    strategies_section = ""
    for i, strategy in enumerate(all_strategies):
        strategies_section += f"\n**Strategy {i+1}:** {strategy}\n"
    
    key_variables_list = list(all_key_variables)
    root_causes_section = ""
    for i, root_cause in enumerate(set(all_root_causes)):  # 去重
        root_causes_section += f"\n**Root Cause {i+1}:** {root_cause}\n"
    
    formatted_vulnerable_lines = format_lines_with_statements(vulnerable_code, vulnerable_line_numbers)
    enhancement_prompt = f"""
You are an expert security code repair specialist. Your task is to analyze multiple patch proposals for the same vulnerability and synthesize a superior, more precise patch by following a structured three-step process.

**Original Vulnerable Code (CWE: {cwe_id}):**
```
{add_line_numbers_to_code(vulnerable_code)}
```

**Vulnerable Lines:**
{formatted_vulnerable_lines}

**Multiple Patch Proposals to Analyze:**
{patches_section}

**Repair Strategies from Proposals:**
{strategies_section}

**Candidate Root Causes:**
{root_causes_section}

**Patch Enhancement Task (Follow these three steps meticulously):**

**Step 1: Identify Core Repair Logic and Consolidate Comprehensive Repair Patterns**
- For each patch proposal, meticulously identify its **core vulnerability-fixing logic**.
- When **identical vulnerability-fixing logic** is found across multiple patches, select the **most appropriate, precise, concise, and robust** repair pattern among them.
- When **different vulnerability-fixing logic** is identified, critically analyze whether each piece of logic is **necessary and beneficial** for a comprehensive fix. If so, integrate these varied logics thoughtfully.
- Combine the selected and integrated core repair logics to form one or more **comprehensive repair patterns** that effectively address the vulnerability.

**Step 2: Analyze and Eliminate Irrelevant Modification Patterns**
- Scrutinize all patch proposals to identify any modification patterns that are **not directly related to fixing the identified vulnerability**. These could include stylistic changes, unrelated refactoring, or other non-essential code alterations.
- **Decisively remove these redundant or irrelevant modifications**. The goal is to isolate changes that purely address the security issue.

**Step 3: Optimize Patch with Project-Specific Code Characteristics**
- Based on the refined and consolidated repair patterns from Step 1 and the elimination of irrelevant changes from Step 2, proceed to optimize the final patch.
- Leverage the **unique code characteristics of the project** where the vulnerable code resides. This includes, but is not limited to:
    - Specific **APIs** commonly used within the project.
    - Project-defined **macro definitions**.
    - Prevailing **identifier naming conventions** (for variables, functions, etc.).
    - Typical **pointer usage patterns** and **struct/class definitions** specific to the codebase.
- Adapt the patch to seamlessly align with these project-specific characteristics. This ensures the patch is not only correct but also well-integrated, maintainable, and robust within the context of the existing codebase.

**Output Format:**
Please provide your response in the following structured format:

PATTERN_ANALYSIS_START:
[Describe the comprehensive repair pattern(s) you identified and consolidated in Step 1. Explain the core logic and why it's effective.]
PATTERN_ANALYSIS_END:

PRECISION_ANALYSIS_START:
[Explain how you ensured the fix is precise and minimal, detailing the core logic selected/integrated from Step 1 and how irrelevant modifications were handled in Step 2.]
PRECISION_ANALYSIS_END:

IRRELEVANT_MODIFICATIONS_START:
[List any significant modification patterns found in the original proposals that you identified as irrelevant to the vulnerability fix and consequently removed in Step 2. Explain briefly why they were deemed irrelevant.]
IRRELEVANT_MODIFICATIONS_END:

NEW_IDEA_START:
[If, during Step 3 (optimization with project-specific characteristics) or your overall analysis, you developed any novel approaches or significant improvements not directly derivable from the input patches, describe them here. Otherwise, state "No new ideas beyond synthesis and optimization."]
NEW_IDEA_END:

SYNTHESIS_SUMMARY_START:
[Provide a concise summary of your synthesis process following the three steps:
1. How core repair logics were identified and consolidated.
2. How irrelevant modifications were eliminated.
3. How project-specific code characteristics were used to optimize the final patch.]
SYNTHESIS_SUMMARY_END:

SECURITY_ANALYSIS_START:
[Analyze how the synthesized patch, developed through the three-step process, provides robust security coverage for the identified vulnerability, potentially being superior to individual proposals.]
SECURITY_ANALYSIS_END:

KEY_VARIABLES_START:
[List the key variables, functions, or crucial program elements that are central to the **final synthesized patch**, separated by commas.]
KEY_VARIABLES_END:

ENHANCEMENT_STRATEGY_START:
[Explain your overall enhancement strategy based on the three steps:
- Step 1 (Consolidation): Detail the logic for choosing and integrating repair patterns.
- Step 2 (Elimination): Justify the removal of irrelevant changes.
- Step 3 (Optimization): Describe how project-specifics improved the patch.
Conclude why this structured approach yields a superior, precise, and well-integrated security patch.]
ENHANCEMENT_STRATEGY_END:

ENHANCED_PATCH_DIFF_START:
[Your synthesized unified diff patch. This patch must be the direct result of the three-step process, focusing exclusively on the essential security fix, demonstrating precision, and incorporating project-specific optimizations.]
ENHANCED_PATCH_DIFF_END:
"""

    try:
        # 调用LLM生成增强patch
        api_call_params = {
            "prompt": enhancement_prompt,
            "model_config": model_config,
            "max_tokens": 16000, # Specific to this call
            "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0), # Use default or 0
            "n": DEFAULT_OPENAI_PARAMS.get("n", 1) # Use default or 1
        }
        
        response_list = generate_with_OpenAI_model(**api_call_params)
        
        if not response_list or not response_list[0]:
            if verbose:
                print(f"      错误: LLM未返回有效响应 (组 {group_id})")
            return None
        
        response_text = response_list[0].strip()
        
        # 解析结构化响应
        enhanced_patch = None
        enhancement_strategy = None
        security_analysis = None
        key_variables = []
        pattern_analysis = None
        precision_analysis = None
        irrelevant_modifications = None
        synthesis_summary = None
        
        # 提取pattern_analysis
        pattern_match = re.search(r'PATTERN_ANALYSIS_START:\s*(.*?)\s*PATTERN_ANALYSIS_END:', response_text, re.DOTALL)
        if pattern_match:
            pattern_analysis = pattern_match.group(1).strip()
        
        # 提取precision_analysis
        precision_match = re.search(r'PRECISION_ANALYSIS_START:\s*(.*?)\s*PRECISION_ANALYSIS_END:', response_text, re.DOTALL)
        if precision_match:
            precision_analysis = precision_match.group(1).strip()
        
        # 提取irrelevant_modifications
        irrelevant_match = re.search(r'IRRELEVANT_MODIFICATIONS_START:\s*(.*?)\s*IRRELEVANT_MODIFICATIONS_END:', response_text, re.DOTALL)
        if irrelevant_match:
            irrelevant_modifications = irrelevant_match.group(1).strip()
        
        # 提取enhanced_patch
        patch_match = re.search(r'ENHANCED_PATCH_DIFF_START:\s*(.*?)\s*ENHANCED_PATCH_DIFF_END:', response_text, re.DOTALL)
        if patch_match:
            enhanced_patch = patch_match.group(1).strip()
        
        # 提取enhancement_strategy
        strategy_match = re.search(r'ENHANCEMENT_STRATEGY_START:\s*(.*?)\s*ENHANCEMENT_STRATEGY_END:', response_text, re.DOTALL)
        if strategy_match:
            enhancement_strategy = strategy_match.group(1).strip()
        
        # 提取security_analysis
        security_match = re.search(r'SECURITY_ANALYSIS_START:\s*(.*?)\s*SECURITY_ANALYSIS_END:', response_text, re.DOTALL)
        if security_match:
            security_analysis = security_match.group(1).strip()
        
        # 提取key_variables
        variables_match = re.search(r'KEY_VARIABLES_START:\s*(.*?)\s*KEY_VARIABLES_END:', response_text, re.DOTALL)
        if variables_match:
            variables_text = variables_match.group(1).strip()
            if variables_text:
                key_variables = [var.strip() for var in variables_text.split(',') if var.strip()]
        
        # 提取synthesis_summary
        synthesis_match = re.search(r'SYNTHESIS_SUMMARY_START:\s*(.*?)\s*SYNTHESIS_SUMMARY_END:', response_text, re.DOTALL)
        if synthesis_match:
            synthesis_summary = synthesis_match.group(1).strip()
        
        # 验证必需字段
        if not enhanced_patch or not enhanced_patch.strip():
            if verbose:
                print(f"      错误: 增强patch为空 (组 {group_id})")
            return None
        
        if not enhancement_strategy or not enhancement_strategy.strip():
            if verbose:
                print(f"      错误: 增强策略为空 (组 {group_id})")
            return None
        
        # 构建增强patch建议对象
        enhanced_suggestion = {
            "suggestion_patch": enhanced_patch,
            "repair_strategy": enhancement_strategy,
            "key_variables": key_variables,
            "source_root_cause_desc": f"Enhanced from group {group_id} ({len(group_patches)} patches)",
            "source_example_pre_repair_state": "Enhanced patch - multiple examples combined",
            "source_example_post_repair_state": security_analysis or "Enhanced security patch",
            "source_example_code_before": "N/A",
            "source_example_code_after": "N/A",
            "source_example_distance": -2.0,  # 特殊标识为增强patch
            "llm_score": 1.0 + PATCH_GROUPING_CONFIG["enhancement_priority_boost"],  # 增强patch优先级更高
            "repair_method": "enhanced_group",
            "group_id": group_id,
            "original_patches_count": len(group_patches),
            "security_analysis": security_analysis,
            "pattern_analysis": pattern_analysis,
            "precision_analysis": precision_analysis,
            "irrelevant_modifications": irrelevant_modifications,
            "synthesis_summary": synthesis_summary
        }
        
        if verbose:
            print(f"      成功生成组 {group_id} 的增强patch")
            print(f"      增强策略: {enhancement_strategy[:100]}...")
            print(f"      关键变量: {key_variables}")
        
        return enhanced_suggestion
        
    except Exception as e:
        if verbose:
            print(f"      错误: 生成组 {group_id} 增强patch时发生异常 - {e}")
        return None

def apply_patch_grouping_enhancement(
    repair_suggestions: List[dict],
    vulnerable_code: str,
    cwe_id: str,
    vulnerable_line_numbers: List[int],
    model_config: dict, # 新增: 用于LLM调用的模型配置
    enable_patch_grouping: bool = False,
    config: dict = None,
    verbose: bool = False
) -> List[dict]:
    """
    应用patch分组增强算法
    
    Args:
        repair_suggestions: 原始修复建议列表
        vulnerable_code: 原始漏洞代码
        cwe_id: CWE漏洞类型ID
        vulnerable_line_numbers: 漏洞行号列表
        enable_patch_grouping: 是否启用patch分组增强
        config: 分组配置参数
        verbose: 是否输出详细信息
        
    Returns:
        List[dict]: 包含增强patch的修复建议列表（增强patch在前）
    """
    if not enable_patch_grouping or not repair_suggestions:
        return repair_suggestions
    
    if config is None:
        config = PATCH_GROUPING_CONFIG
    
    if verbose:
        print(f"  启用patch分组增强算法...")
    
    # 获取embedder实例
    try:
        _, vector_querier = get_thread_local_instances()
        embedder = vector_querier.embedder
    except Exception as e:
        if verbose:
            print(f"    错误: 无法获取embedder实例 - {e}，跳过分组增强")
        return repair_suggestions
    
    # 步骤1: 对patch进行分组
    patch_groups = group_patches_by_similarity(
        repair_suggestions=repair_suggestions,
        embedder=embedder,
        config=config,
        verbose=verbose
    )
    
    if len(patch_groups) <= 1:
        if verbose:
            print(f"    patch未能有效分组 (组数: {len(patch_groups)})，跳过增强")
        return repair_suggestions
    
    # 步骤2: 为每组生成增强patch
    enhanced_patches_results = [] # Store tuples of (group_id, patch) for ordered insertion
    with concurrent.futures.ThreadPoolExecutor(max_workers=32) as executor:
        future_to_group_id = {}
        for group_id, group_patches in enumerate(patch_groups):
            if len(group_patches) >= config["min_group_size"]:
                future = executor.submit(generate_enhanced_patch_for_group,
                                         group_patches=group_patches,
                                         vulnerable_code=vulnerable_code,
                                         cwe_id=cwe_id,
                                         vulnerable_line_numbers=vulnerable_line_numbers,
                                         group_id=group_id + 1,
                                         model_config=model_config, # 传递模型配置
                                         verbose=verbose)
                future_to_group_id[future] = group_id + 1
       
        for future in concurrent.futures.as_completed(future_to_group_id):
                original_group_id = future_to_group_id[future]
                try:
                    enhanced_patch = future.result()
                    if enhanced_patch:
                        enhanced_patches_results.append((original_group_id, enhanced_patch))
                except Exception as exc:
                    if verbose:
                        print(f"    错误: 为组 {original_group_id} 生成增强patch时发生异常 (executor): {exc}")

    # Sort by original_patches_count in descending order to prioritize larger groups
    enhanced_patches_results.sort(key=lambda x: x[1].get('original_patches_count', 0), reverse=True)
    enhanced_patches = [item[1] for item in enhanced_patches_results]
    
    if not enhanced_patches:
        if verbose:
            print(f"    未能生成任何增强patch，返回原始建议")
        return repair_suggestions
    
    # 步骤3: 将增强patch添加到候选列表前面
    if verbose:
        print(f"    成功生成 {len(enhanced_patches)} 个增强patch，将其添加到候选列表前面")
    
    # 增强patch优先级更高，放在前面
    final_suggestions = enhanced_patches + repair_suggestions
    
    return final_suggestions

# --- Helper functions for graph consistency ---

SIMILARITY_THRESHOLD = 0.6  # For graph consistency analysis

def get_graph_for_code(repaired_code_string: str, original_code_string: str, verbose: bool = False) -> Optional[nx.DiGraph]:
    """
    Generates a focused graph for the repaired code based on changes from the original code.
    The graph focuses on the lines that were added or modified in the repaired code.
    """
    if not repaired_code_string:
        if verbose:
            print("  get_graph_for_code: Repaired code string is empty.")
        return None

    # Get lines that were added or modified in the new (repaired) code
    _, added_or_modified_lines_new = analyze_code_diff(original_code_string, repaired_code_string)

    if not added_or_modified_lines_new:
        if verbose:
            print("  get_graph_for_code: No added or modified lines found between original and repaired code. Cannot generate focused graph.")
        # Fallback: could generate graph for the whole repaired_code_string without target_lines,
        # but the plan is to focus on changes. For now, return None if no changes.
        return None

    try:
        if verbose:
            print(f"  get_graph_for_code: Slicing repaired code based on {len(added_or_modified_lines_new)} changed lines: {added_or_modified_lines_new}")
        
        # slice_code_from_string is already imported from slicer.parse_joern_output2
        _sliced_code_str, graph = slice_code_from_string(
            source_code=repaired_code_string,
            target_lines=added_or_modified_lines_new,
            slice_type="combined", # Or "forward" / "backward" if more appropriate for context
            data_flow_only=True, # Capturing more than just DDG for structure
            verbose=verbose,
            return_graph=True,
            graph_detail_level='slice', # Focus on the slice related to changes
            program_graph_edge_types='ddg' # As per user's latest requirement
        )
        if graph is not None and isinstance(graph, nx.DiGraph):
            if verbose:
                print(f"  get_graph_for_code: Successfully generated graph with {graph.number_of_nodes()} nodes and {graph.number_of_edges()} edges.")
            return graph
        else:
            if verbose:
                print("  get_graph_for_code: slice_code_from_string did not return a valid graph.")
            return None
    except Exception as e:
        if verbose:
            print(f"  get_graph_for_code: Error during slice_code_from_string: {e}")
        return None

def map_key_variables_to_node_ids(graph: nx.DiGraph, key_variables: List[str], verbose: bool = False) -> List[str]:
    """
    Maps string key variables to node IDs in the graph.
    Matches if a normalized key variable is contained within a normalized node attribute (name or code).
    """
    matched_node_ids: Set[str] = set()
    if not key_variables or graph.number_of_nodes() == 0:
        return []

    normalized_key_vars = [kv.replace(" ", "").lower() for kv in key_variables if kv]

    for node_id, attrs in graph.nodes(data=True):
        node_code_str = str(attrs.get('code', ''))
        node_name_str = str(attrs.get('name', ''))

        normalized_node_code = node_code_str.replace(" ", "").lower()
        normalized_node_name = node_name_str.replace(" ", "").lower()

        for nk_var in normalized_key_vars:
            if (normalized_node_code and nk_var in normalized_node_code) or \
               (normalized_node_name and nk_var in normalized_node_name):
                matched_node_ids.add(str(node_id)) # Ensure node_id is string
                # if verbose:
                #     print(f"  map_key_variables_to_node_ids: Matched var '{nk_var}' to node '{node_id}' (name: '{node_name_str}', code: '{node_code_str[:30]}...')")
                break # Found a match for this node, no need to check other key_vars for the same node
    
    return sorted(list(matched_node_ids))

def extract_subgraph_for_variables(focused_graph: nx.DiGraph, target_node_ids: List[str], verbose: bool = False) -> Optional[nx.DiGraph]:
    """
    Extracts a subgraph containing target_node_ids and their 1-hop neighbors from the focused_graph.
    """
    if not target_node_ids or focused_graph.number_of_nodes() == 0:
        if verbose:
            print("  extract_subgraph_for_variables: No target node IDs or graph is empty.")
        return None

    nodes_for_subgraph: Set[str] = set()
    
    valid_target_node_ids = [nid for nid in target_node_ids if nid in focused_graph]
    if not valid_target_node_ids:
        if verbose:
            print(f"  extract_subgraph_for_variables: None of the target_node_ids {target_node_ids} found in the graph.")
        return None
        
    for node_id in valid_target_node_ids:
        nodes_for_subgraph.add(node_id)
        # Add 1-hop neighbors (predecessors and successors)
        # Ensure neighbors are also strings if graph node IDs are mixed type, though they should be consistent from Joern.
        nodes_for_subgraph.update(map(str, focused_graph.predecessors(node_id)))
        nodes_for_subgraph.update(map(str, focused_graph.successors(node_id)))
    
    # Filter out any node_ids that might not actually be in the graph (e.g. if a predecessor/successor was somehow not a node)
    # Though .subgraph() handles this gracefully.
    final_nodes_for_subgraph = {nid for nid in nodes_for_subgraph if nid in focused_graph}

    if not final_nodes_for_subgraph:
        if verbose:
            print("  extract_subgraph_for_variables: Resulting node set for subgraph is empty.")
        return None
        
    subgraph = focused_graph.subgraph(final_nodes_for_subgraph).copy()
    if verbose:
        print(f"  extract_subgraph_for_variables: Extracted subgraph with {subgraph.number_of_nodes()} nodes and {subgraph.number_of_edges()} edges.")
    return subgraph

def calculate_graph_similarity(graph1: nx.DiGraph, graph2: nx.DiGraph, verbose: bool = False) -> float:
    """
    Calculates graph similarity using the Weisfeiler-Lehman kernel from GraKeL.
    Node labels are a combination of '_label' and 'code'.
    Edge labels are based on 'flow_type'.
    """
    if graph1 is None or graph2 is None:
        if verbose:
            print("  calculate_graph_similarity: One or both graphs are None. Returning 0.0 similarity.")
        return 0.0
    if graph1.number_of_nodes() == 0 and graph2.number_of_nodes() == 0:
        if verbose:
            print("  calculate_graph_similarity: Both graphs are empty (0 nodes). Returning 1.0 similarity (identical empty graphs).")
        return 1.0
    if graph1.number_of_nodes() == 0 or graph2.number_of_nodes() == 0:
        if verbose:
            print("  calculate_graph_similarity: One graph is empty, the other is not. Returning 0.0 similarity.")
        return 0.0

    try:
        # Preprocess graphs to create combined_label for nodes
        processed_graphs = []
        for g_idx, g_orig in enumerate([graph1, graph2]):
            g_copy = g_orig.copy() # Work on a copy to not modify original graphs
            for node_id, attributes in g_copy.nodes(data=True):
                label_part = attributes.get('_label', 'NOLABEL')
                code_part = attributes.get('code', 'NOCODE')
                # Ensure parts are strings and handle potential long code snippets if necessary (e.g., truncate or hash)
                # For now, direct concatenation. Consider max length for code_part if it causes issues.
                attributes['combined_label'] = f"{str(label_part)}_{str(code_part)}"
            processed_graphs.append(g_copy)
        
        # Convert NetworkX graphs to GraKeL format
        # Node labels: 'combined_label'
        # Edge labels: 'flow_type' (as per user request, derived in slicer from 'etype' like 'REACHES' or 'FLOWS_TO')
        # Ensure 'flow_type' exists on edges. If not, GraKeL might default or error.
        # The slicer's build_full_graph_nx adds 'flow_type' for 'REACHES' (data) and 'FLOWS_TO' (control).
        gk_graphs = graph_from_networkx(
            processed_graphs,
            node_labels_tag='combined_label',
            edge_labels_tag='flow_type' # Using 'flow_type' as requested
        )

        # Initialize Weisfeiler-Lehman kernel
        # n_iter: Number of iterations for the WL relabeling process.
        # normalize: Whether to normalize the kernel values.
        # n_jobs: Number of parallel jobs. -1 uses all available cores.
        #         Check GraKeL documentation for n_jobs support in specific versions/kernels.
        #         If n_jobs causes issues, remove it or set to None/1.
        try:
            gk = WeisfeilerLehman(n_iter=5, normalize=True, verbose=verbose, n_jobs=-1)
        except TypeError: # Older GraKeL might not have n_jobs or verbose in constructor
             gk = WeisfeilerLehman(n_iter=5, normalize=True) # Fallback

        # Calculate kernel matrix
        kernel_matrix = gk.fit_transform(gk_graphs)
        similarity = kernel_matrix[0, 1]
        
        if verbose:
            print(f"  calculate_graph_similarity: Weisfeiler-Lehman kernel similarity = {similarity:.4f}")
        return similarity

    except Exception as e:
        if verbose:
            print(f"  calculate_graph_similarity: Error during Weisfeiler-Lehman kernel calculation: {e}. Returning 0.0.")
        return 0.0

# 新增辅助函数：识别关键变量并生成聚焦切片
def identify_key_variables_and_generate_focused_slice(
    vulnerable_code: str, 
    cwe_id: str,
    vulnerable_line_numbers: List[int],
    model_config: dict, # 新增: 用于LLM调用的模型配置
    verbose: bool = False
) -> Tuple[str, List[int], List[dict]]:
    """
    识别关键变量并生成聚焦代码切片，类似csv_strategy_analyzer.py的方法
    
    Args:
        vulnerable_code: 包含漏洞的代码
        cwe_id: CWE漏洞类型ID
        vulnerable_line_numbers: 漏洞所在行号列表
        model_config: 用于LLM调用的模型配置
        verbose: 是否输出详细信息
        
    Returns:
        Tuple[str, List[int], List[dict]]: (聚焦切片, 关键变量行号, 关键元素信息)
    """
    code_before_numbered = add_line_numbers_to_code(vulnerable_code)
    formatted_vulnerable_lines = format_lines_with_statements(vulnerable_code, vulnerable_line_numbers)
    
    prompt_identify_key_variables = f"""Analyze the following vulnerable code to identify key variables, functions, and program elements that are most relevant to the {cwe_id} vulnerability.

CWE Type: {cwe_id}
Vulnerable Lines (approximate):
{formatted_vulnerable_lines}

Vulnerable Code:
```
{code_before_numbered}
```

Task: Identify the most important variables, functions, and program elements that are directly related to the vulnerability. Focus on:
1. Variables that handle user input or external data
2. Functions that process or validate data
3. Control flow elements that affect security
4. Any other critical elements specific to {cwe_id} vulnerabilities

For each identified element, provide the specific line numbers where they appear in the vulnerable code.

Output STRICTLY in the following JSON format:
```json
{{
  "key_elements": [
    {{"name": "variable_or_function_name", "lines": [line_num1, line_num2], "type": "variable|function|operation", "relevance": "brief description of why this is relevant to the vulnerability"}},
    ...
  ]
}}
```

Focus on the most critical elements - typically 3-8 key elements should be sufficient to capture the vulnerability's essence.
"""

    key_variable_lines = []
    key_elements_info = []
    
    try:
        api_call_params_key_vars = {
            "prompt": prompt_identify_key_variables,
            "model_config": model_config,
            "max_tokens": 3000, # Specific to this call
            "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0),
            "n": DEFAULT_OPENAI_PARAMS.get("n", 1)
        }
        response_text_key_vars_list = generate_with_OpenAI_model(**api_call_params_key_vars)
        
        if response_text_key_vars_list and response_text_key_vars_list[0]:
            response_text_key_vars = response_text_key_vars_list[0]
            # 尝试从LLM响应中提取JSON部分
            json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text_key_vars)
            if not json_match:
                json_match = re.search(r'({[\s\S]*})', response_text_key_vars)

            if json_match:
                json_str = json_match.group(1)
                try:
                    parsed_response = json.loads(json_str)
                    if "key_elements" in parsed_response and isinstance(parsed_response["key_elements"], list):
                        for element in parsed_response["key_elements"]:
                            if element.get("lines") and isinstance(element["lines"], list):
                                key_variable_lines.extend(element["lines"])
                                key_elements_info.append(element)
                        # 去重并排序
                        key_variable_lines = sorted(list(set(key_variable_lines)))
                        if verbose:
                            print(f"识别到关键变量相关行号: {key_variable_lines}")
                    else:
                        if verbose:
                            print(f"警告: LLM未能按预期格式返回关键变量信息。响应: {response_text_key_vars}")
                except json.JSONDecodeError as je:
                    if verbose:
                        print(f"警告: 解析LLM关键变量响应时JSON解码失败: {je}. 响应: {response_text_key_vars}")
            else:
                if verbose:
                    print(f"警告: 在LLM关键变量响应中未找到JSON块。响应: {response_text_key_vars}")
        else:
            if verbose:
                print("警告: LLM未能生成关键变量分析。")

    except Exception as e:
        if verbose:
            print(f"错误: 识别关键变量时发生异常 - {e}")

    # 如果没有识别到关键变量行号，回退到原始的漏洞行号
    if not key_variable_lines:
        if verbose:
            print("未能识别到关键变量行号，使用原始漏洞行号作为回退。")
        key_variable_lines = vulnerable_line_numbers

    # 基于关键变量行号生成聚焦代码切片
    focused_code_slice = ""
    try:
        focused_code_slice, _ = slice_code_from_string(
            source_code=vulnerable_code,
            target_lines=key_variable_lines,
            slice_type="combined",
            data_flow_only=False,
            verbose=False
        )
        if not focused_code_slice.strip():
            if verbose:
                print("警告: 基于关键变量生成的聚焦代码切片为空。将使用原始代码作为回退。")
            focused_code_slice = vulnerable_code
        else:
            if verbose:
                print(f"成功生成聚焦代码切片 (基于行号: {key_variable_lines})")
    except Exception as e:
        if verbose:
            print(f"错误: 生成聚焦代码切片失败 - {e}. 将使用原始代码作为回退。")
        focused_code_slice = vulnerable_code

    return focused_code_slice, key_variable_lines, key_elements_info

def identify_root_causes_and_key_variables(
    vulnerable_code: str,
    cwe_id: str,
    vulnerable_line_numbers: List[int],
    model_config: dict, # 新增: 用于LLM调用的模型配置
    num_root_causes_to_analyze: int = 3,
    verbose: bool = False
) -> List[dict]:
    """
    识别根本原因，同时输出关键变量和对应行号
    
    Args:
        vulnerable_code: 包含漏洞的代码
        cwe_id: CWE漏洞类型ID
        vulnerable_line_numbers: 漏洞所在行号列表
        model_config: 用于LLM调用的模型配置
        num_root_causes_to_analyze: 要分析的根本原因数量
        verbose: 是否输出详细信息
        
    Returns:
        List[dict]: 根本原因列表，每个包含description和key_variable_lines
    """
    code_before_numbered = add_line_numbers_to_code(vulnerable_code)
    formatted_vulnerable_lines = format_lines_with_statements(vulnerable_code, vulnerable_line_numbers)
    
    prompt_identify_root_causes = f"""Analyze the following vulnerable code to identify potential root causes (pre-repair states) of the {cwe_id} vulnerability. For each root cause, also identify the key variables and line numbers that are most relevant to that specific root cause.

CWE Type: {cwe_id}
Vulnerable Lines (approximate):
{formatted_vulnerable_lines}

Vulnerable Code:
```
{code_before_numbered}
```

Task: 
1. Identify up to {num_root_causes_to_analyze} distinct and most likely root causes (pre-repair states) for the vulnerability
2. For each root cause, identify the specific variables, functions, and line numbers that are most relevant to that root cause
3. Ensure that the identified root causes are as diverse as possible
4. Consider the provided CWE ID and its typical characteristics when formulating the root causes
5. Focus on the conditions or program states that allow the vulnerability to manifest
6. Every root cause should be reasoning step by step from source to sink detailedly

Output STRICTLY in the following JSON format:
```json
{{
  "root_causes": [
    {{
      "id": 1,
      "description": "Detailed description of root cause 1, explaining the vulnerability mechanism step by step",
      "key_variables": ["var1", "var2", "function1"],
      "key_variable_lines": [line_num1, line_num2, line_num3],
      "relevance_explanation": "Why these specific lines and variables are critical for this root cause"
    }},
    {{
      "id": 2,
      "description": "Detailed description of root cause 2, explaining the vulnerability mechanism step by step",
      "key_variables": ["var3", "var4", "function2"],
      "key_variable_lines": [line_num4, line_num5, line_num6],
      "relevance_explanation": "Why these specific lines and variables are critical for this root cause"
    }}
  ]
}}
```

Important: Each root cause should focus on different aspects of the vulnerability and have its own set of relevant lines and variables.
"""

    root_causes_with_variables = []
    
    try:
        api_call_params_root_cause = {
            "prompt": prompt_identify_root_causes,
            "model_config": model_config,
            "max_tokens": 5000, # Specific to this call
            "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0),
            "n": DEFAULT_OPENAI_PARAMS.get("n", 1)
        }
        response_text_root_cause_list = generate_with_OpenAI_model(**api_call_params_root_cause)
        
        if response_text_root_cause_list and response_text_root_cause_list[0]:
            response_text_root_cause = response_text_root_cause_list[0]
            # 尝试从LLM响应中提取JSON部分
            json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text_root_cause)
            if not json_match: # 如果没有markdown格式的json，尝试直接解析
                 json_match = re.search(r'({[\s\S]*})', response_text_root_cause)

            if json_match:
                json_str = json_match.group(1)
                try:
                    parsed_response = json.loads(json_str)
                    if "root_causes" in parsed_response and isinstance(parsed_response["root_causes"], list):
                        for rc in parsed_response["root_causes"]:
                            if rc.get("description") and rc.get("key_variable_lines"):
                                # 确保key_variable_lines是整数列表
                                key_lines = []
                                try:
                                    key_lines = [int(line) for line in rc["key_variable_lines"] if isinstance(line, (int, str)) and str(line).isdigit()]
                                except (ValueError, TypeError):
                                    if verbose:
                                        print(f"警告: 根本原因 {rc.get('id', 'unknown')} 的key_variable_lines格式错误，使用原始漏洞行号")
                                    key_lines = vulnerable_line_numbers
                                
                                if not key_lines:  # 如果没有有效的行号，使用原始漏洞行号
                                    key_lines = vulnerable_line_numbers
                                
                                root_causes_with_variables.append({
                                    "description": rc["description"],
                                    "key_variable_lines": key_lines,
                                    "key_variables": rc.get("key_variables", []),
                                    "relevance_explanation": rc.get("relevance_explanation", "")
                                })
                        
                        # 确保不超过指定数量
                        root_causes_with_variables = root_causes_with_variables[:num_root_causes_to_analyze]
                        
                        if verbose:
                            print(f"成功识别 {len(root_causes_with_variables)} 个根本原因及其关键变量行号")
                    else:
                        print(f"警告: LLM未能按预期格式返回根本原因 (缺少 'root_causes' 列表)。响应: {response_text_root_cause}")
                except json.JSONDecodeError as je:
                    print(f"警告: 解析LLM根本原因响应时JSON解码失败: {je}. 响应: {response_text_root_cause}")
            else:
                print(f"警告: 在LLM根本原因响应中未找到JSON块。响应: {response_text_root_cause}")
        else:
            print("警告: LLM未能生成根本原因分析。")

    except Exception as e:
        print(f"错误: 分析根本原因时发生异常 - {e}")

    return root_causes_with_variables

def refine_root_cause_with_slice(
    vulnerable_code: str,
    cwe_id: str,
    initial_root_cause: str,
    key_variable_lines: List[int],
    model_config: dict, # 新增: 用于LLM调用的模型配置
    verbose: bool = False
) -> Optional[dict]:
    """
    基于代码切片精化根本原因描述
    
    Args:
        vulnerable_code: 包含漏洞的代码
        cwe_id: CWE漏洞类型ID
        initial_root_cause: 初始根本原因描述
        key_variable_lines: 关键变量行号列表
        model_config: 用于LLM调用的模型配置
        verbose: 是否输出详细信息
        
    Returns:
        Optional[dict]: 精化后的根本原因信息，包含refined_description, code_slice, key_variable_lines
    """
    # 首先基于关键变量行号生成代码切片
    code_slice = ""
    try:
        code_slice, _ = slice_code_from_string(
            source_code=vulnerable_code,
            target_lines=key_variable_lines,
            slice_type="combined",
            data_flow_only=False,
            verbose=False
        )
        if not code_slice.strip():
            if verbose:
                print("警告: 基于关键变量生成的代码切片为空。将使用原始代码作为回退。")
            code_slice = vulnerable_code
        else:
            if verbose:
                print(f"成功生成代码切片 (基于行号: {key_variable_lines})")
    except Exception as e:
        if verbose:
            print(f"错误: 生成代码切片失败 - {e}. 将使用原始代码作为回退。")
        code_slice = vulnerable_code

    # 使用代码切片精化根本原因描述
    code_slice_numbered = add_line_numbers_to_code(code_slice)
    # Note: key_variable_lines are relative to the original vulnerable_code,
    # so we use vulnerable_code to get the statements for these lines.
    formatted_key_variable_lines = format_lines_with_statements(vulnerable_code, key_variable_lines)
    
    prompt_refine_root_cause = f"""Based on the focused code slice extracted from the vulnerable code, refine and enhance the initial root cause analysis to provide a more precise and detailed description of the vulnerability's root cause.

CWE Type: {cwe_id}
Key Variable Lines:
{formatted_key_variable_lines}

Initial Root Cause Analysis:
"{initial_root_cause}"

Focused Code Slice (extracted based on key variables):
```
{code_slice_numbered}
```

Task:
1. Analyze the focused code slice in detail
2. Refine the initial root cause description based on the specific code patterns and data flows visible in the slice
3. Provide a more precise explanation of how the vulnerability manifests in this specific code context
4. Identify the exact source-to-sink flow that enables the vulnerability
5. Explain the specific conditions or program states that allow the vulnerability to be triggered

Output STRICTLY in the following JSON format:
```json
{{
  "source_sink_analysis": "Detailed explanation of the source-to-sink flow in the code slice",
  "triggering_conditions": "Specific conditions that must be met for the vulnerability to manifest",
  "code_patterns": "Key code patterns or constructs that contribute to the vulnerability",
  "refined_description": "Enhanced and more precise description of the root cause based on the code slice analysis, including specific technical details about the vulnerability mechanism",
  "confidence_level": "High|Medium|Low - confidence in this refined analysis"
}}
```

Focus on providing actionable insights that will help in finding similar vulnerability patterns and generating effective repairs.
"""

    try:
        api_call_params_refine = {
            "prompt": prompt_refine_root_cause,
            "model_config": model_config,
            "max_tokens": 4000, # Specific to this call
            "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0),
            "n": DEFAULT_OPENAI_PARAMS.get("n", 1)
        }
        response_text_refine_list = generate_with_OpenAI_model(**api_call_params_refine)
        
        if response_text_refine_list and response_text_refine_list[0]:
            response_text_refine = response_text_refine_list[0]
            # 尝试从LLM响应中提取JSON部分
            json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text_refine)
            if not json_match:
                json_match = re.search(r'({[\s\S]*})', response_text_refine)

            if json_match:
                json_str = json_match.group(1)
                try:
                    parsed_response = json.loads(json_str)
                    if "refined_description" in parsed_response:
                        refined_description = parsed_response["refined_description"]
                        
                        if verbose:
                            print(f"成功精化根本原因描述")
                            print(f"置信度: {parsed_response.get('confidence_level', 'N/A')}")
                        
                        return {
                            "refined_description": refined_description,
                            "code_slice": code_slice,
                            "key_variable_lines": key_variable_lines,
                            "source_sink_analysis": parsed_response.get("source_sink_analysis", ""),
                            "triggering_conditions": parsed_response.get("triggering_conditions", ""),
                            "code_patterns": parsed_response.get("code_patterns", ""),
                            "confidence_level": parsed_response.get("confidence_level", "Medium")
                        }
                    else:
                        print(f"警告: LLM未能按预期格式返回精化的根本原因 (缺少 'refined_description')。响应: {response_text_refine}")
                except json.JSONDecodeError as je:
                    print(f"警告: 解析LLM精化根本原因响应时JSON解码失败: {je}. 响应: {response_text_refine}")
            else:
                print(f"警告: 在LLM精化根本原因响应中未找到JSON块。响应: {response_text_refine}")
        else:
            print("警告: LLM未能生成精化的根本原因分析。")

    except Exception as e:
        print(f"错误: 精化根本原因时发生异常 - {e}")

    # 如果精化失败，返回基于原始描述的回退结果
    if verbose:
        print("精化根本原因失败，使用原始描述作为回退")
    
    return {
        "refined_description": initial_root_cause,
        "code_slice": code_slice,
        "key_variable_lines": key_variable_lines,
        "source_sink_analysis": "精化失败，使用原始分析",
        "triggering_conditions": "精化失败，使用原始分析",
        "code_patterns": "精化失败，使用原始分析",
        "confidence_level": "Low"
    }


# Helper function to select the best repair suggestion
def select_best_repair_suggestion(
    suggestions: List[dict],
    original_code: str,
    model_config: dict, # 新增: 用于LLM评估的模型配置
    top_n: int = 1,
    graph_consistent: bool = False,
    verbose: bool = False
):
    """
    Selects the best repair suggestion(s) from a list of candidates.
    Uses LLM evaluation (with specified model_config) and optionally graph consistency for ranking.
    Each suggestion is evaluated on whether it achieves the 'source_example_post_repair_state'
    when applied to the 'original_code'.
    If top_n = 1 and a single best suggestion is found, returns its code as a string.
    Otherwise, returns a list of suggestion codes (up to top_n).
    Returns None if no suitable suggestions are found.
    """
    if not suggestions:
        print("  No suggestions provided to select_best_repair_suggestion.")
        return None

    candidate_suggestions = []
    for i, s_item in enumerate(suggestions):
        patch = s_item.get("suggestion_patch")
        if not patch or not patch.strip():
            print(f"  原始建议 {i+1} 的patch为空，已忽略 (select_best_repair_suggestion)。")
            continue
        
        # 直接使用patch，不再尝试应用生成完整代码
        candidate_suggestions.append(s_item)
    
    if not candidate_suggestions:
        print("  所有修复建议均无效 (空patch)，无法进行LLM评估 (select_best_repair_suggestion)。")
        return None

    # print(f"  发现 {len(candidate_suggestions)} 个有效修复建议。将进行LLM评估 (select_best_repair_suggestion)...")

    # Helper function to evaluate a single suggestion
    def _evaluate_one_suggestion(s_item_param, original_code_param, idx_param):
        source_example_post_repair_state = s_item_param.get("source_example_post_repair_state")
        current_llm_score = 0.0

        if not source_example_post_repair_state or source_example_post_repair_state.lower() == "not available":
            print(f"      警告: 候选建议 {idx_param+1} 的 source_example_post_repair_state 无效或缺失。无法进行LLM评估，评分为 {current_llm_score} (select_best_repair_suggestion)。")
        else:
            suggestion_patch = s_item_param.get("suggestion_patch")
            evaluation_prompt = f"""
You are a code analysis expert. Your task is to evaluate if a given 'Suggested Repair Patch' effectively transforms the 'Original Vulnerable Code' to a state that aligns with the 'Desired Post-Repair State Description'.

Original Vulnerable Code:
```
{original_code_param}
```

Suggested Repair Patch (Unified Diff Format):
```diff
{suggestion_patch}
```

Desired Post-Repair State Description (this describes the expected outcome for a similar vulnerability after repair, learn from it):
"{source_example_post_repair_state}"

Evaluation Task:
1. Carefully analyze the 'Original Vulnerable Code' and the 'Suggested Repair Patch' to understand the changes being made.
2. Assess if the 'Suggested Repair Patch', when applied to the 'Original Vulnerable Code', would achieve an outcome or program state that is consistent with the 'Desired Post-Repair State Description'.
3. Focus on whether the core functional or security objectives implied by the 'Desired Post-Repair State Description' are met by the 'Suggested Repair Patch'.

Output your assessment as a single word: 'true' if the 'Suggested Repair Patch' successfully achieves the state described by 'Desired Post-Repair State Description', or 'false' otherwise.
Do not provide any other explanation, preamble, or markdown formatting. Just the single word 'true' or 'false'.
"""
            try:
                # Pass model_config to the LLM call for evaluation
                eval_api_call_params = {
                    "prompt": evaluation_prompt,
                    "model_config": model_config, # 使用传入的评估模型配置
                    "n": 1,
                    "max_tokens": 10, # Specific for this boolean-like response
                    "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0)
                }
                llm_responses = generate_with_OpenAI_model(**eval_api_call_params)
                if llm_responses and llm_responses[0]:
                    response_text = llm_responses[0].strip().lower()
                    if response_text == "true":
                        current_llm_score = 1.0
                    elif response_text == "false":
                        pass # current_llm_score remains 0.0
                    else:
                        print(f"      警告: LLM评估建议 {idx_param+1} 返回不明确: '{llm_responses[0]}'. 评分为 {current_llm_score} (select_best_repair_suggestion)。")
                else:
                    print(f"      警告: LLM评估建议 {idx_param+1} 未返回有效响应。评分为 {current_llm_score} (select_best_repair_suggestion)。")
            except Exception as e:
                print(f"      错误: LLM评估建议 {idx_param+1} 时发生异常: {e}. 评分为 {current_llm_score} (select_best_repair_suggestion)。")
        
        return {
            "suggestion_item": s_item_param,
            "llm_score": current_llm_score,
            "original_idx": idx_param # Keep original index for sorting if needed
        }

    evaluated_suggestions_results = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=32) as executor:
        futures = [
            executor.submit(_evaluate_one_suggestion, s_item, original_code, idx)
            for idx, s_item in enumerate(candidate_suggestions)
        ]
        for future in concurrent.futures.as_completed(futures):
            try:
                result = future.result()
                if result: # Ensure result is not None
                    evaluated_suggestions_results.append(result)
            except Exception as exc:
                # It's hard to get original_idx here without more complex tracking with future
                # _evaluate_one_suggestion should log its own errors with idx
                print(f"      错误: 处理一个已完成的建议评估任务时发生意外: {exc}")
    
    # Sort by original index to maintain order, as as_completed does not guarantee order
    evaluated_suggestions_results.sort(key=lambda x: x["original_idx"])
    evaluated_suggestions = [{"suggestion_item": r["suggestion_item"], "llm_score": r["llm_score"]} for r in evaluated_suggestions_results]

    if not evaluated_suggestions:
        print("  没有建议被LLM评估 (select_best_repair_suggestion)。")
        return None

    # Filter for suggestions evaluated as "true" by LLM
    true_suggestions = [s for s in evaluated_suggestions if s["llm_score"] > 0]

    if not true_suggestions:
        print("  没有修复建议通过LLM评估为 'true'。未能选择最佳修复 (select_best_repair_suggestion)。")
        return None

    if graph_consistent and len(true_suggestions) > 1:
        # 注意：由于移除了patch应用步骤，图一致性分析功能暂时简化
        # 可以考虑基于patch内容进行分析，或者完全移除图一致性功能
        if verbose:
            print("  警告：由于移除了patch应用步骤，图一致性分析暂时不可用。")
        
        # 为兼容性设置默认组ID
        for s_eval_item in true_suggestions:
            s_eval_item['graph_consistency_group_id'] = 0

    # New sort key function (simplified without graph consistency)
    def sort_key_func(x_eval_item):
        llm_score = x_eval_item.get("llm_score", 0.0)
        distance = float(x_eval_item["suggestion_item"].get("source_example_distance", float('inf')))
        return (-llm_score, distance)

    true_suggestions.sort(key=sort_key_func)
    
    if verbose:
        for idx_sorted, ts_item_sorted in enumerate(true_suggestions):
            dist_sorted = float(ts_item_sorted['suggestion_item'].get('source_example_distance', -1.0))
            print(f"    Sorted {idx_sorted+1}: LLM {ts_item_sorted.get('llm_score',0.0):.2f}, Dist {dist_sorted:.4f}")

    num_to_return = min(top_n, len(true_suggestions))
    selected_suggestions_details = true_suggestions[:num_to_return]

    final_suggestion_details = [] # Stores suggestion items with llm_score
    
    if not selected_suggestions_details:
        print("  没有符合条件的修复建议可供选择 (select_best_repair_suggestion)。")
        return None

    for i, s_detail in enumerate(selected_suggestions_details): # s_detail is an s_eval_item
        suggestion_item = s_detail["suggestion_item"]
        llm_score = s_detail["llm_score"]
        
        display_distance = -1.0
        try:
            val = suggestion_item.get('source_example_distance', -1.0)
            if val is not None:
                display_distance = float(val)
        except (ValueError, TypeError):
            pass

        # print(f"  --- 建议 {i+1}/{num_to_return} ---")
        # print(f"    LLM评估得分: {llm_score:.2f}, 示例距离: {display_distance:.4f}")
        # print(f"    源自根本原因: '{suggestion_item.get('source_root_cause_desc', 'N/A')[:50]}...'")
        # print(f"    修复策略: '{suggestion_item.get('repair_strategy', 'N/A')[:100]}...'")
        key_vars_preview = suggestion_item.get('key_variables', [])
        
        # Construct the detailed item to return
        merged_suggestion_detail = suggestion_item.copy()
        merged_suggestion_detail['llm_score'] = llm_score # Already in s_detail, but ensure it's in the final item
        merged_suggestion_detail['graph_consistency_group_id'] = s_detail.get('graph_consistency_group_id')
        final_suggestion_details.append(merged_suggestion_detail)

    if top_n == 1 and len(final_suggestion_details) == 1:
        print(f"  最终选择的单个修复建议对象已确定 (select_best_repair_suggestion)。")
        return final_suggestion_details[0] # Return the single suggestion object
    elif not final_suggestion_details:
        # This case should also be covered by earlier checks
        print("  没有最终选择的修复建议对象 (select_best_repair_suggestion)。")
        return None
    else:
        print(f"  最终选择的 {len(final_suggestion_details)} 个修复建议对象已确定 (select_best_repair_suggestion)。")
        return final_suggestion_details # Return list of suggestion objects


def _generate_single_repair_suggestion(
    vulnerable_code: str,
    cwe_id: str,
    vulnerable_line_numbers: List[int],
    code_slice: str,
    current_root_cause_desc: str,
    example: dict,
    model_config: dict, # 更改: 接收模型配置
    rc_idx: int, # For logging/tracking
    ex_idx: int, # For logging/tracking
    total_root_causes_for_display: int, # For logging/tracking
    num_examples_in_rc: int # For logging/tracking
) -> Optional[dict]:
    """
    Generates a single repair suggestion for a given root cause, example, and model_config.
    This function is designed to be called in parallel.
    """
    
    formatted_vulnerable_lines_approx = format_lines_with_statements(vulnerable_code, vulnerable_line_numbers)
    prompt_intro_individual = f"""
You are an expert code repair assistant. Your task is to repair the given vulnerable code based on an analysis of its specific root cause and by learning from the provided example of a similar vulnerability repair.

Original Vulnerable Code (CWE: {cwe_id}):
```
{add_line_numbers_to_code(vulnerable_code)}
```
Vulnerable Lines approx:
{formatted_vulnerable_lines_approx}

Focused Code Slice (key vulnerability-related context):
```
{code_slice if code_slice.strip() and code_slice != vulnerable_code else "No specific focused slice available or slice is same as full code; consider the full vulnerable code."}
```
---
"""
    prompt_current_root_cause_section = f"""
Identified Root Cause for this specific repair attempt:
- {current_root_cause_desc}
---
"""
    example_code_before = example.get('code_before', 'Not available')
    example_code_after = example.get('code_after_ground_truth', 'Not available')
    example_abstract_strategy = example.get('abstract_strategy', 'Not available')
    example_concrete_strategy = example.get('concrete_strategy', 'Not available')
    example_cwe_specific_strategy = example.get('cwe_specific_strategy', 'Not available')
    example_pre_repair_state = example.get('pre_repair_state_example', 'Not available')
    example_post_repair_state = example.get('post_repair_state_example', 'Not available')

    example_diff = '\n'.join(difflib.unified_diff(
                            example_code_before.splitlines(keepends=True),
                            example_code_after.splitlines(keepends=True),
                            fromfile='example_vulnerable.c',
                            tofile='example_fixed.c',
                            lineterm=''
                        ))
    prompt_current_example_section = f"""
Repair Example for Consideration:
  Pre-Repair State(root cause) described in Example: {example_pre_repair_state}
  Vulnerable Context (from example's code_before):
  ```
{add_line_numbers_to_code(example_code_before) if example_code_before != 'Not available' else example_code_before}
  ```
  Repair Patch (Unified Diff Format showing the exact changes made):
  ```
{example_diff}
  ```
"""
    if example_abstract_strategy != 'Not available' and example_abstract_strategy: # Check for non-empty
        prompt_current_example_section += f"  Abstract Repair Strategy (from example):\n  {example_abstract_strategy}\n"
    if example_concrete_strategy != 'Not available' and example_concrete_strategy: # Check for non-empty
        prompt_current_example_section += f"  Concrete Repair Strategy (from example):\n  {example_concrete_strategy}\n"
    if example_cwe_specific_strategy != 'Not available' and example_cwe_specific_strategy: # Check for non-empty
        prompt_current_example_section += f"  CWE-Specific Repair Strategy (from example):\n  {example_cwe_specific_strategy}\n"
    prompt_current_example_section += "---\n"

    prompt_instruction_individual = f"""
### Task:
Based on the 'Identified Root Cause for this specific repair attempt' and by learning from the provided 'Repair Example' (its strategies and code edits), generate a unified diff patch to repair the vulnerability in the 'Original Vulnerable Code' shown at the beginning.
The repair should aim to address the identified root cause as effectively as possible, drawing inspiration from the example if it helps.

Output Format:
Please provide your response in the following structured format (NOT JSON):

REPAIR_STRATEGY_START:
[A concise description of the repair approach taken]
REPAIR_STRATEGY_END:

KEY_VARIABLES_START:
[List key variables, functions, or crucial program elements, separated by commas]
KEY_VARIABLES_END:

VULNERABILITY_ANALYSIS_START:
[A brief explanation of what vulnerability was identified and how it's fixed]
VULNERABILITY_ANALYSIS_END:

PATCH_DIFF_START:
[Your unified diff patch here - this should be in standard unified diff format showing exactly what lines to change]
PATCH_DIFF_END:

**Important:**
- Generate a valid unified diff that can be applied with standard patch tools
- Focus on the minimal changes needed to fix the security issue based on the root cause
- Ensure the patch maintains original functionality while addressing the vulnerability
- Do not include any other explanations or formatting around the structured response
- The patch should be immediately applicable to the provided code
- Address the specific root cause: {current_root_cause_desc}...
"""
    individual_repair_prompt = (
        prompt_intro_individual +
        prompt_current_root_cause_section +
        prompt_current_example_section +
        prompt_instruction_individual
    )
    
    try:
        # openai_params was DEFAULT_OPENAI_PARAMS, now use model_config
        api_call_params_repair = {
            "prompt": individual_repair_prompt,
            "model_config": model_config,
            "max_tokens": 16000,
            "temperature": model_config.get("temperature", DEFAULT_OPENAI_PARAMS.get("temperature", 0)), # Prefer model_config's temp
            "n": model_config.get("n", DEFAULT_OPENAI_PARAMS.get("n", 1)) # Prefer model_config's n
        }
        response_text_repair_list = generate_with_OpenAI_model(**api_call_params_repair)
        
        
        if response_text_repair_list and response_text_repair_list[0]:
            generated_response_text = response_text_repair_list[0].strip()
            
            try:
                # 使用正则表达式提取结构化信息，而不是解析JSON
                patch_diff = None
                repair_strategy = None
                key_variables = []

                # 提取patch_diff
                patch_match = re.search(r'PATCH_DIFF_START:\s*(.*?)\s*PATCH_DIFF_END:', generated_response_text, re.DOTALL)
                if patch_match:
                    patch_diff = patch_match.group(1).strip()

                # 提取repair_strategy
                strategy_match = re.search(r'REPAIR_STRATEGY_START:\s*(.*?)\s*REPAIR_STRATEGY_END:', generated_response_text, re.DOTALL)
                if strategy_match:
                    repair_strategy = strategy_match.group(1).strip()

                # 提取key_variables
                variables_match = re.search(r'KEY_VARIABLES_START:\s*(.*?)\s*KEY_VARIABLES_END:', generated_response_text, re.DOTALL)
                if variables_match:
                    variables_text = variables_match.group(1).strip()
                    if variables_text:
                        # 按逗号分割并清理空白
                        key_variables = [var.strip() for var in variables_text.split(',') if var.strip()]

                # 验证提取的信息
                if not patch_diff or not patch_diff.strip():
                    print(f"      LLM response 'patch_diff' is missing or empty for RC {rc_idx+1}, Ex {ex_idx+1}. Response: '{generated_response_text[:200]}...'")
                    return None
                if not repair_strategy or not repair_strategy.strip():
                    print(f"      LLM response 'repair_strategy' is missing or empty for RC {rc_idx+1}, Ex {ex_idx+1}. Response: '{generated_response_text[:200]}...'")
                    return None
                # key_variables可以为空列表，所以不需要验证

                current_example_distance = -1.0
                try:
                    val = example.get("distance", -1.0)
                    if val is not None:
                        current_example_distance = float(val)
                except (ValueError, TypeError):
                    pass # Keep -1.0 if conversion fails

                return {
                    "suggestion_patch": patch_diff,
                    "repair_strategy": repair_strategy,
                    "key_variables": key_variables,
                    "source_root_cause_desc": current_root_cause_desc,
                    "source_example_pre_repair_state": example_pre_repair_state,
                    "source_example_post_repair_state": example_post_repair_state,
                    "source_example_code_before": example_code_before,
                    "source_example_code_after": example_code_after,
                    "source_example_distance": current_example_distance
                }

            except Exception as e:
                print(f"      Unexpected error processing LLM response for RC {rc_idx+1}, Ex {ex_idx+1}: {e}. Response: '{generated_response_text[:200]}...'")
                return None

        else:
            print(f"      LLM call did not return a valid response for repair (RC {rc_idx+1}, Ex {ex_idx+1}).")
            return None
    except Exception as e:
        print(f"      错误: 调用LLM为根本原因 {rc_idx+1}、示例 {ex_idx+1} 生成修复代码时发生异常 - {e}")
        return None

def generate_vulnerability_repair(
    vulnerable_code: str,
    cwe_id: str,
    vulnerable_line_numbers: List[int],
    generation_model_config: dict, # 新增
    evaluation_model_config: dict, # 新增
    num_root_causes_to_analyze: int = 3,
    num_examples_per_cause: int = 2,
    top_n: int = 3,
    graph_consistent: bool = False,
    verbose_graph: bool = False,
    enable_root_cause_filtering: bool = True,
    min_consistency_level: str = "Medium",
    min_confidence_score: float = 0.6,
    enable_direct_llm_fallback: bool = True,
    enable_patch_grouping: bool = False
):
    """
    根据给定的代码、CWE ID和漏洞行号列表，生成修复后的代码。
    
    步骤:
    1. 使用LLM识别根本原因，同时输出关键变量和对应行号
    2. 对于每个根本原因，根据其关键变量行号进行代码切片
    3. 将切片返回给LLM，生成更加精确的漏洞根因描述
    4. 基于精确的根本原因，获取其CWE父类型，并从对应的特定CWE的Faiss数据库中查询M个最相似的修复示例
    5. (可选) 使用LLM评估示例的root cause与当前root cause的一致性，过滤不一致的示例
    6. 进行树形的prompt来使用LLM，生成修复后的代码
    7. (可选) 应用patch分组增强算法，生成更优质的修复建议
    8. (可选) 如果没有可用示例或基于示例的修复失败，可选择直接使用LLM进行修复
    
    Args:
        generation_model_config: 用于生成任务的LLM配置
        evaluation_model_config: 用于评估任务的LLM配置
        enable_root_cause_filtering: 是否启用root cause一致性过滤
        min_consistency_level: 最小一致性级别要求 ("High", "Medium", "Low")
        min_confidence_score: 最小置信度分数要求 (0.0-1.0)
        enable_direct_llm_fallback: 是否启用直接LLM修复作为后备方案 (默认: True)
        enable_patch_grouping: 是否启用patch分组增强算法 (默认: False)
    """

    # 步骤 1: 识别初始根本原因和关键变量行号
    initial_root_causes_with_variables = identify_root_causes_and_key_variables(
        vulnerable_code=vulnerable_code,
        cwe_id=cwe_id,
        vulnerable_line_numbers=vulnerable_line_numbers,
        model_config=generation_model_config, # 使用生成模型配置
        num_root_causes_to_analyze=num_root_causes_to_analyze,
        verbose=verbose_graph
    )

    if not initial_root_causes_with_variables:
        print("未能分析出根本原因，无法继续修复。")
        return None

    # 步骤 2-3: 对每个根本原因进行切片并生成精确的根因描述
    refined_root_causes = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=32) as executor:
        future_to_rc_data_desc = {}
        for rc_data in initial_root_causes_with_variables:
            future = executor.submit(refine_root_cause_with_slice,
                                            vulnerable_code=vulnerable_code,
                                            cwe_id=cwe_id,
                                            initial_root_cause=rc_data["description"],
                                            key_variable_lines=rc_data["key_variable_lines"],
                                            model_config=generation_model_config, # 使用生成模型配置
                                            verbose=verbose_graph)
            future_to_rc_data_desc[future] = rc_data.get("description", "UnknownRootCause")

        for future in concurrent.futures.as_completed(future_to_rc_data_desc):
                rc_description_for_error = future_to_rc_data_desc[future]
                try:
                    refined_rc = future.result()
                    if refined_rc:
                        refined_root_causes.append(refined_rc)
                except Exception as exc:
                    print(f"      Error refining root cause '{rc_description_for_error[:50]}...': {exc}")
                    # Optionally, log this error more formally or handle it

    if not refined_root_causes:
        print("未能生成精确的根本原因描述，无法继续修复。")
        return None

    # 步骤 4: 获取CWE父类型并查询Faiss获取相似修复示例
    root_causes_with_examples = [] 

    for i, refined_rc_data in enumerate(refined_root_causes):
        current_cause_data = {
            "root_cause": refined_rc_data["refined_description"], 
            "code_slice": refined_rc_data["code_slice"],
            "key_variable_lines": refined_rc_data["key_variable_lines"],
            "examples": []
        }

        cwe_processor, vector_querier = get_thread_local_instances()

        try:
            _, processed_top_parent = cwe_processor.process_cwe(cwe_id)
            if processed_top_parent not in MAIN_CWE_TYPES_FOR_INDEXING:
                query_cwe_type = "CWE-0" 
            else:
                query_cwe_type = processed_top_parent
        except Exception as e:
            print(f"    错误: 获取CWE父类型失败 for {cwe_id} - {e}. 将尝试使用原始CWE ID。")
            query_cwe_type = cwe_id 

        try:
            similar_examples = vector_querier.search(
                query_text=refined_rc_data["refined_description"],
                top_k=num_examples_per_cause,
                cwe_type=query_cwe_type
            )
            if similar_examples:
                # 构建初始示例数据列表
                initial_examples = []
                for ex_idx, example in enumerate(similar_examples):
                    metadata = example.get("metadata", {})
                    example_data = {
                        "code_before": metadata.get("code_before"),
                        "code_after_ground_truth": metadata.get("code_after_ground_truth"),
                        "abstract_strategy": metadata.get("final_llm1_abstract_strategy"),
                        "concrete_strategy": metadata.get("final_llm1_concrete_strategy"),
                        "cwe_specific_strategy": metadata.get("final_llm1_cwe_specific_strategy"),
                        "pre_repair_state_example": metadata.get("final_llm1_pre_repair_state", "Not available"),
                        "post_repair_state_example": metadata.get("final_llm1_post_repair_state", "Not available"),
                        "distance": example.get("distance")
                    }
                    if example_data["code_before"] and example_data["code_after_ground_truth"]: # 确保核心数据存在
                        initial_examples.append(example_data)
                        # print(f"      初始示例 {ex_idx+1} (距离: {example_data.get('distance', -1):.4f}) 添加成功。")
                    else:
                        print(f"      警告: 示例 {ex_idx+1} 元数据不完整 (缺少code_before或code_after_ground_truth)，已跳过。")
                
                # 步骤 5: Root Cause一致性过滤
                if enable_root_cause_filtering and initial_examples:
                    # 转换字符串枚举类型
                    consistency_level_enum = RootCauseConsistency.MEDIUM  # 默认值
                    try:
                        if min_consistency_level.lower() == "high":
                            consistency_level_enum = RootCauseConsistency.HIGH
                        elif min_consistency_level.lower() == "medium":
                            consistency_level_enum = RootCauseConsistency.MEDIUM
                        elif min_consistency_level.lower() == "low":
                            consistency_level_enum = RootCauseConsistency.LOW
                        else:
                            print(f"    警告: 未知的一致性级别 '{min_consistency_level}'，使用默认值 MEDIUM")
                    except Exception as enum_e:
                        print(f"    警告: 转换一致性级别时发生错误: {enum_e}，使用默认值 MEDIUM")
                    
                    # 进行一致性过滤
                    filtered_examples = filter_examples_by_root_cause_consistency(
                        current_root_cause=refined_rc_data["refined_description"],
                        examples=initial_examples,
                        cwe_id=cwe_id,
                        model_config=evaluation_model_config, # 使用评估模型配置进行一致性检查
                        min_consistency_level=consistency_level_enum,
                        min_confidence_score=min_confidence_score,
                        verbose=verbose_graph
                    )
                    
                    if filtered_examples:
                        current_cause_data["examples"] = filtered_examples
                        if verbose_graph:
                            print(f"    Root Cause一致性过滤: 保留 {len(filtered_examples)}/{len(initial_examples)} 个示例")
                    else:
                        # 过滤后无示例保留，不使用原始示例，让后续的直接LLM修复处理
                        print(f"    Root Cause一致性过滤: 所有示例均不符合要求 (级别: {min_consistency_level}, 置信度: {min_confidence_score})，此根本原因将跳过基于示例的修复")
                        current_cause_data["examples"] = []  # 明确设置为空列表
                else:
                    # 不启用过滤或无初始示例，直接使用原始示例
                    current_cause_data["examples"] = initial_examples
                    if enable_root_cause_filtering:
                        print(f"    Root Cause一致性过滤: 跳过 (无有效初始示例)")
                    else:
                        print(f"    Root Cause一致性过滤: 禁用，直接使用 {len(initial_examples)} 个示例")
            else:
                print(f"    警告: 未能为根本原因 '{refined_rc_data['refined_description'][:50]}...' 找到相似示例 (CWE类型: {query_cwe_type})。")
        except Exception as e:
            print(f"    错误: 查询Faiss时发生异常 - {e}")
        
        root_causes_with_examples.append(current_cause_data)

    # 检查是否有任何可用示例
    has_any_examples = any(rc_data["examples"] for rc_data in root_causes_with_examples)
    
    # 统计过滤情况
    if enable_root_cause_filtering:
        total_root_causes = len(root_causes_with_examples)
        root_causes_with_examples_count = sum(1 for rc_data in root_causes_with_examples if rc_data["examples"])
        root_causes_without_examples_count = total_root_causes - root_causes_with_examples_count
        
        if verbose_graph:
            print(f"Root Cause一致性过滤统计:")
            print(f"  总根本原因数: {total_root_causes}")
            print(f"  有可用示例的根本原因: {root_causes_with_examples_count}")
            print(f"  无可用示例的根本原因: {root_causes_without_examples_count}")
        
        if root_causes_without_examples_count > 0:
            print(f"注意: {root_causes_without_examples_count}/{total_root_causes} 个根本原因在一致性过滤后无可用示例")
    
    # 步骤 6: 构建树形Prompt并调用LLM生成修复代码
    all_repair_suggestions = []

    if not refined_root_causes: # Early exit if no root causes were identified
        print("警告: 未分析出任何根本原因，无法继续生成修复建议。")
    
    # Check if there are any examples at all before starting the loop
    has_any_examples = any(rc_data.get("examples") for rc_data in root_causes_with_examples)
    if not has_any_examples and refined_root_causes: # Only print if root causes exist but no examples
        if enable_root_cause_filtering:
            print("警告: 所有根本原因在一致性过滤后均无可用示例。将依赖直接LLM修复方法。")
        else:
            print("警告: 存在根本原因，但没有任何根本原因有相关的修复示例。修复流程可能无法基于示例进行。")

    # Calculate total_root_causes_display once before parallel processing
    if refined_root_causes:
        total_root_causes_for_display = len(refined_root_causes)
    elif root_causes_with_examples: # Check if this list itself is not None
        total_root_causes_for_display = len(root_causes_with_examples)
    else:
        total_root_causes_for_display = 0
        print("警告: 'refined_root_causes' 和 'root_causes_with_examples' 均为空或未定义，无法确定总根本原因数。")

    futures = []

    # 确定进程数量
    cpu_cores = multiprocessing.cpu_count()
    max_workers = 64
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        for rc_idx, rc_data in enumerate(root_causes_with_examples):
            current_root_cause_desc = rc_data.get("root_cause")
            current_code_slice = rc_data.get("code_slice")
            if not current_root_cause_desc:
                print(f"  警告: 根本原因条目 {rc_idx+1} 描述缺失，跳过提交修复任务。")
                continue

            examples_for_rc = rc_data.get("examples")
            if not examples_for_rc:
                print(f"  根本原因 '{current_root_cause_desc[:50]}...' 没有示例，跳过为此根本原因提交修复任务。")
                continue
            
            for ex_idx, example_item in enumerate(examples_for_rc):
                future = executor.submit(
                    _generate_single_repair_suggestion,
                    vulnerable_code,
                    cwe_id,
                    vulnerable_line_numbers,
                    current_code_slice,  # 使用该根本原因对应的代码切片
                    current_root_cause_desc,
                    example_item, # Pass the individual example item
                    generation_model_config, # 使用生成模型配置
                    rc_idx,
                    ex_idx,
                    total_root_causes_for_display,
                    len(examples_for_rc) # num_examples_in_rc for this specific root cause
                )
                futures.append(future)
 
    for i, future in enumerate(concurrent.futures.as_completed(futures)):
        try:
            # The _generate_single_repair_suggestion function returns a dict or None
            result = future.result()
            if result:
                all_repair_suggestions.append(result)
            # If result is None, an error was handled and logged within _generate_single_repair_suggestion
            # print(f"  已处理完成的任务 {i+1}/{len(futures)}。当前收集到 {len(all_repair_suggestions)} 个有效建议。")
        except Exception as e:
            # This catches exceptions from the execution of the task in the thread,
            # or if future.result() itself has an issue (e.g., task cancelled).
            # _generate_single_repair_suggestion is expected to catch its own LLM/API errors.
            # It's hard to provide specific (rc_idx, ex_idx) here without storing them with the future or more complex tracking.
            print(f"  错误: 处理一个已完成的修复建议任务 {i+1}/{len(futures)} 时发生意外: {e}")

    # 新增：为每个根本原因直接生成LLM修复建议 (如果启用)
    direct_llm_suggestions = []
    if enable_direct_llm_fallback and refined_root_causes:
        if verbose_graph:
            print(f"  为 {len(refined_root_causes)} 个精炼根本原因尝试直接LLM修复...")
        if isinstance(refined_root_causes, list):
            for rc_idx_direct, rc_data in enumerate(refined_root_causes):
                if not isinstance(rc_data, dict):
                    if verbose_graph:
                        print(f"    警告: refined_root_causes 中的元素 (索引 {rc_idx_direct}) 不是字典，跳过。元素: {rc_data}")
                    continue

                current_refined_description = rc_data.get("refined_description")
                current_code_slice_for_rc = rc_data.get("code_slice")
                # 如果 code_slice 为空或无效，则回退到完整的 vulnerable_code
                if not current_code_slice_for_rc or not current_code_slice_for_rc.strip():
                    current_code_slice_for_rc = vulnerable_code
                    if verbose_graph:
                        print(f"    警告: 根本原因 '{str(current_refined_description)[:50]}...' 的 code_slice 为空，使用完整代码。")
                
                # 使用与此根本原因相关的 key_variable_lines，如果不存在则回退到原始的 vulnerable_line_numbers
                vulnerable_line_numbers_for_rc = rc_data.get("key_variable_lines", vulnerable_line_numbers)

                if not current_refined_description:
                    if verbose_graph:
                        print(f"    警告: 精炼根本原因描述缺失 (索引 {rc_idx_direct})，跳过直接LLM修复。")
                    continue

                if verbose_graph:
                    print(f"    为根本原因 '{str(current_refined_description)[:50]}...' (索引 {rc_idx_direct}) 生成直接LLM修复...")
                
                individual_direct_suggestions = generate_simple_vulnerability_repair_from_root_causes(
                    vulnerable_code=vulnerable_code,
                    cwe_id=cwe_id,
                    vulnerable_line_numbers=vulnerable_line_numbers_for_rc,
                    code_slice=current_code_slice_for_rc,
                    root_causes=[current_refined_description],
                    model_config=generation_model_config, # 使用生成模型配置
                    top_n=1,
                    verbose=verbose_graph
                )
                if individual_direct_suggestions:
                    direct_llm_suggestions.extend(individual_direct_suggestions)
                    if verbose_graph:
                        print(f"      成功为根本原因 '{str(current_refined_description)[:50]}...' 生成 {len(individual_direct_suggestions)} 个直接LLM建议。")
                elif verbose_graph:
                    print(f"      未能为根本原因 '{str(current_refined_description)[:50]}...' 生成直接LLM建议。")
        else:
            if verbose_graph:
                print(f"    警告: refined_root_causes 不是列表 (类型: {type(refined_root_causes)})，无法进行直接LLM修复。")
        
        if direct_llm_suggestions:
            if verbose_graph:
                print(f"  总共生成 {len(direct_llm_suggestions)} 个直接LLM修复建议。将与基于示例的建议合并。")
            all_repair_suggestions.extend(direct_llm_suggestions)
        elif verbose_graph and refined_root_causes and isinstance(refined_root_causes, list) and len(refined_root_causes) > 0 : # 仅在尝试过但失败时打印
            print(f"  未能生成任何直接LLM修复建议 (当 enable_direct_llm_fallback=True 且存在精炼根本原因时)。")

    # 步骤 7: 应用patch分组增强算法 (新增)
    if enable_patch_grouping and all_repair_suggestions:
        if verbose_graph:
            print(f"应用patch分组增强算法到 {len(all_repair_suggestions)} 个修复建议...")
        
        enhanced_suggestions = apply_patch_grouping_enhancement(
            repair_suggestions=all_repair_suggestions,
            vulnerable_code=vulnerable_code,
            cwe_id=cwe_id,
            vulnerable_line_numbers=vulnerable_line_numbers,
            model_config=generation_model_config, # 使用生成模型配置
            enable_patch_grouping=True,
            config=None,  # 使用默认配置
            verbose=verbose_graph
        )
        
        if enhanced_suggestions != all_repair_suggestions:
            if verbose_graph:
                print(f"patch分组增强完成: {len(enhanced_suggestions)} 个建议 (包含增强patch)")
            all_repair_suggestions = enhanced_suggestions
        else:
            if verbose_graph:
                print("patch分组增强未产生新的建议")

    final_repaired_suggestions = None
    if all_repair_suggestions:
        final_repaired_suggestions = select_best_repair_suggestion(
            all_repair_suggestions,
            vulnerable_code,
            model_config=evaluation_model_config, # 使用评估模型配置
            top_n=top_n,
            graph_consistent=graph_consistent, # Pass new param
            verbose=verbose_graph # Pass verbose for graph operations
        )
        
        if final_repaired_suggestions is None:
            print("  未能从LLM建议中选择出最终的修复对象。")
            # 旧的后备逻辑已移除。
            # 如果 enable_direct_llm_fallback 为 True，则直接LLM修复已在之前为每个根本原因尝试过。
            # 如果为 False，则不应有后备。
            print("  未能从合并后的建议中选择出最终的修复对象。")
            return [] # 函数应返回 List[dict]
        
        # final_repaired_suggestions is now a single dict (if top_n=1 and successful)
        # or a list of dicts (if top_n > 1 or multiple suggestions returned for top_n=1)
        # This list contains full suggestion details including llm_score, graph_id, etc.
        suggestions_to_process = []

        if isinstance(final_repaired_suggestions, dict): # Single suggestion object
            suggestions_to_process = [final_repaired_suggestions]
        elif isinstance(final_repaired_suggestions, list):
            suggestions_to_process = final_repaired_suggestions
        else: # Should not happen if select_best_repair_suggestion returns None for failure
            print(f"  未知的返回类型 ({type(final_repaired_suggestions)}) 从 select_best_repair_suggestion。")
            return None # Indicates an issue with select_best_repair_suggestion's output

        # At this point, suggestions_to_process is a list of detailed suggestion dicts
        # No need to extract just codes anymore.
        
        # Validate that we have actual suggestions with patch
        valid_suggestions_found = False
        for i, sugg_obj in enumerate(suggestions_to_process):
            if sugg_obj and isinstance(sugg_obj, dict) and sugg_obj.get("suggestion_patch"):
                valid_suggestions_found = True
                # Print details for verbosity, similar to before
                strategy = sugg_obj.get("repair_strategy", "N/A")
                key_vars = sugg_obj.get("key_variables", [])
                llm_s = sugg_obj.get("llm_score", "N/A")
                gid = sugg_obj.get("graph_consistency_group_id", "N/A")
                repair_method = sugg_obj.get("repair_method", "example_based")
                
                if verbose_graph:
                    print(f"\n  --- 最终选定的建议 {i+1} 详情 ---")
                    print(f"    修复方法: {repair_method}")
                    print(f"    修复策略: {strategy}")
                    print(f"    关键变量: {key_vars}")
                    print(f"    LLM得分: {llm_s}, 图组ID: {gid}")
                    if repair_method == "enhanced_group":
                        print(f"    增强组ID: {sugg_obj.get('group_id', 'N/A')}")
                        print(f"    原始patch数量: {sugg_obj.get('original_patches_count', 'N/A')}")
            else:
                print(f"    警告: 建议对象 {i+1} 结构不完整或缺少 'suggestion_patch'。")
        
        if not valid_suggestions_found:
            print("  尽管 select_best_repair_suggestion 返回了对象，但未能确认任何包含修复补丁的有效建议。")
            return [] # Return empty list as per plan for "no valid suggestions extracted"
            
        return suggestions_to_process # Return the list of detailed suggestion dicts
    else:
        print("  没有生成任何修复建议，无法选择。")
        # 旧的后备逻辑已移除。
        # 如果 all_repair_suggestions 为空到此，意味着基于示例的修复和
        # （如果启用的）基于每个根本原因的直接LLM修复均未产生建议。
        print("  在所有阶段（基于示例和直接LLM）之后，没有生成任何修复建议。")
        return []

def generate_simple_vulnerability_repair_from_root_causes(
    vulnerable_code: str,
    cwe_id: str,
    vulnerable_line_numbers: List[int],
    code_slice: str,
    root_causes: List[str],
    model_config: dict, # 新增: 用于LLM调用的模型配置
    top_n: int = 1,
    verbose: bool = False
) -> List[dict]:
    """
    当没有可用示例时，基于根本原因分析直接使用LLM生成修复补丁
    仿照simple_inference.py的提示逻辑
    
    Args:
        vulnerable_code: 包含漏洞的原始代码
        cwe_id: CWE漏洞类型ID
        vulnerable_line_numbers: 漏洞所在行号列表
        code_slice: 相关代码切片
        root_causes: 分析出的根本原因列表
        model_config: 用于LLM调用的模型配置
        top_n: 要生成的修复建议数量
        verbose: 是否输出详细信息
        
    Returns:
        修复建议列表，每个建议包含patch和相关信息
    """
    # Helper function to process a single root cause
    def _generate_for_one_root_cause(rc_idx_param: int, root_cause_desc_param: str) -> Optional[dict]:
        formatted_vulnerable_lines = format_lines_with_statements(vulnerable_code, vulnerable_line_numbers)
        # 构建修复prompt，仿照simple_inference.py的逻辑
        repair_prompt = f"""
You are an expert security code analyst and vulnerability repair specialist. Your task is to analyze the given vulnerable code and generate a precise patch to fix the identified security vulnerability.

**Vulnerable Code (CWE: {cwe_id}):**
```c
{add_line_numbers_to_code(vulnerable_code)}
```

**Vulnerable Lines:**
{formatted_vulnerable_lines}

**Focused Code Slice (key vulnerability-related context):**
```c
{code_slice if code_slice.strip() and code_slice != vulnerable_code else "No specific focused slice available or slice is same as full code; consider the full vulnerable code."}
```

**Identified Root Cause:**
{root_cause_desc_param}

**Task:**
1. Carefully analyze the provided vulnerable code, paying special attention to the lines marked as vulnerable.
2. Consider the focused code slice which highlights the key vulnerability-related context.
3. Consider the identified root cause to understand the specific security vulnerability.
4. Design a minimal but effective fix that:
   - Addresses the specific root cause identified
   - Eliminates the security vulnerability
   - Preserves the original code functionality
   - Follows secure coding best practices
   - Makes the smallest necessary changes

**Output Requirements:**
Please provide your response in the following structured format (NOT JSON):

REPAIR_STRATEGY_START:
[A concise description of the repair approach taken]
REPAIR_STRATEGY_END:

KEY_VARIABLES_START:
[List key variables, functions, or crucial program elements, separated by commas]
KEY_VARIABLES_END:

VULNERABILITY_ANALYSIS_START:
[A brief explanation of what vulnerability was identified and how it's fixed]
VULNERABILITY_ANALYSIS_END:

PATCH_DIFF_START:
[Your unified diff patch here - this should be in standard unified diff format showing exactly what lines to change]
PATCH_DIFF_END:

**Important:**
- Generate a valid unified diff that can be applied with standard patch tools
- Focus on the minimal changes needed to fix the security issue based on the root cause
- Ensure the patch maintains original functionality while addressing the vulnerability
- Do not include any other explanations or formatting around the structured response
- The patch should be immediately applicable to the provided code
- Address the specific root cause above
"""
        try:
            if verbose:
                print(f"    调用LLM生成基于根本原因 {rc_idx_param+1} 的修复补丁...")
            
            api_call_params = {
                "prompt": repair_prompt,
                "model_config": model_config,
                "max_tokens": 16000,
                "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0),
                "n": DEFAULT_OPENAI_PARAMS.get("n", 1)
            }
            responses = generate_with_OpenAI_model(**api_call_params)
            
            if not responses or not responses[0]:
                if verbose:
                    print(f"    错误: LLM未返回有效响应 (根本原因 {rc_idx_param+1})")
                return None
            response_text = responses[0].strip()
            
            patch_diff = None
            repair_strategy = None
            key_variables = []
            vulnerability_analysis = None

            patch_match = re.search(r'PATCH_DIFF_START:\s*(.*?)\s*PATCH_DIFF_END:', response_text, re.DOTALL)
            if patch_match: patch_diff = patch_match.group(1).strip()
            strategy_match = re.search(r'REPAIR_STRATEGY_START:\s*(.*?)\s*REPAIR_STRATEGY_END:', response_text, re.DOTALL)
            if strategy_match: repair_strategy = strategy_match.group(1).strip()
            variables_match = re.search(r'KEY_VARIABLES_START:\s*(.*?)\s*KEY_VARIABLES_END:', response_text, re.DOTALL)
            if variables_match:
                variables_text = variables_match.group(1).strip()
                if variables_text: key_variables = [var.strip() for var in variables_text.split(',') if var.strip()]
            analysis_match = re.search(r'VULNERABILITY_ANALYSIS_START:\s*(.*?)\s*VULNERABILITY_ANALYSIS_END:', response_text, re.DOTALL)
            if analysis_match: vulnerability_analysis = analysis_match.group(1).strip()

            if not patch_diff or not patch_diff.strip():
                if verbose: print(f"    错误: patch_diff字段为空 (根本原因 {rc_idx_param+1})")
                return None
            if not repair_strategy or not repair_strategy.strip():
                if verbose: print(f"    错误: repair_strategy字段为空 (根本原因 {rc_idx_param+1})")
                return None
            if not vulnerability_analysis or not vulnerability_analysis.strip():
                if verbose: print(f"    错误: vulnerability_analysis字段为空 (根本原因 {rc_idx_param+1})")
                return None
            
            repair_suggestion = {
                "suggestion_patch": patch_diff,
                "repair_strategy": repair_strategy,
                "key_variables": key_variables,
                "source_root_cause_desc": root_cause_desc_param,
                "source_example_pre_repair_state": "Direct LLM repair - no example used",
                "source_example_post_repair_state": vulnerability_analysis,
                "source_example_code_before": "N/A",
                "source_example_code_after": "N/A",
                "source_example_distance": 999.0,
                "llm_score": 1.0,
                "repair_method": "direct_llm"
            }
            if verbose:
                print(f"    成功生成基于根本原因 {rc_idx_param+1} 的修复补丁")
                # print(f"    修复策略: {repair_strategy}") # Verbosity can be reduced
                # print(f"    关键变量: {key_variables}")
            return repair_suggestion
        except Exception as e:
            if verbose:
                print(f"    错误: 解析或处理响应失败 (根本原因 {rc_idx_param+1}) - {e}")
                if 'response_text' in locals(): print(f"    响应内容: {response_text[:500]}...")
            return None

    # Note: This function is typically called with len(root_causes) == 1.
    # The ThreadPoolExecutor is applied as per plan, but will run one task in current usage.
    collected_suggestions_with_idx = []
    with concurrent.futures.ThreadPoolExecutor(max_workers=32) as executor:
        future_to_idx = {
            executor.submit(_generate_for_one_root_cause, rc_idx, root_cause_desc): rc_idx
            for rc_idx, root_cause_desc in enumerate(root_causes)
        }
        for future in concurrent.futures.as_completed(future_to_idx):
            original_rc_idx = future_to_idx[future]
            try:
                suggestion = future.result()
                if suggestion:
                    collected_suggestions_with_idx.append((original_rc_idx, suggestion))
            except Exception as exc:
                if verbose:
                    print(f"    错误: 生成直接LLM修复时发生异常 (根本原因索引 {original_rc_idx}): {exc}")
    
    # Sort by original root cause index to maintain determinism if multiple were processed
    collected_suggestions_with_idx.sort(key=lambda x: x[0])
    repair_suggestions = [item[1] for item in collected_suggestions_with_idx]

    if repair_suggestions:
        if verbose: # Moved print outside the loop
             print(f"成功生成 {len(repair_suggestions)} 个基于根本原因的直接LLM修复建议")
        return repair_suggestions[:top_n]
    else:
        if verbose: # Moved print outside the loop
            print("未能生成任何基于根本原因的直接LLM修复建议")
        return []

# Helper function for parallel CSV row processing
def find_insertion_point(original_code: str, fixed_code: str) -> Optional[List[int]]:
    """
    根据代码差异（特别是纯插入）查找上下文相关的行号。
    如果原始代码为空且修复后代码不为空，则返回 [1]。
    对于插入操作，返回插入点之前和之后的原始行号（1-based）。
    """
    if not original_code.strip() and fixed_code.strip(): # 原始代码为空，修复代码不为空
        return [1]

    original_lines = original_code.splitlines()
    fixed_lines = fixed_code.splitlines()
    
    # difflib should already be imported in the file
    matcher = difflib.SequenceMatcher(None, original_lines, fixed_lines)
    opcodes = matcher.get_opcodes()
    
    context_lines = []
    
    for tag, i1, i2, j1, j2 in opcodes:
        # We are interested in 'insert' or 'replace' that acts like an insert (i1 == i2)
        if tag == 'insert' or (tag == 'replace' and i1 == i2):
            # This block means fixed_lines[j1:j2] is effectively inserted before original_lines[i1]
            
            current_op_context = []
            if i1 == 0: # Insertion at the beginning of the file
                if len(original_lines) > 0: # If original file has content
                    current_op_context.append(1) # Context is the first line of original content
                # If original_lines is empty, it's handled by the initial check of the function
            elif i1 == len(original_lines): # Insertion at the end of the file
                if len(original_lines) > 0:
                    current_op_context.append(len(original_lines)) # Context is the last line of original content
            else: # Insertion in the middle (0 < i1 < len(original_lines))
                # original_lines[i1-1] is the line before, original_lines[i1] is the line at/after insertion point
                current_op_context.append(i1)       # Line *before* insertion (1-based index of original_lines[i1-1])
                current_op_context.append(i1 + 1)   # Line *at/after* insertion (1-based index of original_lines[i1])
            
            if current_op_context:
                context_lines.extend(current_op_context)
                # We focus on the context of the first significant insertion operation.
                break 
                
    if context_lines:
        return sorted(list(set(context_lines))) # Return unique, sorted line numbers
    
    return None
def _process_row_and_write_result(
    row_idx: int,
    original_code: Optional[str],
    fixed_code: Optional[str],
    cwe_id: Optional[str],
    trigger_path_value: Optional[str], # Added
    # --- Parameters for generate_vulnerability_repair ---
    num_root_causes_to_analyze: int,
    num_examples_per_cause: int,
    top_n_suggestions: int,
    graph_consistent: bool,
    verbose_graph: bool,
    enable_llm_evaluation: bool,
    # evaluation_model: str, # Replaced by evaluation_model_config
    generation_model_config: dict, # 新增
    evaluation_model_config: dict, # 新增
    enable_root_cause_filtering: bool,
    min_consistency_level: str,
    min_confidence_score: float,
    enable_direct_llm_fallback: bool,
    enable_patch_grouping: bool,
    # --- Output related ---
    output_json_path: str
):
    current_entry_identifier = f"CSV Row {row_idx + 2} (CWE: {cwe_id or 'N/A'}) (Thread: {threading.get_ident()})"
    # Reduced print frequency for cleaner logs in parallel, focus on start/errors/summary
    # print(f"\nProcessing {current_entry_identifier}...")
    
    result_entry = {
        "csv_row": row_idx + 2,
        "original_code": original_code,
        "fixed_code": fixed_code,
        "cwe_id": cwe_id,
        "vulnerable_lines": [], # Added
        "line_source_method": None # Added
    }

    if not all([original_code, fixed_code, cwe_id]):
        error_msg = "Missing one or more required fields (code_before, code_after, cwe_id)."
        print(f"  Skipping {current_entry_identifier}: {error_msg}")
        result_entry.update({
            "status": "error",
            "error_message": error_msg
        })
    else:
        vulnerable_line_numbers: List[int] = []
        line_source_method: Optional[str] = None
        skip_processing_flag: bool = False

        # Try to get line numbers from trigger_path first
        if trigger_path_value and trigger_path_value.strip() and trigger_path_value.strip() != "?":
            try:
                parsed_lines = json.loads(trigger_path_value)
                if isinstance(parsed_lines, list) and all(isinstance(line, int) for line in parsed_lines):
                    if parsed_lines:  # Non-empty list
                        vulnerable_line_numbers = parsed_lines
                        line_source_method = "trigger_path"
                    else: # Empty list [] from trigger_path, fall back
                        print(f"  {current_entry_identifier}: trigger_path provided empty list, falling back to analyze_code_diff.")
                else: # Invalid format from trigger_path, fall back
                    print(f"  {current_entry_identifier}: trigger_path ('{trigger_path_value}') not a valid int list, falling back to analyze_code_diff.")
            except json.JSONDecodeError: # JSON error from trigger_path, fall back
                print(f"  {current_entry_identifier}: trigger_path ('{trigger_path_value}') JSON parse failed, falling back to analyze_code_diff.")

        # If trigger_path did not yield lines, try analyze_code_diff
        if not vulnerable_line_numbers:
            if line_source_method is None: # Only print if not already handled by trigger_path's fallback messages
                print(f"  {current_entry_identifier}: No valid lines from trigger_path, attempting analyze_code_diff...")
            try:
                diff_lines, _ = analyze_code_diff(original_code, fixed_code)
                if diff_lines:
                    vulnerable_line_numbers = diff_lines
                    line_source_method = "analyze_code_diff"
                else: # analyze_code_diff also returned empty, try find_insertion_point
                    if verbose_graph: # Assuming verbose_graph can be used for this level of detail
                        print(f"  {current_entry_identifier}: analyze_code_diff did not return lines, attempting find_insertion_point...")
                    
                    insertion_points = None
                    if original_code is not None and fixed_code is not None:
                        insertion_points = find_insertion_point(original_code, fixed_code)
                    
                    if insertion_points:
                        vulnerable_line_numbers = insertion_points
                        line_source_method = "diff_insertion_point"
                        if verbose_graph:
                            print(f"  {current_entry_identifier}: Using line numbers from find_insertion_point: {vulnerable_line_numbers}")
                    else:
                        error_msg = "No vulnerable lines found from trigger_path, analyze_code_diff, or find_insertion_point."
                        print(f"  {current_entry_identifier}: {error_msg} Skipping repair.")
                        result_entry.update({"status": "skipped_no_line_numbers", "message": error_msg, "vulnerable_lines": []})
                        skip_processing_flag = True
            except Exception as diff_e:
                error_msg = f"Failed to analyze code diff or find insertion point: {str(diff_e)}"
                print(f"  Error for {current_entry_identifier}: {error_msg}")
                result_entry.update({"status": "error_diff_analysis", "error_message": error_msg, "vulnerable_lines": []})
                skip_processing_flag = True
        
        # Update result_entry with line information if processing is to continue
        if not skip_processing_flag:
            # if line_source_method: # Log which method was successful
            #      print(f"  {current_entry_identifier}: Using line numbers from {line_source_method}: {vulnerable_line_numbers}")
            result_entry["vulnerable_lines"] = vulnerable_line_numbers
            result_entry["line_source_method"] = line_source_method
        
        if not skip_processing_flag: # This replaces the 'else:' at original line 1002
            # print(f"  Invoking generate_vulnerability_repair for {current_entry_identifier} with lines: {vulnerable_line_numbers}")
            try:
                repair_suggestions_details = generate_vulnerability_repair(
                    vulnerable_code=original_code,
                    cwe_id=cwe_id,
                    vulnerable_line_numbers=vulnerable_line_numbers,
                    generation_model_config=generation_model_config, # 传递
                    evaluation_model_config=evaluation_model_config, # 传递
                    num_root_causes_to_analyze=num_root_causes_to_analyze,
                    num_examples_per_cause=num_examples_per_cause,
                    top_n=top_n_suggestions,
                    graph_consistent=graph_consistent,
                    verbose_graph=verbose_graph,
                    enable_root_cause_filtering=enable_root_cause_filtering,
                    min_consistency_level=min_consistency_level,
                    min_confidence_score=min_confidence_score,
                    enable_direct_llm_fallback=enable_direct_llm_fallback,
                    enable_patch_grouping=enable_patch_grouping
                )
 
                if repair_suggestions_details:
                    # print(f"  Successfully generated {len(repair_suggestions_details)} repair suggestion(s) for {current_entry_identifier}.")
                    
                    evaluations = []
                    eval_stats = {}
                    ground_truth_diff = ""
                    
                    # ===== 添加Gemini模型自动评估环节 =====
                    if enable_llm_evaluation:
                        # 生成ground truth的unified diff
                        ground_truth_diff = '\n'.join(difflib.unified_diff(
                            original_code.splitlines(keepends=True),
                            fixed_code.splitlines(keepends=True),
                            fromfile='original_vulnerable.c',
                            tofile='ground_truth_fixed.c',
                            lineterm=''
                        ))
                        
                        # 为每个修复建议进行评估 (使用ThreadPoolExecutor)
                        with concurrent.futures.ThreadPoolExecutor(max_workers=32) as executor:
                            future_to_suggestion_idx = {}
                            for i, repair_suggestion_item in enumerate(repair_suggestions_details):
                                if isinstance(repair_suggestion_item, dict):
                                    suggestion_patch = repair_suggestion_item.get('suggestion_patch', '')
                                    if suggestion_patch:
                                        future = executor.submit(evaluate_patch_quality,
                                                                 original_vulnerable_code=original_code,
                                                                 generated_patch=suggestion_patch,
                                                                 ground_truth_patch=ground_truth_diff,
                                                                 cwe_id=cwe_id,
                                                                 # evaluation_model is now part of evaluation_model_config
                                                                 # Pass evaluation_model_config to evaluate_patch_quality
                                                                 model_config=evaluation_model_config,
                                                                 verbose=verbose_graph)
                                        future_to_suggestion_idx[future] = (i, repair_suggestion_item, suggestion_patch)
                                    else:
                                        print(f"    警告: 建议 {i+1} 没有有效的patch，跳过评估提交")
                                        evaluations.append({ # Add a placeholder for skipped items to maintain count if needed
                                            "suggestion_index": i, "evaluation_result": "Skipped_NoPatch",
                                            "explanation": "Patch was empty", "evaluation_details": {},
                                            "generated_patch": "", "original_suggestion_metadata": repair_suggestion_item
                                        })
                                else:
                                    print(f"    警告: 建议 {i+1} 格式不正确，跳过评估提交")
                                    evaluations.append({
                                        "suggestion_index": i, "evaluation_result": "Skipped_BadFormat",
                                        "explanation": "Suggestion format incorrect", "evaluation_details": {},
                                        "generated_patch": "", "original_suggestion_metadata": repair_suggestion_item
                                    })

                            # Collect results, ensuring order by original suggestion index
                            temp_evaluations = {} # Use dict to store results keyed by index
                            for future in concurrent.futures.as_completed(future_to_suggestion_idx):
                                original_idx, original_suggestion, patch_used = future_to_suggestion_idx[future]
                                try:
                                    eval_result, explanation, eval_data = future.result()
                                    temp_evaluations[original_idx] = {
                                        "suggestion_index": original_idx,
                                        "evaluation_result": eval_result.value,
                                        "explanation": explanation,
                                        "evaluation_details": eval_data,
                                        "generated_patch": patch_used,
                                        "original_suggestion_metadata": original_suggestion
                                    }
                                except Exception as exc:
                                    print(f"    错误: 评估建议 {original_idx+1} 时发生异常 (executor): {exc}")
                                    temp_evaluations[original_idx] = {
                                        "suggestion_index": original_idx, "evaluation_result": "Error_In_Eval",
                                        "explanation": str(exc), "evaluation_details": {},
                                        "generated_patch": patch_used, "original_suggestion_metadata": original_suggestion
                                    }
                            
                            # Populate evaluations list in order
                            for i in range(len(repair_suggestions_details)):
                                if i in temp_evaluations: # If it was processed by executor
                                    evaluations.append(temp_evaluations[i])
                                else: # If it was skipped before submission, it might already be in evaluations or needs a placeholder
                                    # This logic assumes skipped items were already added or we find them if not.
                                    # For simplicity, if an item was skipped before future submission, it should already have a placeholder.
                                    # If an item was submitted but its future is not in temp_evaluations (should not happen with as_completed),
                                    # it's an issue. The current placeholder logic for pre-skip handles this.
                                    # We ensure all original indices are covered.
                                    # Check if a placeholder for this index was already added
                                    found_placeholder = False
                                    for eval_entry in evaluations:
                                        if eval_entry["suggestion_index"] == i and "Skipped" in eval_entry["evaluation_result"]:
                                            found_placeholder = True
                                            break
                                    if not found_placeholder and i not in temp_evaluations: # Should not happen if logic is correct
                                         print(f"    警告: 建议 {i+1} 未被处理也未被跳过，添加错误占位符。")
                                         evaluations.append({
                                            "suggestion_index": i, "evaluation_result": "Error_Missed",
                                            "explanation": "Missed in processing loop", "evaluation_details": {},
                                            "generated_patch": repair_suggestions_details[i].get('suggestion_patch', ''),
                                            "original_suggestion_metadata": repair_suggestions_details[i]
                                        })
                        
                        # 计算评估统计信息
                        eval_stats = {
                            "total_suggestions": len(repair_suggestions_details),
                            "syntactic_patch_equivalent": len([e for e in evaluations if e["evaluation_result"] == "SynPatchEq"]), # 新增
                            "semantic_equivalent": len([e for e in evaluations if e["evaluation_result"] == "SemEq"]),
                            "plausible": len([e for e in evaluations if e["evaluation_result"] == "Plausible"]),
                            "incorrect": len([e for e in evaluations if e["evaluation_result"] == "Incorrect"]),
                            "unknown": len([e for e in evaluations if e["evaluation_result"] == "Unknown"])
                        }
                    # ===== Gemini模型自动评估环节结束 =====
                    
                    # 构建结果条目
                    result_update = {
                        "vulnerable_lines": vulnerable_line_numbers,
                        "status": "success",
                        "repairs": repair_suggestions_details
                    }
                    
                    if enable_llm_evaluation:
                        result_update.update({
                            "llm_evaluations": evaluations,
                            "evaluation_statistics": eval_stats,
                            "ground_truth_patch": ground_truth_diff
                        })
                    
                    result_entry.update(result_update)
                elif isinstance(repair_suggestions_details, list) and not repair_suggestions_details:
                    msg = "No suitable repair suggestions found or extracted."
                    # print(f"  {msg} for {current_entry_identifier}.")
                    result_entry.update({
                        "vulnerable_lines": vulnerable_line_numbers,
                        "status": "no_suggestion_found",
                        "message": msg
                    })
                else:
                    msg = f"Failed to generate repair (unexpected return: {type(repair_suggestions_details)})."
                    # print(f"  {msg} for {current_entry_identifier}.")
                    result_entry.update({
                        "vulnerable_lines": vulnerable_line_numbers if vulnerable_line_numbers else [],
                        "status": "error",
                        "error_message": msg
                    })
            except Exception as e:
                error_msg = f"Unexpected error during repair generation for {current_entry_identifier}: {str(e)}"
                print(f"  Critical Error for {current_entry_identifier}: {error_msg}")
                result_entry.update({
                    "vulnerable_lines": vulnerable_line_numbers if vulnerable_line_numbers else [],
                    "status": "error",
                    "error_message": error_msg
                })

    # Write the result to the JSON Lines file
    try:
        result_json_str = json.dumps(result_entry, ensure_ascii=False) + '\n'
        with open(output_json_path, 'a', encoding='utf-8') as outfile:
            outfile.write(result_json_str)
    except Exception as write_e:
        print(f"  Error writing result for {current_entry_identifier} to {output_json_path}: {write_e}")


def process_vulnerabilities_from_csv(
    csv_file_path: str,
    output_json_path: str,
    num_root_causes_to_analyze: int = 3,
    num_examples_per_cause: int = 2,
    top_n_suggestions: int = 1, # Corresponds to top_n in generate_vulnerability_repair
    graph_consistent: bool = False,
    verbose_graph: bool = False,
    enable_llm_evaluation: bool = True,
    generation_model_config: dict = None, # 新增, with default for now
    evaluation_model_config: dict = None, # 新增, with default for now
    enable_root_cause_filtering: bool = True,
    min_consistency_level: str = "Medium",
    min_confidence_score: float = 0.6,
    enable_direct_llm_fallback: bool = True,
    enable_patch_grouping: bool = False
) -> None:
    """
    Processes vulnerabilities from a CSV file in parallel, generates repairs,
    and outputs results incrementally to a JSON Lines file.
    """
    # Default model configs if None are provided (basic OpenRouter setup)
    if generation_model_config is None:
        generation_model_config = {
        "provider": "openai",
        "model_name": "ep-20250329153117-pqs5b",
        "base_url": "https://ark.cn-beijing.volces.com/api/v3",
        "api_key": '2c504220-0507-499d-9369-a840379e8e27',
    }
    if evaluation_model_config is None:
        evaluation_model_config = {
        "provider": "openai",
        "model_name": "ep-20250329153117-pqs5b",
        "base_url": "https://ark.cn-beijing.volces.com/api/v3",
        "api_key": '2c504220-0507-499d-9369-a840379e8e27',
    }

    print(f"Starting PARALLEL vulnerability processing from CSV: {csv_file_path}")
    print(f"Output will be written to: {output_json_path} as JSON Lines.")
    
    gen_provider = generation_model_config.get('provider', 'N/A')
    gen_model_name = generation_model_config.get('model_name', 'N/A')
    print(f"Generation Model: Provider={gen_provider}, Model={gen_model_name}")

    print(f"LLM Evaluation: {'启用' if enable_llm_evaluation else '禁用'}")
    if enable_llm_evaluation:
        eval_provider = evaluation_model_config.get('provider', 'N/A')
        eval_model_name = evaluation_model_config.get('model_name', 'N/A')
        print(f"Evaluation Model: Provider={eval_provider}, Model={eval_model_name}")

    print(f"Root Cause一致性过滤: {'启用' if enable_root_cause_filtering else '禁用'} (级别: {min_consistency_level}, 置信度: {min_confidence_score})")
    print(f"直接LLM修复后备方案: {'启用' if enable_direct_llm_fallback else '禁用'}")
    print(f"Patch分组增强算法: {'启用' if enable_patch_grouping else '禁用'}")

    # 移除threading.Lock，因为在多进程环境下无法工作
    # JSON Lines格式天然支持并发追加写入

    try:
        with open(output_json_path, 'w', encoding='utf-8') as outfile:
            pass # Clears the file or creates it if it doesn't exist
        print(f"Output file {output_json_path} initialized (cleared).")
    except IOError as e:
        print(f"Error: Failed to initialize output file {output_json_path}: {e}")
        return

    tasks_to_submit = []
    try:
        with open(csv_file_path, mode='r', encoding='utf-8', newline='') as infile:
            reader = csv.DictReader(infile)
            if not reader.fieldnames:
                print(f"Error: CSV file {csv_file_path} is empty or has no header.")
                return

            required_columns = ['code_before', 'code_after', 'CWE ID'] # Ensure this matches your CSV
            missing_columns = [col for col in required_columns if col not in reader.fieldnames]
            if missing_columns:
                print(f"Error: CSV file {csv_file_path} is missing required columns: {', '.join(missing_columns)}")
                return

            for row_idx, row_data in enumerate(reader):
                tasks_to_submit.append({
                    "row_idx": row_idx,
                    "original_code": row_data.get('code_before'),
                    "fixed_code": row_data.get('code_after'),
                    "cwe_id": row_data.get('CWE ID'),
                    "trigger_path_value": row_data.get('trigger_path'), # Added
                    "num_root_causes_to_analyze": num_root_causes_to_analyze,
                    "num_examples_per_cause": num_examples_per_cause,
                    "top_n_suggestions": top_n_suggestions,
                    "graph_consistent": graph_consistent,
                    "verbose_graph": verbose_graph,
                    "enable_llm_evaluation": enable_llm_evaluation,
                    # "evaluation_model": evaluation_model, # Replaced
                    "generation_model_config": generation_model_config, # Pass dict
                    "evaluation_model_config": evaluation_model_config, # Pass dict
                    "enable_root_cause_filtering": enable_root_cause_filtering,
                    "min_consistency_level": min_consistency_level,
                    "min_confidence_score": min_confidence_score,
                    "enable_direct_llm_fallback": enable_direct_llm_fallback,
                    "enable_patch_grouping": enable_patch_grouping,
                    "output_json_path": output_json_path
                })
        
        if not tasks_to_submit:
            print(f"No tasks to process from {csv_file_path}.")
            # Write an empty list or some indicator to the output file if desired, or just leave it empty.
            # For JSON Lines, an empty file is valid.
            return

        processed_count = 0
        # 确定进程数量
        cpu_cores = multiprocessing.cpu_count()
        max_workers = min(16, max(1, cpu_cores - 1))  # 保留一个核心给主进程
        
        with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
            # Submit tasks and store futures if you need to track them individually later,
            # but as_completed is good for processing as they finish.
            future_to_row_idx = {
                executor.submit(
                    _process_row_and_write_result,
                    task["row_idx"], task["original_code"], task["fixed_code"], task["cwe_id"],
                    task["trigger_path_value"],
                    task["num_root_causes_to_analyze"], task["num_examples_per_cause"],
                    task["top_n_suggestions"], task["graph_consistent"], task["verbose_graph"],
                    task["enable_llm_evaluation"],
                    # task["evaluation_model"], # Replaced
                    task["generation_model_config"], # Pass dict
                    task["evaluation_model_config"], # Pass dict
                    task["enable_root_cause_filtering"],
                    task["min_consistency_level"],
                    task["min_confidence_score"],
                    task["enable_direct_llm_fallback"],
                    task["enable_patch_grouping"],
                    task["output_json_path"]
                ): task["row_idx"] for task in tasks_to_submit
            }
 
        for future in concurrent.futures.as_completed(future_to_row_idx):
            row_idx_completed = future_to_row_idx[future]
            try:
                future.result() # Wait for task to complete and raise exceptions if any occurred in the task
                processed_count += 1
                # print(f"  Task for CSV row {row_idx_completed + 2} completed. ({processed_count}/{len(tasks_to_submit)})")
            except Exception as e:
                processed_count += 1
                # This error is from _process_row_and_write_result, which should have already
                # logged and written its specific error to the JSON line.
                # This print is for unexpected errors in the future itself or unhandled ones.
                print(f"  Error processing task for CSV row {row_idx_completed + 2}: {e}. ({processed_count}/{len(tasks_to_submit)})")
        
        # print(f"\nAll {len(tasks_to_submit)} tasks submitted and processing initiated/completed.")
        # print(f"Total results expected in {output_json_path}: {processed_count} (should match task count).")

    except FileNotFoundError:
        print(f"Error: Input CSV file not found at {csv_file_path}")
        # Optionally write an error to the output JSON if it was initialized
        # For now, just prints and exits.
    except csv.Error as csve:
        print(f"Error: CSV parsing error for file {csv_file_path}: {csve}")
    except IOError as ioe: # Catch other IOErrors, e.g., during CSV read
        print(f"Error: An I/O error occurred with {csv_file_path}: {ioe}")
    except Exception as e_global:
        print(f"An unexpected global error occurred during CSV processing or task submission: {e_global}")
        # import traceback; traceback.print_exc() # For detailed debugging

    print(f"\nProcessing complete. Results are in: {output_json_path}")
    # The old block for writing all_results and its try-except is removed.

def evaluate_patch_quality(
    original_vulnerable_code: str,
    generated_patch: str,
    ground_truth_patch: str,
    cwe_id: str,
    model_config: dict, # 更改: 接收模型配置
    # evaluation_model: str = "gemini", # Replaced by model_config
    verbose: bool = False
) -> Tuple[EvaluationResult, str, dict]:
    """
    使用指定模型自动评估生成的补丁质量
    
    Args:
        original_vulnerable_code: 原始有漏洞的代码
        generated_patch: 生成的修复补丁 (unified diff格式)
        ground_truth_patch: 标准答案补丁 (unified diff格式)
        cwe_id: CWE漏洞类型ID
        model_config: 包含提供商和模型名称的配置字典
        verbose: 是否输出详细信息
        
    Returns:
        Tuple[EvaluationResult, str, dict]: (评估结果, 详细解释, 原始评估数据)
    """
    provider = model_config.get("provider", "openai").lower() # Default to gemini if not specified
    llm_display_name = f"{provider.capitalize()} (Model: {model_config.get('model_name', 'default')})"

    if verbose:
        print(f"  开始使用{llm_display_name}模型自动评估补丁质量 (CWE: {cwe_id})...")
    
    # 构建评估prompt
    evaluation_prompt = f"""
You are an expert security code reviewer evaluating a generated vulnerability fix.

**Original Vulnerable Code:**
```c
{add_line_numbers_to_code(original_vulnerable_code)}
```

**CWE Type:** {cwe_id}

**Ground Truth Patch (Reference Solution - Unified Diff Format):**
```diff
{ground_truth_patch}
```

**Generated Patch (Unified Diff Format):**
```diff
{generated_patch}
```

**Evaluation Task:**
Compare the 'Generated Patch' to the 'Ground Truth Patch' and assess its quality. Follow this evaluation order:

**1. Syntactic Patch Equivalence (SynPatchEq) (Highest Priority):**
   - Is the text content of the 'Generated Patch' **IDENTICAL** to the text content of the 'Ground Truth Patch'?
   If yes, classify as **SynPatchEq**. This is the final evaluation.

**2. Correctness Assessment (If not SynPatchEq):**
   If not SynPatchEq, evaluate if the 'Generated Patch', when applied to the 'Original Vulnerable Code', is correct:
   - Does it fully fix the vulnerability?
   - Does it maintain original functionality without breaking anything?
   - Is it technically sound?
   If it fails any of these, classify as **Incorrect**.

**3. Semantic Equivalence (If Correct but not SynPatchEq) vs. Plausible (If Correct but not SemEq):**
   If the 'Generated Patch' is Correct (and not SynPatchEq), classify its *applied effect*:
   - **SemEq**: The applied 'Generated Patch' achieves the same core security effect and functional behavior as the applied 'Ground Truth Patch'. It **must include all essential security requirements** from the 'Ground Truth Patch'. It may contain **additional, non-conflicting security checks** (redundancy is acceptable if it doesn't break functionality or introduce new vulnerabilities). The fundamental security outcome and functional preservation are equivalent, even if implementation details differ.
   - **Plausible**: The applied 'Generated Patch' is a technically sound alternative solution that fixes the vulnerability and maintains functionality. It **must include all essential security requirements** from the 'Ground Truth Patch', even if it uses a different overall approach. It may contain **additional, non-conflicting security checks** (redundancy is acceptable if it doesn't break functionality or introduce new vulnerabilities).
   - **Incorrect**: If it doesn't meet SemEq or Plausible criteria after passing Correctness.

**Output Format (JSON):**
{{
    "detailed_explanation": "Briefly explain your decision, highlighting key reasons.",
    "evaluation_result": "SynPatchEq" | "SemEq" | "Plausible" | "Incorrect",
    "confidence_level": "High/Medium/Low"
}}
Important: Be strict but fair. Focus on the definitions provided.
"""

    try:
        if verbose:
            print(f"    调用{llm_display_name}模型进行补丁质量评估...")
        
        api_call_params = {
            "prompt": evaluation_prompt,
            "model_config": model_config,
            "n": 1,
            "max_tokens": 5000, # Specific for this evaluation task
            "temperature": model_config.get("temperature", DEFAULT_OPENAI_PARAMS.get("temperature", 0))
        }

        # 根据选择的模型调用相应的API
        if provider == "gemini":
            model_responses = generate_with_Gemini_model(**api_call_params)
        elif provider in ["openai", "openrouter", "azure", "volcengine"]: # Assuming OpenAI_API handles these
            model_responses = generate_with_OpenAI_model(**api_call_params)
        else:
            raise ValueError(f"Unsupported provider in model_config for evaluation: {provider}")
        
        if not model_responses or not model_responses[0]:
            if verbose:
                print(f"    警告: {llm_display_name}模型未返回有效评估响应")
            return EvaluationResult.UNKNOWN, f"{llm_display_name}模型评估失败：无响应", {}
            
        response_text = model_responses[0].strip()
        
        # 尝试解析JSON响应
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text)
        if not json_match:
            json_match = re.search(r'({[\s\S]*})', response_text)
            
        if json_match:
            json_str = json_match.group(1)
            try:
                evaluation_data = json.loads(json_str)
                
                result_str = evaluation_data.get("evaluation_result", "").strip()
                detailed_explanation = evaluation_data.get("detailed_explanation", "")
                
                if result_str.lower() == "synpatcheq":
                    evaluation_result = EvaluationResult.SYNTACTIC_PATCH_EQUIVALENT
                elif result_str.lower() == "semeq":
                    evaluation_result = EvaluationResult.SEMANTIC_EQUIVALENT
                elif result_str.lower() == "plausible":
                    evaluation_result = EvaluationResult.PLAUSIBLE
                elif result_str.lower() == "incorrect":
                    evaluation_result = EvaluationResult.INCORRECT
                else:
                    if verbose: print(f"    警告: 未知的评估结果 '{result_str}'，标记为UNKNOWN")
                    evaluation_result = EvaluationResult.UNKNOWN
                
                if verbose:
                    print(f"    {llm_display_name}模型评估结果: {evaluation_result.value}")
                    print(f"    置信度: {evaluation_data.get('confidence_level', 'N/A')}")
                
                return evaluation_result, detailed_explanation, evaluation_data
                
            except json.JSONDecodeError as e:
                if verbose: print(f"    错误: JSON解析失败 - {e}")
                return EvaluationResult.UNKNOWN, f"JSON解析错误: {e}", {"raw_response": response_text}
        else:
            if verbose: print(f"    警告: {llm_display_name}模型响应中未找到有效JSON格式")
            return EvaluationResult.UNKNOWN, "响应格式错误：未找到JSON", {"raw_response": response_text}
            
    except Exception as e:
        if verbose: print(f"    错误: {llm_display_name}模型评估过程中发生异常 - {e}")
        return EvaluationResult.UNKNOWN, f"评估异常: {e}", {}

# 新增辅助函数：为代码添加行号
def add_line_numbers_to_code(code_string: str) -> str:
    """Adds 1-based line numbers to each line of a code string."""
    if not code_string or not code_string.strip():
        return code_string # Return as is if empty or only whitespace
    
    lines = code_string.splitlines()
    numbered_lines = [f"{i+1:3d} | {line}" for i, line in enumerate(lines)]
    return "\n".join(numbered_lines)

def format_lines_with_statements(code_string: str, line_numbers: List[int]) -> str:
    """Formats specified line numbers with their corresponding code statements."""
    if not line_numbers:
        return "No specific lines provided."
    
    code_lines = code_string.splitlines()
    output_parts = []
    
    # Ensure line numbers are unique, sorted, and within valid range
    # Also handle potential non-integer items in line_numbers if they come from LLM output directly
    valid_line_numbers = []
    for item in set(line_numbers): # Use set to get unique line numbers
        try:
            ln = int(item)
            if 1 <= ln <= len(code_lines):
                valid_line_numbers.append(ln)
        except (ValueError, TypeError):
            # Ignore items that cannot be converted to int
            pass
    
    valid_line_numbers.sort() # Sort them

    if not valid_line_numbers:
        return "Provided line numbers are out of range, empty after validation, or not valid integers."

    for ln in valid_line_numbers:
        # list index is 0-based, line numbers are 1-based
        statement = code_lines[ln - 1]
        output_parts.append(f"{ln}: {statement}")
        
    return "\n".join(output_parts)

def evaluate_root_cause_consistency(
    current_root_cause: str,
    example_root_cause: str,
    cwe_id: str,
    model_config: dict, # 新增: 用于LLM评估的模型配置
    verbose: bool = False
) -> Tuple[RootCauseConsistency, str, float]:
    """
    使用LLM评估当前分析的root cause与示例的root cause是否一致
    
    Args:
        current_root_cause: 当前分析出的根本原因描述
        example_root_cause: 示例的根本原因描述
        cwe_id: CWE漏洞类型ID
        model_config: 用于LLM评估的模型配置
        verbose: 是否输出详细信息
        
    Returns:
        Tuple[RootCauseConsistency, str, float]: (一致性级别, 详细解释, 一致性分数0-1)
    """
    if verbose:
        print(f"    评估Root Cause一致性 (CWE: {cwe_id})...")
    
    # 构建一致性评估prompt
    consistency_prompt = f"""
You are an expert in vulnerability analysis and root cause identification. Your task is to evaluate whether two root cause descriptions are semantically consistent and refer to the same underlying vulnerability pattern.

**CWE Type:** {cwe_id}

**Current Root Cause (Analyzed for target vulnerability):**
"{current_root_cause}"

**Example Root Cause (From reference case):**
"{example_root_cause}"

**Evaluation Task:**
Compare these two root cause descriptions and determine their consistency level. Consider:

1. **Core vulnerability mechanism**: Do they describe the same fundamental security flaw?
2. **Causal chain**: Do they follow the same source→sink vulnerability path?
3. **Triggering conditions**: Are the conditions that enable the vulnerability similar?
4. **Impact scope**: Do they affect the same type of security properties?
5. **Technical context**: Are they applicable to similar code contexts?

**Consistency Levels:**
- **High**: Root causes describe essentially the same vulnerability pattern with very similar mechanisms
- **Medium**: Root causes are related and share common vulnerability aspects but differ in some details
- **Low**: Root causes have some conceptual overlap but represent different vulnerability patterns
- **Inconsistent**: Root causes describe completely different vulnerability types or mechanisms

**Important Notes:**
- Focus on semantic meaning rather than exact wording
- Consider the specific CWE type context
- Two descriptions can be consistent even if they use different technical terms
- Look for the underlying vulnerability logic, not surface-level text similarity

**Output Format:**
Provide your evaluation in the following JSON format:
{{
    "detailed_explanation": "Detailed explanation of why you assigned this consistency level",
    "key_similarities": "Main similarities between the root causes",
    "key_differences": "Main differences between the root causes",
    "consistency_level": "High" | "Medium" | "Low" | "Inconsistent",
    "confidence_score": <float between 0.0 and 1.0>,
    "recommendation": "Whether this example should be used for repair generation (Use/Consider/Avoid)"
}}

Provide only the JSON response without any additional text or formatting.
"""

    try:
        # 调用LLM进行一致性评估
        api_call_params_consistency = {
            "prompt": consistency_prompt,
            "model_config": model_config,
            "max_tokens": 5000,
            "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0),
            "n": DEFAULT_OPENAI_PARAMS.get("n", 1)
        }
        
        if verbose:
            print(f"      调用LLM评估Root Cause一致性...")
        
        llm_responses = generate_with_OpenAI_model(**api_call_params_consistency)
        
        if not llm_responses or not llm_responses[0]:
            if verbose:
                print("      警告: Root Cause一致性评估LLM未返回有效响应")
            return RootCauseConsistency.INCONSISTENT, "LLM评估失败：无响应", 0.0
            
        response_text = llm_responses[0].strip()
        
        # 尝试解析JSON响应
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text)
        if not json_match:
            json_match = re.search(r'({[\s\S]*})', response_text)
            
        if json_match:
            json_str = json_match.group(1)
            try:
                consistency_data = json.loads(json_str)
                
                # 提取一致性级别
                level_str = consistency_data.get("consistency_level", "").strip()
                confidence_score = float(consistency_data.get("confidence_score", 0.0))
                detailed_explanation = consistency_data.get("detailed_explanation", "")
                
                # 映射结果字符串到枚举
                if level_str.lower() == "high":
                    consistency_level = RootCauseConsistency.HIGH
                elif level_str.lower() == "medium":
                    consistency_level = RootCauseConsistency.MEDIUM
                elif level_str.lower() == "low":
                    consistency_level = RootCauseConsistency.LOW
                elif level_str.lower() == "inconsistent":
                    consistency_level = RootCauseConsistency.INCONSISTENT
                else:
                    if verbose:
                        print(f"      警告: 未知的一致性级别 '{level_str}'，标记为INCONSISTENT")
                    consistency_level = RootCauseConsistency.INCONSISTENT
                    confidence_score = 0.0
                
                if verbose:
                    print(f"      Root Cause一致性评估结果: {consistency_level.value}")
                    print(f"      置信度分数: {confidence_score:.2f}")
                    recommendation = consistency_data.get("recommendation", "N/A")
                    print(f"      使用建议: {recommendation}")
                
                return consistency_level, detailed_explanation, confidence_score
                
            except (json.JSONDecodeError, ValueError) as e:
                if verbose:
                    print(f"      错误: Root Cause一致性JSON解析失败 - {e}")
                return RootCauseConsistency.INCONSISTENT, f"JSON解析错误: {e}", 0.0
        else:
            if verbose:
                print("      警告: Root Cause一致性LLM响应中未找到有效JSON格式")
            return RootCauseConsistency.INCONSISTENT, "响应格式错误：未找到JSON", 0.0
            
    except Exception as e:
        if verbose:
            print(f"      错误: Root Cause一致性评估过程中发生异常 - {e}")
        return RootCauseConsistency.INCONSISTENT, f"评估异常: {e}", 0.0

def filter_examples_by_root_cause_consistency(
    current_root_cause: str,
    examples: List[dict],
    cwe_id: str,
    model_config: dict, # 新增: 用于LLM评估的模型配置
    min_consistency_level: RootCauseConsistency = RootCauseConsistency.MEDIUM,
    min_confidence_score: float = 0.6,
    verbose: bool = False
) -> List[dict]:
    """
    根据root cause一致性过滤示例
    
    Args:
        current_root_cause: 当前分析的根本原因
        examples: 从Faiss查询到的示例列表
        cwe_id: CWE漏洞类型ID
        model_config: 用于LLM评估的模型配置
        min_consistency_level: 最小一致性级别要求
        min_confidence_score: 最小置信度分数要求
        verbose: 是否输出详细信息
        
    Returns:
        List[dict]: 过滤后的示例列表
    """
    if not examples:
        return []
    
    if verbose:
        print(f"    开始根据Root Cause一致性过滤 {len(examples)} 个示例...")
    
    filtered_examples = []
    consistency_levels_order = {
        RootCauseConsistency.HIGH: 4,
        RootCauseConsistency.MEDIUM: 3,
        RootCauseConsistency.LOW: 2,
        RootCauseConsistency.INCONSISTENT: 1
    }
    
    min_level_value = consistency_levels_order.get(min_consistency_level, 3)
    
    # 统计各级别的示例数量
    level_stats = {
        "High": 0,
        "Medium": 0,
        "Low": 0,
        "Inconsistent": 0,
        "Missing": 0
    }
    
    with concurrent.futures.ThreadPoolExecutor(max_workers=32) as executor:
        future_to_example_info = {}
        for idx, example_data in enumerate(examples): # Renamed 'example' to 'example_data' to avoid conflict
            example_root_cause = example_data.get("pre_repair_state_example", "")
            
            if not example_root_cause or example_root_cause == "Not available":
                level_stats["Missing"] += 1
                if verbose:
                    print(f"      示例 {idx+1}: 跳过（缺少root cause信息）")
                continue
            
            future = executor.submit(evaluate_root_cause_consistency,
                                     current_root_cause=current_root_cause,
                                     example_root_cause=example_root_cause,
                                     cwe_id=cwe_id,
                                     model_config=model_config, # 传递评估模型配置
                                     verbose=verbose)
            future_to_example_info[future] = (idx, example_data)

        for future in concurrent.futures.as_completed(future_to_example_info):
                idx, original_example_data = future_to_example_info[future]
                try:
                    consistency_level, explanation, confidence_score = future.result()
                    
                    # 统计级别
                    level_stats[consistency_level.value] += 1
                    
                    level_value = consistency_levels_order.get(consistency_level, 1)
                    
                    # 检查是否满足最小要求
                    if level_value >= min_level_value and confidence_score >= min_confidence_score:
                        # 为示例添加一致性信息
                        enhanced_example = original_example_data.copy()
                        enhanced_example.update({
                            "root_cause_consistency_level": consistency_level.value,
                            "root_cause_confidence_score": confidence_score,
                            "root_cause_consistency_explanation": explanation
                        })
                        filtered_examples.append(enhanced_example)
                        
                        if verbose:
                            print(f"      示例 {idx+1}: 保留 ({consistency_level.value}, 置信度: {confidence_score:.2f})")
                    else:
                        if verbose:
                            reason = "级别不足" if level_value < min_level_value else "置信度不足"
                            print(f"      示例 {idx+1}: 过滤掉 ({consistency_level.value}, 置信度: {confidence_score:.2f}) - {reason}")
                except Exception as exc:
                    print(f"      Error evaluating consistency for example {idx+1} (original data: {str(original_example_data)[:100]}...): {exc}")
                    # Optionally, log this error more formally or handle it
        
    # 输出详细统计信息
    if verbose:
        print(f"    Root Cause一致性过滤统计:")
        print(f"      总示例数: {len(examples)}")
        print(f"      各级别分布: High={level_stats['High']}, Medium={level_stats['Medium']}, Low={level_stats['Low']}, Inconsistent={level_stats['Inconsistent']}, Missing={level_stats['Missing']}")
        print(f"      过滤要求: 最小级别={min_consistency_level}, 最小置信度={min_confidence_score}")
        print(f"      最终保留: {len(filtered_examples)} 个示例")
    else:
        print(f"    Root Cause一致性过滤完成: {len(filtered_examples)}/{len(examples)} 个示例保留")
    
    return filtered_examples

def main():
    dataset="zeroday_2024"
    process_vulnerabilities_from_csv(
        csv_file_path=f"/mnt/projects/unnamed/datasets/{dataset}/processed_vulnerabilities.csv",
        output_json_path=f"/mnt/projects/unnamed/datasets/{dataset}/results_output.json",
        num_root_causes_to_analyze=5,
        num_examples_per_cause=5,
        top_n_suggestions=5,
        graph_consistent=True,
        verbose_graph=False,
        enable_llm_evaluation=True,
        enable_root_cause_filtering=True,
        min_consistency_level="Medium",
        min_confidence_score=0.6,
        enable_direct_llm_fallback=True,
        enable_patch_grouping=False  # 新增：启用patch分组增强算法
    )

if __name__ == '__main__':
    main()
    # test()

