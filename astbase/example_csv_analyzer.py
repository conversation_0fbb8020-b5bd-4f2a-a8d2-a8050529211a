#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSV AST分析器示例脚本

该脚本展示了如何使用csv_ast_analyzer模块分析CSV文件中的代码编辑对并生成AST关系树形结构。
"""

import os
import csv
from csv_ast_analyzer import analyze_csv_edits

# 示例CSV文件路径
EXAMPLE_CSV_PATH = "example_edits.csv"
OUTPUT_DIR = "output"

def create_example_csv():
    """创建示例CSV文件，包含几个简单的代码编辑对"""

    # 示例代码编辑对
    edit_pairs = [
        # 编辑对1：添加空指针检查
        (
            """
            void process_data(char *data) {
                int len = strlen(data);
                char *buffer = malloc(len + 1);
                strcpy(buffer, data);
                // 处理数据
                free(buffer);
            }
            """,
            """
            void process_data(char *data) {
                int len = strlen(data);
                char *buffer = malloc(len + 1);
                strncpy(buffer, data);
                // 处理数据
                free(buffer);
            }
            """
        ),
        # 编辑对2：修复缓冲区溢出
        (
            """
            void copy_string(char *dest, const char *src) {
                strcpy(dest, src);
            }
            """,
            """
            void copy_string(char *dest, const char *src) {
                strncpy(dest, src);
            }
            """
        ),
        # 编辑对3：添加资源释放
        (
            """
            FILE *open_file(const char *filename) {
                FILE *file = fopen(filename, "r");
                if (file == NULL) {
                    printf("Error opening file\\n");
                }
                return file;
            }
            """,
            """
            FILE *open_file(const char *filename) {
                FILE *file = fopen(filename, "r");
                if (file == NULL) {
                    printf("Error opening file\\n");
                    return NULL;
                }
                return file;
            }
            """
        ),
        # 编辑对4：修复整数溢出
        (
            """
            int multiply(int a, int b) {
                return a * b;
            }
            """,
            """
            #include <limits.h>

            int multiply(int a, int b) {
                if (a > 0 && b > 0 && a > INT_MAX / b) {
                    return -1; // 溢出错误
                }
                if (a < 0 && b < 0 && a < INT_MAX / b) {
                    return -1; // 溢出错误
                }
                return a * b;
            }
            """
        ),
        # 编辑对5：修复空指针解引用
        (
            """
            int get_length(char *str) {
                return strlen(str);
            }
            """,
            """
            int get_length(char *str) {
                if (str == NULL) {
                    return 0;
                }
                return strlen(str);
            }
            """
        )
    ]

    # 创建CSV文件
    with open(EXAMPLE_CSV_PATH, 'w', newline='') as f:
        writer = csv.writer(f)
        for before, after in edit_pairs:
            writer.writerow([before, after])

    print(f"示例CSV文件已创建：{EXAMPLE_CSV_PATH}")
    return EXAMPLE_CSV_PATH

def main():
    """主函数"""

    # 创建示例CSV文件
    csv_path = '/mnt/projects/unnamed/datasets/ICVul/processed_vulnerabilities.csv'#create_example_csv()

    # 确保输出目录存在
    os.makedirs(OUTPUT_DIR, exist_ok=True)

    # 分析CSV文件中的代码编辑对
    print("开始分析代码编辑对...")
    relation_tree = analyze_csv_edits(
        csv_file=csv_path,
        output_dir=OUTPUT_DIR,
        language='c',
        before_col=0,
        after_col=1,
        has_header=False
    )

    # 打印分析结果
    print("\n分析完成！结果已保存到目录：", OUTPUT_DIR)
    # print("\n关系树结构：")
    # print(relation_tree.to_json())

if __name__ == "__main__":
    main()
