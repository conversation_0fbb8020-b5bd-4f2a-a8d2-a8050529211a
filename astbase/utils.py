import re
import traceback
from openai import OpenAI
import pandas as pd
from concurrent.futures import ProcessPoolExecutor, as_completed
import json
from tqdm import tqdm
import difflib
import csv

###TODO
"""
整个条件Condition：
1.领域 D
2.功能 F
3.entry S
4.Propagation P1, P2, P3...
5.exit C

修复规则 R:
Condition -> Condition'
\exists x \, (D(x) \land C_1(x) \land C_2(x)) \rightarrow R(x)

合并规则 M:
D' -> D1 V D2
F' -> F1 (intersect) F2
A' -> (A11 or A12) and (A21 or A22) and A3 ...

种类：
Api Misuse
Buffer Overflow
Command Injection
Cross-Site Scripting
Cryptographic Issues
Denial of Service
"""
###

def getFunction3(vul, fix, slice_context=None):  # focuss
    def get_diff(vul, fix):
        diff = difflib.unified_diff(vul.splitlines(), fix.splitlines(), lineterm='')
        return '\n'.join(diff)
    patch = get_diff(vul, fix)
    client = OpenAI(
        base_url="https://api.deepseek.com/v1",
        api_key="***********************************",
    )
    if slice_context is None or slice_context == 'None':
        prompt = f"""
    - Analyze a given patch written in C with a statement-level edit operation for fixing a security vulnerability. 
    - Provide detailed summary of the patch.
    - Your summary should include three parts:
    1. The application domain of the edited statement.
    2. The functionality of the edited statement.
    3. The condition of preventing triggering the vulnerability. You should provide the condition in the form of a natural language abstract flow. \
            And it contain three parts: 1) The entry of the whole trace of the edited statement, that is direction of the program control flow before the edited statement execution is started and is related to the edited statement. \
                2) The propagation flow of the trace of the edited statement between entry and exit, in which one or more element is for preventing vulnerablity triggering operation. \
                3) the exit of the trace of the edited statement, that is direction of the program control flow after the edited statement execution is completed.
    - In the flow trace, every element in the trace should be a predicate at the finest granularity.
    - Each predicate of entry, propagation and exit represents a atomic abstract detailed program behavior described in natural language. 
    - Flow trace should only include the necessary elements related to the edited statement for preventing vulnerability.
    - You should analyze the patch step by step, and provide the your thought process in detail. 
        Step 1: Confirm the domain of the edited statement 
        Step 2: Confirm the functionality of the edited statement 
        Step 3: Understand the edited statements which is the modified statements in the patch, which is for preventing vulnerability. \
            And analyze conditions and correspondding statements to prevent triggering vulnerabilities based on the information obtained from change of patch. \
            Analyse as meticulously as possible.
        Step 4: Find the entry of natural language abstract flow trace of the edited statements. \
            It must have appeared in the code both before and after the patch, and be the most relevant to the statement. \
            Entry should analyze the detailed functionality at an abstract level, without referencing specific identifier names.
        Step 5: Find the propagation flow of the trace of the edited statements between entry and exit . \
            There is 0-2 necessary normal element which is related to the safety element.  More than 2 is not allowed.\
            There is one or more safety element which is preventing triggering vulnerabilities based on Step 3. \
            The symbol '$safety element$' is appended to the correspondding element at the beginning of predicate. \
            The correspondding safety statement for preventing vulnerability should explain in very detail the specific safe code properties, specific identifier names, and operation employed to avoid vulnerabilities specifically.
            The propagation flow should follow the order of edited statement execution. 
        Step 6: Find the exit of the whole flow trace of the edited statements. \
            It must have appeared in the code both before and after the patch, and be the most relevant to the statement. \
            Exit should analyze the detailed functionality at an abstract level, without referencing specific identifier names.
    - Respond strictly in JSON format as shown below, with no additional commentary.
    - Each step must strictly adhere to the instructions provided above.
    - First, provide a detailed thought process, and then present the output.
    {{
        "domain":"the domain of the code",
        "functionality":"the functionality of the code",
        "condition":"flow trace for statements preventing triggering the vulnerability, in the form of a abstract control flow(python dict containing three parts: entry: sentence, propagation: python list with one or more in length, contains safety element. exit: sentence)"
    }}
    ### Code(vulnerable) Before the Fix:
    {vul}

    ### Code(normal) After the Fix:
    {fix}
    
    ### Patch:
    {patch}   

    ### Expected Summary:
    """
    else:
        prompt = f"""
        - Analyze a given patch written in C with a statement-level edit operation for fixing a security vulnerability. 
        - Provide detailed summary of the patch.
        - Additionally, consider the provided data flow context slice information which contains more precise context. And find the core necessary condition. But do not rely entirely on the data flow slice, cause it may contain some error.
        - Your summary should include three parts:
        1. The application domain of the edited statement.
        2. The functionality of the edited statement.
        3. The condition of preventing triggering the vulnerability. You should provide the condition in the form of a natural language abstract flow. \
            And it contain three parts: 1) The entry of the whole trace of the edited statement, that is direction of the program control flow after the edited statement execution is started and is related to the edited statement. \
                2) The propagation flow of the trace of the edited statement between entry and exit, in which one or more element is for preventing vulnerablity triggering operation. \
                3) the exit of the trace of the edited statement, that is direction of the program control flow after the edited statement execution is completed.
        - In the flow trace, every element in the trace should be a predicate at the finest granularity.
        - Each predicate of entry, propagation and exit represents a atomic abstract detailed program behavior described in natural language.
        - Flow trace should only include the necessary elements related to the edited statement for preventing vulnerability.
        - You should analyze the patch step by step, and provide the your thought process in detail. \
            Step 1: Confirm the domain of the edited statement 
            Step 2: Confirm the functionality of the edited statement 
            Step 3: Understand the edited statements which is the modified statements in the patch, which is for preventing vulnerability. \
                And analyze conditions and correspondding statements to prevent triggering vulnerabilities based on the information obtained from change of patch. \
                Analyse as meticulously as possible.
            Step 4: Find the entry of natural language abstract flow trace of the edited statements. \
                It must have appeared in the code both before and after the patch, and be the most relevant to the statement. \
                Entry should analyze the detailed functionality at an abstract level. You can not reference specific identifier names in entry!
            Step 5: Find the propagation flow of the trace of the edited statements between entry and exit . \
                There is 0-2 necessary normal element which is related to the safety element. More than 2 is not allowed. \
                There is one or more safety element which is preventing triggering vulnerabilities based on Step 3. \
                The symbol '$safety element$' is appended to the correspondding element at the beginning of predicate. \
                The correspondding safety statement for preventing vulnerability should explain in very detail the specific safe code properties, specific identifier names, and operation employed to avoid vulnerabilities specifically.
                The propagation flow should follow the order of edited statement execution. 
            Step 6: Find the exit of the whole flow trace of the edited statements. \
                It must have appeared in the code both before and after the patch, and be the most relevant to the statement. \
                Exit should analyze the detailed functionality at an abstract level. You can not reference specific identifier names in entry!
        - Each step must strictly adhere to the instructions provided above.
        - Respond strictly in JSON format as shown below, with no additional commentary.
        {{
            "domain":"the domain of the code",
            "functionality":"the functionality of the code",
            "condition":"flow trace for statements preventing triggering the vulnerability, in the form of a abstract control flow(python dict containing three parts: entry: sentence, propagation: python list with one or more in length, contains safety element. exit: sentence)"
        }}
        - First, provide a detailed thought process, and then present the output in JSON format.
        ### Code(vulnerable) Before the Fix:
        {vul}

        ### Code(normal) After the Fix:
        {fix}
        
        ### Patch:
        {patch}  
        
        ### Data Flow Context Slice:
        {slice_context}

        ### Expected Summary:
        """
    messages =[
            {"role": "system", "content": "You are an expert in C language security vulnerabilities"},
            {"role": "user", "content": prompt}
    ] 
    try:
        completion = client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            temperature=0
        )
        response = completion.choices[0].message.content
    except:
        traceback.print_exc()
        return '', '', '', '', '', '', ''
    # print(response)
    try:
        domain = json.loads(response)['domain']
        functionality = json.loads(response)['functionality']
        # condition = json.loads(response)['condition']
    except:
        try:
            response = re.search(r"```json(.*?)```", response, re.DOTALL).group(1)
            domain = json.loads(response)['domain']
            functionality = json.loads(response)['functionality']
        except:
            return '', '', '', '', '', '', ''
        # condition = json.loads(response)['condition']
    # print(response)
    # print("="*30)
    messages.append({"role": "assistant", "content": response})
    messages.append({"role": "user", "content": f"""
        - Based on the previous information, analyze the actions and effects of the patch, reflecting them through the transformation of conditions, and output in JSON format.
        - Analyze step by step, and provide the your thought process in detail. \
            Step 1: Understand the entry, propagation, and exit of the patched code based on the above information. \
            Step 2: Identify the elements that remain unchanged, need to be added, or need to be deleted based on Step 1. \
                - List all the modifications made in detail! \
                - If vulnerable code has operations that fixed code has not encountered, they should be added and marked as $vulnerable$ at the beginning of element description; \
                - If vulnerable code has the same operations as fixed code, they should be unchanged; \
                - If vulnerable code does not implement the security operations in fixed code, the operations in vulnerable code should be reversed inplace ,explained detailedly, and marked as $vulnerable$ at the beginning of element description, for example, '$safety element$:description ' --> '$vulnerable$:description ';\
                - The correspondding vulnerable statement for causing vulnerability should explain in very detail the specific safe code properties or operation employed to cause vulnerabilities specifically.
            Step 3: Define the vulnerable condition before the patch based on the information obtained from Step 1 ,Step 2.
            Step 4: Trim the conditions by removing some propagation elements that are entirely unrelated to the vulnerability.
        - Each step must strictly adhere to the instructions provided above.
        - Respond strictly in JSON format as shown below, with no additional commentary.
        - First, provide a detailed thought process, and then present the output in JSON format.
        {{
            "after_condition":"the new normal condition after patch, in the form of a abstract control flow(python dict containing three parts: entry: sentence, propagation: python list, exit: sentence)",
            "before_condition":"the root cause condition of triggering the vulnerability analysed before, in the form of a abstract control flow(python dict containing three parts: entry: sentence, propagation: python list, exit: sentence)"
        }}
        """})
    try:
        completion = client.chat.completions.create(
            model="deepseek-chat",
            messages=messages,
            temperature=0
        )
        response2 = completion.choices[0].message.content  
    except:
        traceback.print_exc()
        return '', '', '', '', '', '', ''
    
    try:
        fix_action= json.loads(response2)['after_condition']
        before_condition= json.loads(response2)['before_condition']
    except:
        try:
            response2 = re.search(r"```json(.*?)```", response2, re.DOTALL).group(1)
            fix_action= json.loads(response2)['after_condition']
            before_condition= json.loads(response2)['before_condition']
        except:
            traceback.print_exc()
            return '', '', '', '', '', '', ''
            # print(response2)
    
    # messages=[
    #     {"role": "system", "content": "You're a first order logic expert."},
    #     {"role": "user", "content": response2}
    #     ]
    # messages.append({"role": "user", "content": f"""
    #     ### Before Condition:
    #     {before_condition}
    #     ### After Condition:
    #     {fix_action}
        
    #     ### Instruction:
    #     - Based on the above before_condition and after_condition in json format, generate the corresponding simplified first-order logic formula.
    #     - We define entry as S, propagation as P1, P2, ..., Pn, and exit as C in the before_condition. And after_condition is a modification of before_condition, which can be seen as the process of negating, deleting, perserving, or adding some of the premises in before_condition.
    #     - If certain premises in after_condition differ from those in before_condition and cannot be represented through reversal or deletion, we update them to premise'.
    #     - Each logical premise includes a brief description that refers to a previously obtained condition.
    #     - The premises in a first-order logic formula should retain the trace order information.
    #     - Respond strictly in JSON format as shown below, with no additional commentary.
    #     - Analyse the first-order logic formula step by step and give the detailed step-by-step analysis.
    #         Step 1: Gain a deep understanding of the entry, propagation, and exit of both before_condition and after_condition. 
    #         Step 2: Define the logical premises of before_condition. Retain the trace order information. Retain the $triggering element$ mark in the corresponding element.
    #         Step 3: Find the changes or persevations from provided before_condition to provided after_condition. \
    #             Based on the semantic meaning and logical deviation of the sentence. \
    #                 Specially, identify the changes or persevations in the entry, the changes or persevations in the propagation, and the changes or persevations in the exit. \
    #                     And determine whether they are deletion, negation, persevation, or update. 
    #         Step 4: Define the logical premises of after_condition based on the information obtained from step 1,2,3. \
    #             First, find the premises that are the same as before_condition. \
    #                 Then, define new premises and determine their symbols for changed condition part, including negation (¬), updates (') and add. \
    #                     Deletions do not require assigning symbols since no new premises need to be defined.
    #         Step 5: Form first order logic formulas based on step 1,2,3,4.
    #     - First, provide a detailed thought process, and then present the output in JSON format.
    #     {{
    #         "premise_definition":"the natural language definition of each logical premise",
    #         "before_condition_formula":"first-order logic formula of the before_condition",
    #         "after_condition_formula":"first-order logic formula of the after_condition"
    #     }}
    #     """})    
    # completion = client.chat.completions.create(
    #     model="deepseek-chat",
    #     messages=messages,
    #     temperature=0
    # )
    # response3 = completion.choices[0].message.content 
    # try:
    #     before_condition_formula = json.loads(response3)['before_condition_formula']
    #     after_condition_formula = json.loads(response3)['after_condition_formula']
    #     premise_definition = json.loads(response3)['premise_definition']
    # except:
    #     response3 = re.search(r"```json(.*?)```", response3, re.DOTALL).group(1)
    #     before_condition_formula = json.loads(response3)['before_condition_formula']
    #     after_condition_formula = json.loads(response3)['after_condition_formula']
    #     premise_definition = json.loads(response3)['premise_definition']    
    
    # diff = get_diff(vul, fix)
    # print(diff)
    # print("-"*30)
    # print(response2)
    # print("-"*30)
    # print(response)
    # print("="*30)
    
    return domain, functionality, before_condition,fix_action, '', '', ''

def getFunction2(vul, fix, slice_context=None):  # focuss
    client = OpenAI(
        base_url="https://api.deepseek.com/v1",
        api_key="***********************************",
    )
    if slice_context is None or slice_context == 'None':
        prompt = f"""
    You are a C code security expert.
    ### Instruction:
    - Analyze a given patch written in C with a statement-level edit operation for fixing a security vulnerability. 
    - Provide a 70-word detailed summary of the location fixed in the code.
    - Your summary should include fix action and the root cause of the vulnerability, and CWE number.
    - the root cause should contain two insights within one "root_cause" json field: 1. the concrete cause of the vulnerability. 2. the abstract cause of the vulnerability. 
    - the CWE type number field should be filled with the CWE type number. 
    - Respond strictly in JSON format as shown below, with no additional commentary.
    {{
        "root_cause":"Analyze the causes of errors leading to vulnerabilities, connecting them to the broader task or context of the code while avoiding specific identifier names. Provide an explanation that identifies how the context contributes to the vulnerability",
        "fix_action":"Given the patch, briefly summarize how the vulnerability was fixed.",
        "CWE_type":"the CWE type number"
    }}
    ### Code(vulnerable) Before the Fix:
    {vul}

    ### Code(normal) After the Fix:
    {fix}

    ### Expected Output:
    """
    else:
        prompt = f"""
        ### Instruction:
        - Analyze a given patch written in C with a statement-level edit operation for fixing a security vulnerability. 
        - Provide a 80-word detailed summary of the location fixed in the code.
        - Additionally, consider the provided data flow context slice information to understand the more precise information of the vulnerability.
        - Your summary should include fix action and the root cause of the vulnerability, and CWE number.
        - the root cause should contain two insights within one "root_cause" json field: 1. the concrete cause of the vulnerability. 2. the abstract cause of the vulnerability. 
        - the CWE type number field should be filled with the CWE type number.
        - Respond strictly in JSON format as shown below, with no additional commentary.
        {{
            "root_cause":"Analyze the causes of errors leading to vulnerabilities, connecting them to the broader task or context of the code while avoiding specific identifier names. Provide an explanation that identifies how the context contributes to the vulnerability",
            "fix_action":"Given the patch, briefly summarize how the vulnerability was fixed, and migitated the vulnerability.",
            "CWE type":"the CWE type number"
        }}
        ### Code(vulnerable) Before the Fix:
        {vul}

        ### Code(normal) After the Fix:
        {fix}

        ### Data Flow Context Slice:
        {slice_context}

        ### Expected Output:
        """
    completion = client.chat.completions.create(
        model="deepseek-chat",
        messages=[
            {"role": "system", "content": "You are an expert in C language security vulnerabilities. "},
            {"role": "user", "content": prompt}
        ],
        temperature=0
    )
    response = completion.choices[0].message.content
    # print(response)
    try:
        functionality = json.loads(response)['fix_action']
        rootcause = json.loads(response)['root_cause']
    except:
        response = re.search(r"```json(.*?)```", response, re.DOTALL).group(1)
        functionality = json.loads(response)['fix_action']
        rootcause = json.loads(response)['root_cause']
        return functionality, rootcause
    return functionality, rootcause


def process_row(row, index):
    vul = row[0]
    fix = row[1]
    slice_context = row[2] if len(row) > 2 else None
    try:
        domain, functionality, condition,fix_action,before_condition_formula,after_condition_formula,premise_definition= getFunction3(vul, fix, slice_context)
    except Exception:
        traceback.print_exc()
        domain, functionality, condition,fix_action,before_condition_formula,after_condition_formula,premise_definition= '', '', '', '', '', '', ''
    return domain, functionality, condition,fix_action,before_condition_formula,after_condition_formula,premise_definition, index

def main2(file_path, store_path):

    # 读取原始数据
    with open(file_path, 'r') as f:
        reader = csv.reader(f)
        df = list(reader)

    # 如果存在之前的输出文件，读取它
    try:
        with open(store_path, 'r') as f:
            reader = csv.reader(f)
            existing_df = list(reader)
        # 如果现有文件列数不够，添加所需的列
        if len(existing_df[0]) < 11:
            for row in existing_df:
                row.extend([''] * (11 - len(row)))
        df = existing_df
    except FileNotFoundError:
        # 如果文件不存在，添加新列
        for row in df:
            row.extend([''] * (11 - len(row)))

    num_processes = 127
    with ProcessPoolExecutor(max_workers=num_processes) as executor:
        futures = []
        # 只处理未完成的行（通过检查domain列是否为空）
        for index, row in enumerate(df):
            if row[4].strip()=='':
                print(f"Processing row {index}")
                futures.append(executor.submit(process_row, row, index))
        
        print(f"Processing {len(futures)} remaining rows")
        
        # 使用tqdm显示进度
        for future in tqdm(as_completed(futures), total=len(futures)):
            try:
                domain, functionality, condition, fix_action, before_condition_formula, after_condition_formula, premise_definition, index = future.result()
                # 更新DataFrame中的值
                df[index][4] = domain
                df[index][5] = functionality
                df[index][6] = json.dumps(condition)
                df[index][7] = json.dumps(fix_action)
                df[index][8] = before_condition_formula
                df[index][9] = after_condition_formula
                df[index][10] = json.dumps(premise_definition)
                
                # 立即写入文件
                with open(store_path, 'w', newline='') as f:
                    writer = csv.writer(f)
                    writer.writerows(df)
                
            except Exception as e:
                print(f"Error processing row {index}: {e}")
                traceback.print_exc()
    print(f'Completed. Total rows: {len(df)}')

def main(file_path, store_path):
    df = pd.read_csv(file_path,header=None)
    df.insert(4, 'domain', '')
    df.insert(5, 'functionality', '')
    df.insert(6, 'condition', '')
    df.insert(7, 'fixaction', '')
    df.insert(8, 'before_condition_formula', '')
    df.insert(9, 'after_condition_formula', '')
    df.insert(10, 'premise_definition', '')
    
    num_processes = 63
    executor = ProcessPoolExecutor(max_workers=num_processes)
    futures = []
    for index, row in df.iterrows():
        print(f"Processing row {index}")
        futures.append(executor.submit(process_row, row, index))
    print(f"Processing {len(df)} rows")
    for future in tqdm(as_completed(futures), total=len(futures)):
        try:
            domain, functionality, condition, fix_action,before_condition_formula,after_condition_formula,premise_definition,index = future.result()
            df.iloc[index, 4] = domain
            df.iloc[index, 5] = functionality
            df.iloc[index, 6] = json.dumps(condition)
            df.iloc[index, 7] = json.dumps(fix_action)
            df.iloc[index, 8] = before_condition_formula
            df.iloc[index, 9] = after_condition_formula
            df.iloc[index, 10] = json.dumps(premise_definition)
        except Exception as e:
            traceback.print_exc()
            print(f"Error processing row {index}: {e}")
    executor.shutdown()
    output_file_path = store_path
    df.to_csv(output_file_path, index=False, header=False)
    print(f'row count: {len(df)}')


if __name__ == "__main__":
    main2('../datasets/sven_exp/sven_processed.csv',
         '../datasets/sven_exp/sven_processed_test.csv')
