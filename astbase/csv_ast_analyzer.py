#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
CSV AST Analyzer - 分析CSV文件中的代码编辑对并生成AST关系树形结构

该模块提供了一个外部接口，用于分析CSV文件中的代码编辑对，并生成展示编辑对之间AST关系的树形结构。
主要功能包括：
1. 读取CSV文件中的代码编辑对
2. 分析每个编辑对的AST变化模式
3. 识别编辑对之间的关系（依赖、可合并性等）
4. 构建并输出AST关系树形结构
"""

import csv
import os
import json
import logging
import pickle
import random
import traceback
import multiprocessing # Added for parallel processing
from collections import defaultdict, deque
from tqdm import tqdm
import networkx as nx
import matplotlib.pyplot as plt

from AST import GumTreeAstPair
import Pattern
from Hierarchical import HierarchicalNode, HierarchicalCluster, clusterSubset

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Helper function for parallel AST analysis
# This function must be at the top level of the module to be picklable by multiprocessing
def _global_process_single_edit_pair(args_tuple):
    """
    Processes a single edit pair to generate AST and patterns.
    Designed to be called by multiprocessing.Pool.
    """
    edit_pair_data, language = args_tuple
    idx, before_code, after_code = edit_pair_data
    try:
        ast_pair = GumTreeAstPair(before=before_code, after=after_code, idx=idx, language=language)
        patterns = Pattern.getEditPatterns(idx, ast_pair, llm=False)
        return idx, ast_pair, patterns, None  # Success: idx, ast_pair, patterns, error
    except Exception as e:
        # Return error information to be logged by the main process
        error_msg = f"Error processing edit pair {idx} in child process: {str(e)}\n{traceback.format_exc()}"
        return idx, None, None, error_msg


class EditRelationNode:
    """编辑关系树节点类，表示树形结构中的一个节点"""

    def __init__(self, edit_id, before_code, after_code, ast_pair=None, node_type="original"):
        """
        初始化编辑关系树节点

        Args:
            edit_id: 编辑对的唯一标识符
            before_code: 修改前的代码
            after_code: 修改后的代码
            ast_pair: GumTreeAstPair对象，表示AST分析结果
            node_type: 节点类型 ("original" 或 "merged")
        """
        self.edit_id = edit_id
        self.before_code = before_code
        self.after_code = after_code
        self.ast_pair = ast_pair
        self.node_type = node_type
        self.patterns = []  # 编辑模式
        self.children = []  # 子节点
        self.parent = None  # 父节点
        self.relation_type = None  # 与父节点的关系类型
        self.metadata = {}  # 额外元数据

    def add_child(self, child_node, relation_type=None):
        """
        添加子节点

        Args:
            child_node: 子节点对象
            relation_type: 关系类型，如"依赖"、"可合并"等
        """
        self.children.append(child_node)
        child_node.parent = self
        child_node.relation_type = relation_type

    def to_dict(self):
        """将节点转换为字典表示，用于JSON序列化"""
        result = {
            "edit_id": self.edit_id,
            "node_type": self.node_type,
            "before_code_snippet": self.before_code[:100] + "..." if len(self.before_code) > 100 else self.before_code,
            "after_code_snippet": self.after_code[:100] + "..." if len(self.after_code) > 100 else self.after_code,
            "patterns_present": bool(self.patterns),
            "relation_type": self.relation_type,
            "metadata": self.metadata,
            "children": [child.to_dict() for child in self.children]
        }
        return result

# Helper functions for parallel node relation analysis (Phase 2)
MIN_STRIPPED_PATTERN_NODES_FOR_RELATION = 6 # Global constant, as it was in the original method

def _standalone_analyze_ast_relation(ast_pair1_obj, ast_pair2_obj, patterns_list_for_ast1, patterns_list_for_ast2):
    """
    Analyzes the relationship between two AST pairs based on their patterns.
    This is a standalone version of the original _analyze_ast_relation method.
    """
    if ast_pair1_obj is None or ast_pair2_obj is None:
        return None # Should not happen if called correctly

    # Filter patterns based on node count
    def filter_patterns(patterns_list):
        if not patterns_list:
            return []
        filtered = []
        for pattern_item in patterns_list:
            try:
                stripped_before_ast = pattern_item.getBeforePattern()
                stripped_after_ast = pattern_item.getAfterPattern()
                # len() on GumTreeAst objects returns the number of nodes.
                total_stripped_nodes = len(stripped_before_ast) + len(stripped_after_ast)
                if total_stripped_nodes >= MIN_STRIPPED_PATTERN_NODES_FOR_RELATION:
                    filtered.append(pattern_item)
            except Exception: # Catch any error during pattern processing
                # logger.debug(f"Could not process pattern during filtering: {pattern_item}")
                continue # Skip problematic pattern
        return filtered

    effective_patterns1 = filter_patterns(patterns_list_for_ast1)
    effective_patterns2 = filter_patterns(patterns_list_for_ast2)

    if not effective_patterns1 or not effective_patterns2:
        return {"mergeable": False, "reason": "Insufficient valid patterns after filtering"}

    mergeable_found = False
    for p1_item in effective_patterns1:
        for p2_item in effective_patterns2:
            try:
                merged_pattern = p1_item.mergeEditPattern(p2_item)
                # Check for unbound holes or ambiguity
                if hasattr(merged_pattern, 'hasUnboundHoles') and merged_pattern.hasUnboundHoles():
                    continue
                if hasattr(merged_pattern, 'isAmbiguous') and merged_pattern.isAmbiguous():
                    continue
                
                mergeable_found = True
                break  # Found a mergeable pair, no need to check further for this p1_item
            except Exception:
                # logger.debug(f"Merging pattern failed between p1_item and p2_item")
                continue # Merge failed, try next pair
        if mergeable_found:
            break # Found a mergeable pair for ast_pair1 and ast_pair2

    return {"mergeable": mergeable_found}


def _global_process_single_node_pair_relation(args_tuple):
    """
    Processes a single pair of nodes to determine their relationship.
    Designed to be called by multiprocessing.Pool.
    """
    node1_id, node2_id, ast_pair1_obj, ast_pair2_obj, patterns1_list, patterns2_list = args_tuple
    try:
        relation_result = _standalone_analyze_ast_relation(ast_pair1_obj, ast_pair2_obj, patterns1_list, patterns2_list)
        return node1_id, node2_id, relation_result, None # Success
    except Exception as e:
        error_msg = f"Error analyzing relation between {node1_id} and {node2_id} in child process: {str(e)}\n{traceback.format_exc()}"
        return node1_id, node2_id, None, error_msg


class EditRelationTree:
    """编辑关系树类，表示编辑对之间的关系树形结构"""

    def __init__(self):
        """初始化编辑关系树"""
        self.root = None
        self.nodes = {}  # 存储所有节点，键为edit_id

    def add_node(self, edit_id, before_code, after_code, ast_pair=None):
        """
        添加节点

        Args:
            edit_id: 编辑对的唯一标识符
            before_code: 修改前的代码
            after_code: 修改后的代码
            ast_pair: GumTreeAstPair对象

        Returns:
            新创建的节点
        """
        node = EditRelationNode(edit_id, before_code, after_code, ast_pair)
        self.nodes[edit_id] = node
        return node

    def set_root(self, node):
        """设置根节点"""
        self.root = node

    def to_dict(self):
        """将树转换为字典表示，用于JSON序列化"""
        if not self.nodes:
            return {"nodes": []}

        result = {"nodes": []}

        # 添加所有节点，包括根节点和孤立节点
        for node_id, node in self.nodes.items():
            node_dict = node.to_dict()
            # 添加一个标志，表示这是否是根节点
            node_dict["is_root"] = (node == self.root)
            # 添加一个标志，表示这是否是孤立节点
            node_dict["is_isolated"] = (node.parent is None and node != self.root)
            result["nodes"].append(node_dict)

        return result

    def to_json(self, indent=2):
        """将树转换为JSON字符串"""
        return json.dumps(self.to_dict(), indent=indent, ensure_ascii=False)

    def save_json(self, file_path):
        """将树保存为JSON文件"""
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(self.to_json())

    def visualize(self, output_file=None):
        """
        可视化树形结构

        Args:
            output_file: 输出文件路径，如果为None则显示图形
        """
        G = nx.DiGraph()

        # 添加所有节点到图中
        for node_id, node in self.nodes.items():
            # 根节点
            if node == self.root:
                node_label = f"Edit {node.edit_id} (根节点)"
                G.add_node(node_label, label=f"Edit {node.edit_id}",
                          style="filled", fillcolor="lightgreen")
            # 孤立节点
            elif node.parent is None:
                node_label = f"Edit {node.edit_id} (孤立)"
                G.add_node(node_label, label=f"Edit {node.edit_id}",
                          style="filled", fillcolor="lightgray")
            # 普通节点
            else:
                node_label = f"Edit {node.edit_id}"
                G.add_node(node_label, label=f"Edit {node.edit_id}",
                          style="filled", fillcolor="lightblue")

            # 添加边（父子关系）
            if node.parent:
                parent_label = f"Edit {node.parent.edit_id}"
                if node.parent == self.root:
                    parent_label += " (根节点)"
                G.add_edge(parent_label, node_label, label=node.relation_type or "")

        if G.number_of_nodes() > 0:
            plt.figure(figsize=(12, 8))

            # 使用不同的布局算法，根据节点数量选择
            if G.number_of_nodes() < 10:
                pos = nx.spring_layout(G, seed=42)  # 小图使用弹簧布局
            else:
                pos = nx.kamada_kawai_layout(G)  # 大图使用Kamada-Kawai布局

            # 绘制根节点
            root_nodes = [n for n in G.nodes() if "根节点" in n]
            if root_nodes:
                nx.draw_networkx_nodes(G, pos,
                                      nodelist=root_nodes,
                                      node_color='lightgreen',
                                      node_size=2000)

            # 绘制普通节点
            normal_nodes = [n for n in G.nodes() if "根节点" not in n and "孤立" not in n]
            if normal_nodes:
                nx.draw_networkx_nodes(G, pos,
                                      nodelist=normal_nodes,
                                      node_color='lightblue',
                                      node_size=2000)

            # 绘制孤立节点
            isolated_nodes = [n for n in G.nodes() if "孤立" in n]
            if isolated_nodes:
                nx.draw_networkx_nodes(G, pos,
                                      nodelist=isolated_nodes,
                                      node_color='lightgray',
                                      node_size=2000)

            # 绘制边和标签
            nx.draw_networkx_edges(G, pos, arrows=True, arrowsize=20)
            nx.draw_networkx_labels(G, pos)

            # 绘制边标签
            edge_labels = {(u, v): d['label'] for u, v, d in G.edges(data=True) if d.get('label')}
            nx.draw_networkx_edge_labels(G, pos, edge_labels=edge_labels)

            plt.axis('off')  # 关闭坐标轴

            if output_file:
                plt.savefig(output_file)
            else:
                plt.show()


class CsvAstAnalyzer:
    """CSV AST分析器类，用于分析CSV文件中的代码编辑对并生成AST关系树形结构"""

    def __init__(self, language='c'):
        """
        初始化CSV AST分析器

        Args:
            language: 代码语言，默认为'c'
        """
        self.language = language
        self.edit_pairs = []  # 存储所有编辑对
        self.ast_pairs = {}  # 存储所有AST分析结果
        self.patterns = {}  # 存储所有编辑模式
        self.relation_tree = EditRelationTree()  # 关系树

    def load_csv(self, csv_file, before_col=0, after_col=1, has_header=False, delimiter=','):
        """
        从CSV文件加载代码编辑对

        Args:
            csv_file: CSV文件路径
            before_col: 修改前代码所在列索引
            after_col: 修改后代码所在列索引
            has_header: 是否有表头
            delimiter: 分隔符

        Returns:
            加载的编辑对数量
        """
        self.edit_pairs = []

        try:
            with open(csv_file, 'r', encoding='utf-8') as f:
                reader = csv.reader(f, delimiter=delimiter)

                if has_header:
                    next(reader)  # 跳过表头

                for i, row in enumerate(reader):
                    if len(row) > max(before_col, after_col):
                        before_code = row[before_col]
                        after_code = row[after_col]
                        self.edit_pairs.append((i, before_code, after_code))

            logger.info(f"从CSV文件加载了 {len(self.edit_pairs)} 个代码编辑对")
            return len(self.edit_pairs)

        except Exception as e:
            logger.error(f"加载CSV文件时出错: {str(e)}")
            raise

    def analyze_ast_pairs(self):
        """
        分析所有编辑对的AST (并行化版本)

        Returns:
            成功分析的编辑对数量
        """
        self.ast_pairs = {}
        self.patterns = {} # Ensure patterns are also re-initialized for the run
        successful_pairs = 0

        if not self.edit_pairs:
            logger.info("No edit pairs to analyze.")
            return 0

        # Prepare tasks for multiprocessing
        # Each task is a tuple: ( (idx, before_code, after_code), self.language )
        tasks_to_process = [
            ( (edit_id, before, after), self.language )
            for edit_id, before, after in self.edit_pairs
        ]

        # Determine number of processes
        cpu_cores = multiprocessing.cpu_count()
        num_processes = max(1, cpu_cores - 1 if cpu_cores > 1 else 1)
        num_processes = min(num_processes, len(tasks_to_process)) # Cap by number of tasks

        logger.info(f"Starting parallel AST analysis for {len(self.edit_pairs)} pairs using {num_processes} processes.")

        results_from_pool = []
        try:
            with multiprocessing.Pool(processes=num_processes) as pool:
                # Using imap_unordered for progress bar and potentially better memory management
                for result in tqdm(pool.imap_unordered(_global_process_single_edit_pair, tasks_to_process), total=len(tasks_to_process), desc="Analyzing ASTs in parallel"):
                    results_from_pool.append(result)
        except Exception as e:
            logger.error(f"Multiprocessing pool for AST analysis failed: {str(e)}\n{traceback.format_exc()}")
            # Depending on the error (e.g., pickling), many tasks might fail.
            # We'll still try to process any results that might have completed.

        for idx, ast_pair, patterns_list, error in results_from_pool:
            if error:
                logger.error(f"Failed to analyze edit pair {idx} (from child process): {error}")
            elif ast_pair is not None and patterns_list is not None:
                self.ast_pairs[idx] = ast_pair
                self.patterns[idx] = patterns_list # Store patterns associated with this idx
                successful_pairs += 1
            else:
                # This case might occur if error is None but data is still missing (should be rare)
                logger.warning(f"Received incomplete data for edit pair {idx} without an explicit error from child process.")

        logger.info(f"Successfully analyzed {successful_pairs}/{len(self.edit_pairs)} edit pairs' ASTs using parallel processing.")
        return successful_pairs

    def build_relation_tree(self):
        """
        构建编辑对之间的关系树

        Returns:
            构建的关系树对象
        """
        # 重置关系树
        self.relation_tree = EditRelationTree()

        # 创建所有节点
        for idx, (pair_idx, before_code, after_code) in enumerate(self.edit_pairs):
            ast_pair = self.ast_pairs.get(pair_idx)
            self.relation_tree.add_node(pair_idx, before_code, after_code, ast_pair)

        # 分析节点之间的关系
        self._analyze_node_relations()

        # 构建树形结构
        self._build_tree_structure()

        return self.relation_tree

    def _analyze_node_relations(self):
        """
        分析节点之间的关系 (并行化版本)
        """
        nodes_list = list(self.relation_tree.nodes.values())
        if len(nodes_list) < 2:
            logger.info("Not enough nodes to analyze relations for tree building.")
            return

        tasks_for_relation_analysis = []
        for i in range(len(nodes_list)):
            for j in range(len(nodes_list)):
                if i == j:
                    continue

                node1 = nodes_list[i]
                node2 = nodes_list[j]

                ast_pair1_obj = node1.ast_pair
                ast_pair2_obj = node2.ast_pair

                if ast_pair1_obj is None or ast_pair2_obj is None:
                    continue
                
                patterns1_list = self.patterns.get(ast_pair1_obj.idx) if hasattr(ast_pair1_obj, 'idx') else None
                patterns2_list = self.patterns.get(ast_pair2_obj.idx) if hasattr(ast_pair2_obj, 'idx') else None
                
                # Pass actual objects; relies on them being picklable.
                tasks_for_relation_analysis.append(
                    (node1.edit_id, node2.edit_id, ast_pair1_obj, ast_pair2_obj, patterns1_list, patterns2_list)
                )
        
        if not tasks_for_relation_analysis:
            logger.info("No valid pairs of nodes with ASTs/patterns found for relation analysis.")
            return

        cpu_cores = multiprocessing.cpu_count()
        num_processes = max(1, cpu_cores - 1 if cpu_cores > 1 else 1)
        num_processes = min(num_processes, len(tasks_for_relation_analysis)) # Cap by number of tasks

        logger.info(f"Starting parallel node relation analysis for {len(tasks_for_relation_analysis)} pairs using {num_processes} processes.")

        relation_results_from_pool = []
        try:
            with multiprocessing.Pool(processes=num_processes, maxtasksperchild=4) as pool:
                # Using imap_unordered for progress bar and potentially better memory management
                # maxtasksperchild=1 helps in releasing memory by replacing worker processes after each task.
                for result in tqdm(pool.imap_unordered(_global_process_single_node_pair_relation, tasks_for_relation_analysis), total=len(tasks_for_relation_analysis), desc="Analyzing node relations in parallel"):
                    relation_results_from_pool.append(result)
        except Exception as e:
            logger.error(f"Multiprocessing pool for node relation analysis failed: {str(e)}\n{traceback.format_exc()}")

        for node1_id, node2_id, relation_output, error in relation_results_from_pool:
            if error:
                logger.error(f"Failed to analyze relation between {node1_id} and {node2_id} (from child process): {error}")
            elif relation_output and relation_output.get("mergeable", False):
                if node1_id in self.relation_tree.nodes: # Check if node still exists
                    self.relation_tree.nodes[node1_id].metadata[f"relation_to_{node2_id}"] = relation_output
            # If not mergeable or relation_output is None, no metadata is added.

    # The original _analyze_ast_relation method is removed as its logic
    # has been moved to the global _standalone_analyze_ast_relation function.

    def _build_tree_structure(self):
        # 获取所有节点
        nodes = list(self.relation_tree.nodes.values())

        if not nodes:
            return

        # 准备编辑模式列表
        patterns_list = []
        pattern_to_node_map = []  # 用于跟踪模式和节点的对应关系

        for node in nodes:
            if node.ast_pair and node.edit_id in self.patterns:
                patterns = self.patterns[node.edit_id]
                if patterns:
                    # 选择第一个模式作为代表
                    pattern = patterns[0]
                    patterns_list.append(pattern)
                    pattern_to_node_map.append((pattern, node))

        if not patterns_list:
            # 如果没有有效的模式，使用简单的关系图方法
            self._build_tree_structure_simple()
            return

        # 创建一个关系图，用于表示节点之间的关系
        relation_graph = {}
        for node in nodes:
            relation_graph[node.edit_id] = []

        # 填充关系图
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes):
                if i == j:
                    continue

                # 检查节点1与节点2的关系
                relation_key = f"relation_to_{node2.edit_id}"
                if relation_key in node1.metadata:
                    relation = node1.metadata[relation_key]

                    # 检查是否可合并
                    if relation.get("mergeable", False):
                        relation_type = "可合并"
                        # 由于强度不再重要，我们只添加关系本身
                        relation_graph[node1.edit_id].append((node2.edit_id, relation_type))
                    # 如果不可合并，则不添加边到 relation_graph

        # 强度排序不再需要
        # for node_id in relation_graph:
        #     relation_graph[node_id].sort(key=lambda x: x[2], reverse=True)

        # 尝试使用层次聚类构建树
        try:
            # 构建层次聚类树
            hierarchical_nodes = self._build_hierarchical_tree(patterns_list)

            if hierarchical_nodes:
                # 将层次聚类树转换为关系树
                self._convert_hierarchical_to_relation_tree(hierarchical_nodes, pattern_to_node_map)
            else:
                # 如果层次聚类失败，使用简单的关系图方法
                self._build_tree_structure_from_graph(relation_graph)
        except Exception as e:
            logger.error(f"层次聚类构建树失败: {str(e)}")
            # 使用简单的关系图方法作为备选
            self._build_tree_structure_from_graph(relation_graph)

        # 处理孤立节点（没有关系的节点）
        isolated_nodes = []
        for node in nodes:
            if node.parent is None and node != self.relation_tree.root:
                isolated_nodes.append(node)

        # 如果有孤立节点，记录日志
        if isolated_nodes:
            logger.info(f"发现 {len(isolated_nodes)} 个孤立节点（无关系）")

    def _build_hierarchical_tree(self, patterns_list):
        """
        使用层次聚类算法构建树

        Args:
            patterns_list: 编辑模式列表

        Returns:
            层次节点列表
        """
        if not patterns_list:
            return []

        # 创建层次节点
        hierarchical_nodes = []
        pattern_to_node = {}  # 用于快速查找模式对应的节点

        # 为每个模式创建一个层次节点
        for pattern in patterns_list:
            node = HierarchicalNode()
            node.pattern = pattern
            hierarchical_nodes.append(node)
            pattern_to_node[id(pattern)] = node

        # 使用优先队列存储可能的合并对
        merge_candidates = []

        # 初始化所有可能的合并对
        for i in range(len(hierarchical_nodes)):
            for j in range(i + 1, len(hierarchical_nodes)):
                merge_candidates.append((i, j))

        # 记录已处理的节点
        processed_nodes = set()

        # 尝试合并模式
        while merge_candidates:
            i, j = merge_candidates.pop(0)

            # 检查节点是否已被处理
            if i in processed_nodes or j in processed_nodes:
                continue

            try:
                pattern1 = hierarchical_nodes[i].pattern
                pattern2 = hierarchical_nodes[j].pattern

                # 尝试合并模式
                merged_pattern = pattern1.mergeEditPattern(pattern2)

                # 检查合并后的模式是否有未绑定的洞或模糊性
                try:
                    if hasattr(merged_pattern, 'hasUnboundHoles') and merged_pattern.hasUnboundHoles():
                        continue
                    if hasattr(merged_pattern, 'isAmbiguous') and merged_pattern.isAmbiguous():
                        continue
                except:
                    # 如果检查失败，假设模式是有效的
                    pass

                # 创建新的层次节点
                new_node = HierarchicalNode()
                new_node.pattern = merged_pattern
                new_node.children.append(hierarchical_nodes[i])
                new_node.children.append(hierarchical_nodes[j])
                hierarchical_nodes[i].parent = new_node
                hierarchical_nodes[j].parent = new_node

                # 标记已处理的节点
                processed_nodes.add(i)
                processed_nodes.add(j)

                # 添加到层次节点列表
                hierarchical_nodes.append(new_node)
                new_index = len(hierarchical_nodes) - 1
                pattern_to_node[id(merged_pattern)] = new_node

                # 添加新节点与其他未处理节点的合并候选
                for k in range(len(hierarchical_nodes) - 1):
                    if k not in processed_nodes:
                        merge_candidates.append((k, new_index))
            except Exception as e:
                # 合并失败，继续尝试下一对
                continue

        # 过滤出未被合并的节点和合并后的根节点
        result_nodes = [node for i, node in enumerate(hierarchical_nodes) if i not in processed_nodes or node.parent is None]

        return result_nodes

    def _convert_hierarchical_to_relation_tree(self, hierarchical_nodes, pattern_to_node_map):
        """
        将层次聚类树转换为关系树

        Args:
            hierarchical_nodes: 层次节点列表
            pattern_to_node_map: 模式和节点的对应关系列表
        """
        # 找出根节点（没有父节点的节点）
        root_hierarchical_nodes = [node for node in hierarchical_nodes if node.parent is None]

        if not root_hierarchical_nodes:
            return

        # 选择第一个根节点作为关系树的根节点
        root_hierarchical_node = root_hierarchical_nodes[0]

        # 创建模式到节点的映射字典
        pattern_dict = {}
        for pattern, node in pattern_to_node_map:
            pattern_dict[id(pattern)] = node

        # 递归构建关系树
        def build_relation_tree(hierarchical_node, parent_relation_node=None):
            if hierarchical_node.pattern is None:
                logger.warning(f"HierarchicalNode found with None pattern. Skipping.")
                return

            pattern_id = id(hierarchical_node.pattern)
            relation_node = pattern_dict.get(pattern_id)

            if relation_node is None:  # This is a merged node
                merged_node_edit_id_raw = str(hierarchical_node.pattern.index)
                sane_id_part = merged_node_edit_id_raw.replace(' ', '').replace('[', '').replace(']', '').replace(',', '_').replace('(', '').replace(')', '')
                new_edit_id = f"merged_{sane_id_part}"
                
                # Ensure unique ID in case of unforeseen collisions
                temp_id = new_edit_id
                counter = 0
                while temp_id in self.relation_tree.nodes:
                    counter += 1
                    temp_id = f"{new_edit_id}_{counter}"
                new_edit_id = temp_id
                
                before_code_snippet = "Merged Node (No Before AST)"
                if hierarchical_node.pattern and hierarchical_node.pattern.getBeforePattern():
                    code = hierarchical_node.pattern.getBeforePattern().getCode()
                    before_code_snippet = f"Merged Before: {code[:50]}{'...' if len(code) > 50 else ''}"

                after_code_snippet = "Merged Node (No After AST)"
                if hierarchical_node.pattern and hierarchical_node.pattern.getAfterPattern():
                    code = hierarchical_node.pattern.getAfterPattern().getCode()
                    after_code_snippet = f"Merged After: {code[:50]}{'...' if len(code) > 50 else ''}"

                relation_node = EditRelationNode(
                    edit_id=new_edit_id,
                    before_code=before_code_snippet,
                    after_code=after_code_snippet,
                    ast_pair=None,
                    node_type="merged"
                )
                relation_node.patterns = [hierarchical_node.pattern]
                self.relation_tree.nodes[relation_node.edit_id] = relation_node
            
            # 如果有父节点，添加为其子节点
            if parent_relation_node:
                parent_relation_node.add_child(relation_node, "hierarchical_merge")
            elif hierarchical_node in root_hierarchical_nodes: # Ensure it's a designated root
                self.relation_tree.set_root(relation_node)

            # 递归处理子节点
            for child_hn in hierarchical_node.children:
                if child_hn is not None: # Children can be None from clustering logic
                    build_relation_tree(child_hn, relation_node)

        # 从根节点开始构建关系树
        build_relation_tree(root_hierarchical_node)

    def _build_tree_structure_from_graph(self, relation_graph):
        """
        从关系图构建树形结构

        Args:
            relation_graph: 关系图，键为节点ID，值为(相关节点ID, 关系类型, 关系强度)的列表
        """
        # 获取所有节点
        nodes = list(self.relation_tree.nodes.values())

        if not nodes:
            return

        # 找出关系最多的节点作为根节点
        root_id = max(relation_graph.keys(), key=lambda k: len(relation_graph[k]))
        root_node = self.relation_tree.nodes[root_id]
        self.relation_tree.set_root(root_node)

        # 已添加到树中的节点ID
        added_nodes = {root_id}

        # 使用广度优先搜索构建树
        queue = [root_id]
        while queue and len(added_nodes) < len(nodes):
            current_id = queue.pop(0)
            current_node = self.relation_tree.nodes[current_id]

            # 遍历当前节点的所有关系
            # 移除了强度 '_' 因为它不再被使用
            for related_id, relation_type in relation_graph[current_id]:
                if related_id not in added_nodes:
                    related_node = self.relation_tree.nodes[related_id]
                    current_node.add_child(related_node, relation_type)
                    added_nodes.add(related_id)
                    queue.append(related_id)

    def _build_tree_structure_simple(self):
        """使用简单的方法构建树形结构"""
        # 获取所有节点
        nodes = list(self.relation_tree.nodes.values())

        if not nodes:
            return

        # 创建一个关系图，用于表示节点之间的关系
        relation_graph = {}
        for node in nodes:
            relation_graph[node.edit_id] = []

        # 填充关系图
        for i, node1 in enumerate(nodes):
            for j, node2 in enumerate(nodes):
                if i == j:
                    continue

                # 检查节点1与节点2的关系
                relation_key = f"relation_to_{node2.edit_id}"
                if relation_key in node1.metadata:
                    relation = node1.metadata[relation_key]
                    relation_type = "无关系"

                    # 只有当模式可合并时，才添加关系
                    if relation.get("mergeable", False):
                        relation_type = "可合并" # 在简单构建模式下，如果可合并，则类型为"可合并"
                        # 由于强度不再重要，我们只添加关系本身
                        relation_graph[node1.edit_id].append((node2.edit_id, relation_type))
                    # 如果不可合并，则不添加边到 relation_graph

        # 使用关系图构建树
        self._build_tree_structure_from_graph(relation_graph)

    def save_results(self, output_dir):
        """
        保存分析结果

        Args:
            output_dir: 输出目录
        """
        os.makedirs(output_dir, exist_ok=True)

        # 保存关系树为JSON
        json_path = os.path.join(output_dir, "ast_relation_tree.json")
        self.relation_tree.save_json(json_path)
        logger.info(f"关系树已保存到 {json_path}")

        # 可视化关系树
        viz_path = os.path.join(output_dir, "ast_relation_tree.png")
        self.relation_tree.visualize(viz_path)
        logger.info(f"关系树可视化已保存到 {viz_path}")

        # 保存AST分析结果
        ast_path = os.path.join(output_dir, "ast_analysis.pkl")
        with open(ast_path, 'wb') as f:
            pickle.dump({
                'ast_pairs': self.ast_pairs,
                'patterns': self.patterns
            }, f)
        logger.info(f"AST分析结果已保存到 {ast_path}")


def analyze_csv_edits(csv_file, output_dir, language='c', before_col=0, after_col=1,
                     has_header=False, delimiter=','):
    """
    分析CSV文件中的代码编辑对并生成AST关系树形结构

    Args:
        csv_file: CSV文件路径
        output_dir: 输出目录
        language: 代码语言，默认为'c'
        before_col: 修改前代码所在列索引
        after_col: 修改后代码所在列索引
        has_header: 是否有表头
        delimiter: 分隔符

    Returns:
        关系树对象
    """
    analyzer = CsvAstAnalyzer(language)

    # 加载CSV文件
    analyzer.load_csv(csv_file, before_col, after_col, has_header, delimiter)

    # 分析AST
    analyzer.analyze_ast_pairs()

    # 构建关系树
    relation_tree = analyzer.build_relation_tree()

    # 保存结果
    analyzer.save_results(output_dir)

    return relation_tree


if __name__ == "__main__":
    import argparse

    parser = argparse.ArgumentParser(description="分析CSV文件中的代码编辑对并生成AST关系树形结构")
    parser.add_argument("csv_file", help="CSV文件路径")
    parser.add_argument("output_dir", help="输出目录")
    parser.add_argument("--language", default="c", help="代码语言，默认为'c'")
    parser.add_argument("--before-col", type=int, default=0, help="修改前代码所在列索引，默认为0")
    parser.add_argument("--after-col", type=int, default=1, help="修改后代码所在列索引，默认为1")
    parser.add_argument("--has-header", action="store_true", help="CSV文件是否有表头")
    parser.add_argument("--delimiter", default=",", help="CSV文件分隔符，默认为','")

    args = parser.parse_args()

    analyze_csv_edits(
        args.csv_file,
        args.output_dir,
        args.language,
        args.before_col,
        args.after_col,
        args.has_header,
        args.delimiter
    )

