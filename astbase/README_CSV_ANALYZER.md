# CSV AST 分析器

CSV AST 分析器是 astbase 模块的一个扩展功能，用于分析 CSV 文件中的代码编辑对，并生成展示编辑对之间 AST 关系的树形结构。

## 功能特点

- 读取 CSV 文件中的代码编辑对（修改前和修改后的代码片段）
- 分析每个编辑对的 AST 变化模式
- 识别编辑对之间的关系（依赖、可合并性等）
- 构建并输出 AST 关系树形结构
- 支持多种输出格式（JSON、可视化图像）

## 安装依赖

该功能依赖于以下第三方库：

```bash
pip install networkx matplotlib tqdm
```

## 使用方法

### 命令行使用

```bash
python csv_ast_analyzer.py input.csv output_dir --language c --before-col 0 --after-col 1 --has-header
```

参数说明：
- `input.csv`：输入的 CSV 文件，包含代码编辑对
- `output_dir`：输出目录，用于保存分析结果
- `--language`：代码语言，默认为 'c'
- `--before-col`：修改前代码所在列索引，默认为 0
- `--after-col`：修改后代码所在列索引，默认为 1
- `--has-header`：CSV 文件是否有表头
- `--delimiter`：CSV 文件分隔符，默认为 ','

### 在代码中使用

```python
from csv_ast_analyzer import analyze_csv_edits

# 分析 CSV 文件中的代码编辑对
relation_tree = analyze_csv_edits(
    csv_file="input.csv",
    output_dir="output",
    language='c',
    before_col=0,
    after_col=1,
    has_header=False
)

# 打印关系树结构
print(relation_tree.to_json())
```

### 示例脚本

可以运行示例脚本来测试功能：

```bash
python example_csv_analyzer.py
```

该脚本会创建一个示例 CSV 文件，包含几个简单的代码编辑对，然后分析这些编辑对并生成 AST 关系树形结构。

## 输出结果

分析完成后，会在指定的输出目录中生成以下文件：

1. `ast_relation_tree.json`：关系树的 JSON 表示
2. `ast_relation_tree.png`：关系树的可视化图像
3. `ast_analysis.pkl`：AST 分析结果的 pickle 文件

### 关系树 JSON 格式

```json
{
  "nodes": [
    {
      "edit_id": 0,
      "before_code_snippet": "...",
      "after_code_snippet": "...",
      "relation_type": null,
      "metadata": {},
      "children": [
        {
          "edit_id": 1,
          "before_code_snippet": "...",
          "after_code_snippet": "...",
          "relation_type": "相似",
          "metadata": {},
          "children": []
        }
      ],
      "is_root": true,
      "is_isolated": false
    },
    {
      "edit_id": 2,
      "before_code_snippet": "...",
      "after_code_snippet": "...",
      "relation_type": null,
      "metadata": {},
      "children": [],
      "is_root": false,
      "is_isolated": true
    }
  ]
}
```

## 关系类型

编辑对之间的关系类型包括：

- **相似**：两个编辑对具有相似的 AST 修改模式
- **依赖**：一个编辑对的修改依赖于另一个编辑对的修改
- **无明显关系**：两个编辑对之间没有明显的关系

## 注意事项

- 确保输入的 CSV 文件格式正确，每行包含修改前和修改后的代码片段
- 对于大型 CSV 文件，分析过程可能需要较长时间
- 可视化图像可能在编辑对数量较多时变得复杂，可以考虑只可视化部分关系
