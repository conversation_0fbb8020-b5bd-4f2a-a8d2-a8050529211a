import csv
import json
import os
import re # 新增导入
import difflib # 新增导入
import logging
import concurrent.futures
from models.OpenAI_API import generate_with_OpenAI_model
from utils.dataprocess import add_headers_and_ids_to_csv
from tqdm import tqdm  # 导入tqdm进度条库
from slicer.parse_joern_output2 import slice_code_from_string

# 新增辅助函数
def add_line_numbers_to_code(code_string: str) -> str:
    """Adds 1-based line numbers to each line of a code string."""
    if not code_string or not code_string.strip():
        return code_string # Return as is if empty or only whitespace
    
    lines = code_string.splitlines()
    numbered_lines = [f"{i+1} | {line}" for i, line in enumerate(lines)]
    return "\n".join(numbered_lines)

# 新增辅助函数: 生成 unified diff
def generate_unified_diff(code_before_str: str, code_after_str: str, fromfile: str = 'before', tofile: str = 'after', n: int = 3) -> str:
    """Generates a unified diff string between two code strings."""
    if not isinstance(code_before_str, str) or not isinstance(code_after_str, str):
        # Log or handle error appropriately if full logging is set up
        # For now, returning an error message string
        return "Error: Input code strings must be valid strings for diff generation."
    
    code_before_lines = code_before_str.splitlines(keepends=True)
    code_after_lines = code_after_str.splitlines(keepends=True)
    
    # Handle cases where one or both strings might be empty or only whitespace,
    # which could lead to no diff output or minimal diff output.
    # difflib.unified_diff handles empty lists gracefully, returning no diff lines.
    
    diff = difflib.unified_diff(code_before_lines, code_after_lines, fromfile=fromfile, tofile=tofile, n=n)
    return "".join(diff)

def parse_llm3_evaluation(llm_response_text: str) -> tuple[bool, str]:
    """
    解析LLM3的评估输出。
    预期格式:
    Result: [Correct/Incorrect]
    Feedback: [details]
    """
    is_correct = False
    feedback = "LLM3 response format error or missing, or feedback not extracted."
    
    # 尝试以不区分大小写的方式匹配 "Result:" 行
    result_match = re.search(r"Result:\s*(Correct|Incorrect)", llm_response_text, re.IGNORECASE)
    if result_match:
        is_correct = result_match.group(1).lower() == "correct"
    
    # 尝试以不区分大小写的方式匹配 "Feedback:" 行
    # 使用 DOTALL 使 . 匹配换行符，以便捕获多行反馈
    feedback_match = re.search(r"Feedback:\s*(.*)", llm_response_text, re.DOTALL | re.IGNORECASE)
    if feedback_match:
        feedback = feedback_match.group(1).strip()
    elif result_match: # 如果匹配到Result但没匹配到Feedback
        if is_correct:
            feedback = "LLM3 evaluated as Correct. Specific feedback line not found."
        else:
            feedback = "LLM3 evaluated as Incorrect. Specific feedback line not found."
    
    return is_correct, feedback

# New function to parse LLM1 strategy output
def parse_llm1_strategy(llm_response_text: str) -> dict:
    """
    Parses the LLM1b (enhanced strategy LLM) output.
    This includes precise source/sink/path, and the original strategy components.
    """
    results = {
        "vulnerability_source_precise": "N/A",
        "vulnerability_sink_precise": "N/A",
        "vulnerability_path_description": "N/A",
        "cwe_specific_strategy": "N/A",
        "pre_repair_state": "N/A",
        "abstract_strategy": "N/A",
        "concrete_strategy": "N/A",
        "post_repair_state": "N/A",
        "parsing_status": "failed"
    }

    # New fields (expected to be at the beginning of LLM1b's structured output)
    # Vulnerability Source (Precise)
    vs_match = re.search(r"Vulnerability Source \(Precise\):\s*(.*?)(?=\s*Vulnerability Sink \(Precise\):|\s*CWE-Specific Repair Strategy:|\Z)", llm_response_text, re.DOTALL | re.IGNORECASE)
    if vs_match and vs_match.group(1).strip():
        results["vulnerability_source_precise"] = vs_match.group(1).strip()

    # Vulnerability Sink (Precise)
    vk_match = re.search(r"Vulnerability Sink \(Precise\):\s*(.*?)(?=\s*Vulnerability Path Description:|\s*CWE-Specific Repair Strategy:|\Z)", llm_response_text, re.DOTALL | re.IGNORECASE)
    if vk_match and vk_match.group(1).strip():
        results["vulnerability_sink_precise"] = vk_match.group(1).strip()

    # Vulnerability Path Description
    vp_match = re.search(r"Vulnerability Path Description:\s*(.*?)(?=\s*CWE-Specific Repair Strategy:|\s*Pre-Repair State:|\Z)", llm_response_text, re.DOTALL | re.IGNORECASE)
    if vp_match and vp_match.group(1).strip():
        results["vulnerability_path_description"] = vp_match.group(1).strip()

    # Original fields (their relative order is maintained after the new fields)
    # CWE-Specific Repair Strategy
    cwe_specific_match = re.search(r"CWE-Specific Repair Strategy:\s*(.*?)(?=\s*Pre-Repair State:|\Z)", llm_response_text, re.DOTALL | re.IGNORECASE)
    if cwe_specific_match and cwe_specific_match.group(1).strip():
        results["cwe_specific_strategy"] = cwe_specific_match.group(1).strip()
    
    # Pre-Repair State
    pre_match = re.search(r"Pre-Repair State:\s*(.*?)(?=\s*Repair Strategy:|\Z)", llm_response_text, re.DOTALL | re.IGNORECASE)
    if pre_match and pre_match.group(1).strip():
        results["pre_repair_state"] = pre_match.group(1).strip()

    # Isolate the "Repair Strategy" block text
    strategy_block_text = None
    # The Repair Strategy block should be after Pre-Repair State and before Post-Repair State
    strategy_block_match = re.search(r"Repair Strategy:\s*(.*?)(?=\s*Post-Repair State:|\Z)", llm_response_text, re.DOTALL | re.IGNORECASE)
    if strategy_block_match:
        strategy_block_text = strategy_block_match.group(1).strip()

    if strategy_block_text:
        # Abstract Strategy (within the strategy block)
        abstract_match = re.search(r"Abstract:\s*(.*?)(?=\s*Concrete:|\Z)", strategy_block_text, re.DOTALL | re.IGNORECASE)
        if abstract_match and abstract_match.group(1).strip():
            results["abstract_strategy"] = abstract_match.group(1).strip()

        # Concrete Strategy (within the strategy block)
        concrete_match = re.search(r"Concrete:\s*(.*)", strategy_block_text, re.DOTALL | re.IGNORECASE) # Removed re.DOTALL temporarily if it's single line
        if concrete_match and concrete_match.group(1).strip():
            results["concrete_strategy"] = concrete_match.group(1).strip()
    else:
        # Fallback: Try to find Abstract and Concrete globally if main "Repair Strategy" block is not well-defined
        # These fallbacks might need adjustment if the new fields interfere.
        # For now, keeping them, but they are less likely to be hit if the main block is parsed.
        abstract_fallback_match = re.search(r"Repair Strategy:\s*Abstract:\s*(.*?)(?=\s*Concrete:|\s*Post-Repair State:|\Z)", llm_response_text, re.DOTALL | re.IGNORECASE)
        if abstract_fallback_match and abstract_fallback_match.group(1).strip():
            results["abstract_strategy"] = abstract_fallback_match.group(1).strip()

        concrete_fallback_match = re.search(r"Repair Strategy:(?:.|\n)*?Concrete:\s*(.*?)(?=\s*Post-Repair State:|\Z)", llm_response_text, re.DOTALL | re.IGNORECASE)
        if concrete_fallback_match and concrete_fallback_match.group(1).strip():
            results["concrete_strategy"] = concrete_fallback_match.group(1).strip()
            
    # Post-Repair State
    # This should be the last part of the structured response.
    post_match = re.search(r"Post-Repair State:\s*(.*)", llm_response_text, re.DOTALL | re.IGNORECASE)
    if post_match and post_match.group(1).strip():
        results["post_repair_state"] = post_match.group(1).strip()
    
    # Determine parsing_status based on successfully filled ORIGINAL 5 fields
    found_parts_count = 0
    if results["cwe_specific_strategy"] != "N/A": found_parts_count +=1
    if results["pre_repair_state"] != "N/A": found_parts_count +=1
    if results["abstract_strategy"] != "N/A": found_parts_count +=1
    if results["concrete_strategy"] != "N/A": found_parts_count +=1
    if results["post_repair_state"] != "N/A": found_parts_count +=1

    if found_parts_count == 5:
        results["parsing_status"] = "success"
    elif found_parts_count > 0:
        results["parsing_status"] = "partial"
    # The new fields (vulnerability_source_precise etc.) do not affect this "parsing_status"
    # as it's related to the core strategy needed by LLM2.
    
    return results

# Function to parse LLM1a's (candidate source/sink identification) output
def parse_llm1a_output(llm1a_response_text: str) -> dict:
    """
    Parses the LLM1a response to extract candidate sources, sinks, and their line numbers.
    Expects LLM1a to output a JSON string, ideally fenced with ```json ... ```.
    """
    parsed_data = {
        "sources": [],
        "sinks": [],
        "all_candidate_lines": []
    }
    
    if not llm1a_response_text or not llm1a_response_text.strip():
        return parsed_data

    try:
        json_str = ""
        # Prioritize fenced JSON block
        json_match_fenced = re.search(r"```json\s*([\s\S]*?)\s*```", llm1a_response_text, re.DOTALL)
        if json_match_fenced:
            json_str = json_match_fenced.group(1).strip()
        else:
            # If no fenced block, try to find a JSON object/array within the text
            # This looks for the first '{' or '[' and the last '}' or ']'
            first_char_json = -1
            last_char_json = -1

            first_brace = llm1a_response_text.find('{')
            first_bracket = llm1a_response_text.find('[')

            if first_brace != -1 and (first_bracket == -1 or first_brace < first_bracket):
                first_char_json = first_brace
            elif first_bracket != -1:
                first_char_json = first_bracket
            
            if first_char_json != -1:
                # Find corresponding closing char
                if llm1a_response_text[first_char_json] == '{':
                    last_char_json = llm1a_response_text.rfind('}')
                else: # '['
                    last_char_json = llm1a_response_text.rfind(']')
                
                if last_char_json > first_char_json:
                    json_str = llm1a_response_text[first_char_json : last_char_json + 1]
                else: # Fallback if no clear structure, try the whole string if it starts with { or [
                    if llm1a_response_text.strip().startswith(("{", "[")):
                         json_str = llm1a_response_text.strip()

        if not json_str:
            # If still no JSON string, it's likely not valid or not present as expected
            return parsed_data # Return default empty structure

        data = json.loads(json_str)
        
        sources = data.get("sources", [])
        sinks = data.get("sinks", [])
        all_lines_set = set()

        if isinstance(sources, list):
            for item in sources:
                if isinstance(item, dict) and "name" in item and isinstance(item.get("lines"), list):
                    valid_lines = [line for line in item["lines"] if isinstance(line, int) and line > 0]
                    if valid_lines: # Only add if there are valid lines
                        parsed_data["sources"].append({"name": str(item["name"]), "lines": valid_lines})
                        for line in valid_lines:
                            all_lines_set.add(line)
        
        if isinstance(sinks, list):
            for item in sinks:
                if isinstance(item, dict) and "name" in item and isinstance(item.get("lines"), list):
                    valid_lines = [line for line in item["lines"] if isinstance(line, int) and line > 0]
                    if valid_lines: # Only add if there are valid lines
                        parsed_data["sinks"].append({"name": str(item["name"]), "lines": valid_lines})
                        for line in valid_lines:
                            all_lines_set.add(line)
                            
        parsed_data["all_candidate_lines"] = sorted(list(all_lines_set))

    except json.JSONDecodeError:
        # Add logging here if verbose mode is on for debugging LLM output
        # print(f"DEBUG: LLM1a output JSON parsing failed. Raw output snippet: {llm1a_response_text[:200]}")
        pass # Return default parsed_data, error will be evident by empty fields
    except Exception:
        # Catch any other error during parsing
        # print(f"DEBUG: An unexpected error occurred during LLM1a output parsing. Raw output snippet: {llm1a_response_text[:200]}")
        pass # Return default parsed_data
        
    return parsed_data

# Helper function to process a single row
def _process_row(row_tuple, openai_api_params, max_iterations, logger):
    original_row_index, row = row_tuple
    logger.info(f"--- 开始处理行 {original_row_index + 1} ---")
    current_row_failed = False
    try:
        code_before = row['code_before']
        code_after = row['code_after'] # Ground truth
        cwe_type = row['cwe_type']
        cve_id = row['cve_id']

        if not code_before.strip() or not code_after.strip():
            logger.warning(f"行 {original_row_index + 1}: 'code_before' 或 'code_after' 为空，跳过此行。")
            return {
                "original_row_index": original_row_index + 1, "error": "Empty code_before or code_after",
                "cwe_type": cwe_type, "cve_id": cve_id, "code_before": code_before, "code_after_ground_truth": code_after,
                "status": "failed"
            }

        logger.info(f"行 {original_row_index + 1}: 步骤 A: 执行初始代码切片 (code_before)...")
        # try:
        #     changes_result = process_code_changes(code_before, code_after)
        #     sliced_old_initial = changes_result[0] # 获取旧代码切片
        #     # sliced_new_initial 不再需要
        # except IndexError:
        #     logger.error(f"行 {original_row_index + 1}: process_code_changes 返回值不足两个元素。")
        #     sliced_old_initial = "Error: Failed to get old slice from process_code_changes"
        # except Exception as e_pc:
        #     logger.error(f"行 {original_row_index + 1}: process_code_changes 执行出错: {e_pc}")
        #     sliced_old_initial = f"Error: process_code_changes failed: {e_pc}"

        # logger.info(f"行 {original_row_index + 1}: 旧代码初始切片长度: {len(sliced_old_initial.splitlines()) if isinstance(sliced_old_initial, str) else 'N/A'}")
        # logger.info(f"行 {original_row_index + 1}: 新代码初始切片长度: {len(sliced_new_initial.splitlines())}") # 已移除

        # 生成 ground truth unified diff
        unified_diff_ground_truth = generate_unified_diff(code_before, code_after, fromfile=f"{cve_id}_before.txt", tofile=f"{cve_id}_after_ground_truth.txt")
        logger.info(f"行 {original_row_index + 1}: Ground Truth Unified Diff (预览): {unified_diff_ground_truth[:200]}...")

        logger.info(f"行 {original_row_index + 1}: 阶段 1 (LLM1a): 初步识别候选Source/Sink...")
        code_before_numbered = add_line_numbers_to_code(code_before)
        # code_after_numbered 不再直接用于 LLM1a 的 prompt，但 LLM1b 会使用 unified_diff_ground_truth
        # 为了 LLM1a prompt 的 Patched Code (Reference) 部分，我们仍然需要一个带行号的 code_after
        # code_after_numbered_for_llm1a_ref = add_line_numbers_to_code(code_after)


        prompt_llm1a = f"""Analyze the Original Vulnerable Code and its Patched version below.
CWE Type: {cwe_type}
Original Vulnerable Code:
```
{code_before_numbered}
```
Patched Code (unified diff):
```
{unified_diff_ground_truth}
```
Task: Identify potential **Sources** (where tainted data originates or a vulnerability condition starts) and **Sinks** (where the tainted data is used unsafely or the vulnerability manifests) in the `Original Vulnerable Code` related to the `{cwe_type}` vulnerability.
When reporting line numbers, use the line numbers provided in the `Original Vulnerable Code` block above.
For each Source and Sink, list the relevant variable or function names and all specific line numbers in the `Original Vulnerable Code` where they appear.
The variables and functions included in a source/sink should be as concise as possible while still clearly demonstrating the root cause of the vulnerability.

First, carefully reason the vulnerability step by strp, then, Output STRICTLY in the following JSON format:
```json
{{
  "sources": [
    {{"name": "variable_or_function_name", "lines": [line_num1]}},
    ...
  ],
  "sinks": [
    {{"name": "variable_or_function_name", "lines": [line_num2]}},
    ...
  ]
}}
```
If no specific sources or sinks can be identified, output an empty list for "sources" and/or "sinks".
"""
        llm1a_api_params = {"n": 1}
        if openai_api_params:
            llm1a_api_params.update(openai_api_params)
        if "stop" in llm1a_api_params and isinstance(llm1a_api_params["stop"], str):
            llm1a_api_params["stop"] = [llm1a_api_params["stop"]]
        llm1a_api_params["prompt"] = prompt_llm1a
        
        llm_response_llm1a_list = generate_with_OpenAI_model(**llm1a_api_params)

        if llm_response_llm1a_list and isinstance(llm_response_llm1a_list, list) and llm_response_llm1a_list[0]:
            llm1a_raw_output = llm_response_llm1a_list[0].strip()
            logger.info(f"行 {original_row_index + 1}: LLM1a 原始输出 (预览): {llm1a_raw_output[:150]}...")
            llm1a_parsed_candidates = parse_llm1a_output(llm1a_raw_output)
            candidate_line_numbers_for_slice = llm1a_parsed_candidates.get("all_candidate_lines", [])
        else:
            logger.warning(f"行 {original_row_index + 1}: LLM1a 未能生成候选source/sink。")
            llm1a_raw_output = "Error or empty response from LLM1a"

        # Phase 2: Generate focused code slice
        if candidate_line_numbers_for_slice:
            logger.info(f"行 {original_row_index + 1}: 阶段 2: 基于LLM1a识别的行号 {candidate_line_numbers_for_slice} 生成聚焦代码切片...")
            try:
                # Ensure slice_code_from_string is available (imported with placeholder if needed)
                slice_result, _ = slice_code_from_string(
                    source_code=code_before,
                    target_lines=candidate_line_numbers_for_slice,
                    slice_type="combined",
                    data_flow_only=True,
                    verbose=False
                )
                focused_vuln_slice_before = slice_result if slice_result else "Slice generation returned empty."
                logger.info(f"行 {original_row_index + 1}: 聚焦代码切片 (预览): {focused_vuln_slice_before[:150]}...")
            except NameError: # slice_code_from_string might not be defined if import failed badly
                logger.error(f"行 {original_row_index + 1}: slice_code_from_string 函数未定义，无法生成聚焦切片。")
                focused_vuln_slice_before = "Error: slice_code_from_string not defined."
            except Exception as e_slice:
                logger.error(f"行 {original_row_index + 1}: 生成聚焦代码切片时发生错误: {e_slice}")
                focused_vuln_slice_before = f"Error during focused slice generation: {e_slice}"
        else:
            logger.info(f"行 {original_row_index + 1}: LLM1a未提供候选行号，跳过聚焦代码切片生成。")
            focused_vuln_slice_before = "No candidate lines from LLM1a for focused slice."

        current_iteration = 0
        llm1_feedback_for_next_iteration = ""
        
        llm1_strategy_history = []
        llm2_repaired_code_history = []
        llm3_evaluation_history = []

        final_llm1_strategy_raw = "N/A"
        final_llm1_cwe_specific_strategy = "N/A"
        final_llm1_pre_repair_state = "N/A"
        final_llm1_abstract_strategy = "N/A"
        final_llm1_concrete_strategy = "N/A"
        final_llm1_post_repair_state = "N/A"
        final_llm1_parsing_status = "N/A"

        # New initializations for LLM1a, focused slices, and precise analysis
        # llm1a_raw_output = "N/A"
        # llm1a_parsed_candidates = {"sources": [], "sinks": [], "all_candidate_lines": []}
        # candidate_line_numbers_for_slice = []
        # focused_vuln_slice_before = "N/A"
        final_llm1_precise_source = "N/A"
        final_llm1_precise_sink = "N/A"
        final_llm1_vulnerability_path = "N/A"
        
        final_llm2_repaired_code = "N/A"
        final_llm3_evaluation_correct = False
        final_llm3_feedback = "N/A"

        current_api_params_row = {"n": 1} # Make a copy for this row
        if openai_api_params:
            current_api_params_row.update(openai_api_params)
        if "stop" in current_api_params_row and isinstance(current_api_params_row["stop"], str):
            current_api_params_row["stop"] = [current_api_params_row["stop"]]
        
        while current_iteration <= max_iterations:
            logger.info(f"行 {original_row_index + 1}: --- 开始迭代 {current_iteration + 1} / {max_iterations + 1} ---")

            logger.info(f"行 {original_row_index + 1}: 阶段 3 (LLM1b): 精细分析与策略生成...")
            if current_iteration == 0:
                prompt_llm1 = f"""Analyze the following vulnerability fix to understand the vulnerability, its root cause, precise source/sink/path, the repair strategy, and the state after repair.
When reporting line numbers for Source and Sink, refer to the line numbers in the 'Original Vulnerable (Before Fix) Code'.

CWE Type: {cwe_type}
Original Vulnerable (Before Fix) Code:
```
{code_before_numbered}
```
Unified Diff of Patched Code:
```diff
{unified_diff_ground_truth}
```
Focused Code Slice:
```
{focused_vuln_slice_before}
```
Based on ALL the provided information, provide the following components. Ensure your descriptions are abstract where specified for strategies and states. For precise identification of source, sink, and path, you MUST use the line numbers as they appear in the 'Original Vulnerable (Before Fix) Code' block ONLY. Do NOT use line numbers from any other code snippets or slices provided (e.g., 'Original (Before Fix) Code Slice', 'Focused Code Slice'), as their line numbering is internal to those snippets and not relevant for the final precise location reporting:
1.  **Vulnerability Source (Precise)**:
    *   Identify the precise source of the vulnerability in the 'Original Vulnerable Code'.
2.  **Vulnerability Sink (Precise)**:
    *   Identify the precise sink of the vulnerability in the 'Original Vulnerable Code'.
3.  **Vulnerability Path Description**:
    *   Describe the path or conditions from the source to the sink that constitute the vulnerability.
4.  **Pre-Repair State (Root Cause of Vulnerability)**:
    *   Describe the state of the program *before* the repair, focusing on the abstract root cause of the vulnerability related to the source/sink and Path Description.
5.  **CWE-Specific Repair Strategy**:
    *   Based on your knowledge of CWE Type '{cwe_type}',the vulnerability fix above and the root cause, describe a specific strategy for repairing the vulnerability tailored to this vulnerability type.
6.  **Repair Strategy**:
    *   **Abstract Repair Strategy**: Describe the general, high-level and step-by-step approach to patch the vulnerability as observed from the provided before/after code and the root cause. This description should NOT include specific code statements or identifiers from the *original vulnerable code* unless illustrating a general pattern. This strategy must be precise enough for repairing the vulnerability in the conceptual sense.
    *   **Concrete Repair Strategy**: Describe the specific, actionable steps to transform the 'Original Vulnerable Code' into a correctly patched version. This strategy must be precise enough for developers to apply.
7.  **Post-Repair State**:
    *   Describe the state of the program *after* the repair, focusing on how the vulnerability is resolved in an abstract manner related to its root cause.

Output the analysis STRICTLY in the following format:
Vulnerability Source (Precise):
[Your precise source identification]
Vulnerability Sink (Precise):
[Your precise sink identification]
Vulnerability Path Description:
[Your vulnerability path description]
CWE-Specific Repair Strategy:
[Your CWE-specific repair strategy]
Pre-Repair State:
[Your analysis of the pre-repair state and root cause]
Repair Strategy:
  Abstract: [Your abstract repair strategy based on observed changes]
  Concrete: [Your concrete repair strategy based on observed changes]
Post-Repair State:
[Your analysis of the post-repair state]"""
            else:
                prompt_llm1 = f"""Re-analyze the code based on previous feedback.
CWE Type: {cwe_type}
Original Vulnerable (Before Fix) Code:
```
{code_before_numbered}
```
Unified Diff of Patched Code (Reference, against Original Vulnerable Code):
```diff
{unified_diff_ground_truth}
```
Focused Code Slice based on LLM1a Candidates (from Original Vulnerable Code, line numbers refer to original):
```
{focused_vuln_slice_before}
```
A previous attempt to generate a repair strategy and apply it resulted in the following feedback from an evaluator:
"{llm1_feedback_for_next_iteration}"

!!!Important: Considering this feedback, please provide a revised and improved analysis with the following components. Ensure your descriptions are abstract where specified for strategies and states. For precise identification of source, sink, and path, you MUST use the line numbers as they appear in the 'Original Vulnerable (Before Fix) Code' block ONLY. Do NOT use line numbers from any other code snippets or slices provided (e.g., 'Original (Before Fix) Code Slice', 'Focused Code Slice'), as their line numbering is internal to those snippets and not relevant for the final precise location reporting:
1.  **Vulnerability Source (Precise)**:
    *   Re-evaluate and identify the precise source of the vulnerability.
2.  **Vulnerability Sink (Precise)**:
    *   Re-evaluate and identify the precise sink of the vulnerability.
3.  **Vulnerability Path Description**:
    *   Re-evaluate and describe the vulnerability path.
4.  **CWE-Specific Repair Strategy**:
    *   Re-evaluate and refine this based on feedback and all available context.
5.  **Pre-Repair State (Root Cause of Vulnerability)**:
    *   Re-evaluate and describe the pre-repair state.
6.  **Repair Strategy (General, based on observed changes)**:
    *   **Abstract Repair Strategy**: Re-evaluate and describe.
    *   **Concrete Repair Strategy**: Re-evaluate and describe.
7.  **Post-Repair State**:
    *   Re-evaluate and describe the post-repair state.

Output the revised analysis STRICTLY in the following format:
Vulnerability Source (Precise):
[Your revised precise source identification]
Vulnerability Sink (Precise):
[Your revised precise sink identification]
Vulnerability Path Description:
[Your revised vulnerability path description]
CWE-Specific Repair Strategy:
[Your revised CWE-specific repair strategy]
Pre-Repair State:
[Your revised analysis of the pre-repair state and root cause]
Repair Strategy:
  Abstract: [Your revised abstract repair strategy based on observed changes]
  Concrete: [Your revised concrete repair strategy based on observed changes]
Post-Repair State:
[Your revised analysis of the post-repair state]"""
            
            current_api_params_row["prompt"] = prompt_llm1
            llm_response_llm1 = generate_with_OpenAI_model(**current_api_params_row)
            current_fix_strategy = ""
            if llm_response_llm1 and isinstance(llm_response_llm1, list) and llm_response_llm1[0]:
                current_fix_strategy = llm_response_llm1[0].strip()
                logger.info(f"行 {original_row_index + 1}: LLM1 生成的策略 (预览): {current_fix_strategy[:100]}...")
            else:
                logger.warning(f"行 {original_row_index + 1}: LLM1 未能生成修复策略。")
                current_fix_strategy = "Error or empty response from LLM1"
                llm1_strategy_history.append(current_fix_strategy)
                current_row_failed = True; break

            parsed_llm1_strategy_parts = parse_llm1_strategy(current_fix_strategy)
            llm1_strategy_history.append(parsed_llm1_strategy_parts)
            
            final_llm1_strategy_raw = current_fix_strategy # This is LLM1b's raw output
            final_llm1_precise_source = parsed_llm1_strategy_parts.get("vulnerability_source_precise", "N/A")
            final_llm1_precise_sink = parsed_llm1_strategy_parts.get("vulnerability_sink_precise", "N/A")
            final_llm1_vulnerability_path = parsed_llm1_strategy_parts.get("vulnerability_path_description", "N/A")
            final_llm1_cwe_specific_strategy = parsed_llm1_strategy_parts.get("cwe_specific_strategy", "N/A")
            final_llm1_pre_repair_state = parsed_llm1_strategy_parts.get("pre_repair_state", "N/A")
            final_llm1_abstract_strategy = parsed_llm1_strategy_parts.get("abstract_strategy", "N/A")
            final_llm1_concrete_strategy = parsed_llm1_strategy_parts.get("concrete_strategy", "N/A")
            final_llm1_post_repair_state = parsed_llm1_strategy_parts.get("post_repair_state", "N/A")
            final_llm1_parsing_status = parsed_llm1_strategy_parts.get("parsing_status", "failed")
            
            logger.info(f"行 {original_row_index + 1}: LLM2: 应用策略修复代码...")
            prompt_llm2 = f"""CWE Type: {cwe_type}
Original Vulnerable Code:
```
{code_before}
```
You are tasked to repair the 'Original Vulnerable Code'.
Use the following strategies derived from an analysis of the vulnerability and a reference patch:
CWE Number: {cwe_type}
CWE-Specific Repair Strategy:
{parsed_llm1_strategy_parts['cwe_specific_strategy']}
Abstract Repair Strategy:
{parsed_llm1_strategy_parts['abstract_strategy']}
Concrete Repair Strategy:
{parsed_llm1_strategy_parts['concrete_strategy']}
Based on the "Abstract Repair Strategy" and "Concrete Repair Strategy", generate a unified diff patch to repair the vulnerability in the 'Original Vulnerable Code'.
Concentrate on the vulnerable section(s); avoid modifying any non-vulnerable or unrelated areas.

Output Format:
Respond STRICTLY with a single JSON object containing the following keys:
- "patch_diff": A string containing the unified diff patch for fixing the 'Original Vulnerable Code'. This should be in standard unified diff format showing exactly what lines to change.
- "repair_strategy": A concise string describing the strategy or approach taken to repair the vulnerability.

Example JSON Output:
{{
  "repair_strategy": "Input validation was added to sanitize user-provided data before buffer operations.",
  "patch_diff": "@@ -10,3 +10,6 @@\\n     char buffer[100];\\n-    strcpy(buffer, input);\\n+    if (strlen(input) < sizeof(buffer)) {{\\n+        strcpy(buffer, input);\\n+    }}\\n     return buffer;"
}}

Important:
- The "patch_diff" should be a valid unified diff that can be applied to the original vulnerable code
- Focus on the minimal changes needed to fix the vulnerability
- Ensure the patch maintains the original code's functionality while addressing the security issue
- Do not include any explanations, preamble, or markdown formatting around the JSON object
- The patch should be applicable using standard patch tools"""
            current_api_params_row["prompt"] = prompt_llm2
            llm_response_llm2 = generate_with_OpenAI_model(**current_api_params_row)
            patch_diff_by_llm2 = ""
            repair_strategy_llm2 = ""
            if llm_response_llm2 and isinstance(llm_response_llm2, list) and llm_response_llm2[0]:
                llm2_raw_output = llm_response_llm2[0].strip()
                try:
                    # Attempt to remove markdown backticks if LLM still wraps JSON in them
                    processed_text = llm2_raw_output
                    if processed_text.startswith("```json"):
                        match = re.search(r"```json\s*([\s\S]*?)\s*```$", processed_text, re.DOTALL)
                        if match: processed_text = match.group(1).strip()
                    elif processed_text.startswith("```"): # General markdown block
                        match = re.search(r"```\s*([\s\S]*?)\s*```$", processed_text, re.DOTALL)
                        if match: processed_text = match.group(1).strip()

                    parsed_json = json.loads(processed_text)
                    
                    patch_diff_by_llm2 = parsed_json.get("patch_diff", "")
                    repair_strategy_llm2 = parsed_json.get("repair_strategy", "")

                    # Validate types and presence
                    if not isinstance(patch_diff_by_llm2, str) or not patch_diff_by_llm2.strip():
                        logger.warning(f"行 {original_row_index + 1}: LLM2 response JSON 'patch_diff' is missing, not a string, or empty. Response: '{llm2_raw_output[:200]}...'")
                        patch_diff_by_llm2 = f"Error: LLM2 failed to generate valid patch_diff. Raw output: {llm2_raw_output[:200]}..."
                        llm2_repaired_code_history.append(patch_diff_by_llm2)
                        current_row_failed = True; break
                    if not isinstance(repair_strategy_llm2, str) or not repair_strategy_llm2.strip():
                        logger.warning(f"行 {original_row_index + 1}: LLM2 response JSON 'repair_strategy' is missing, not a string, or empty. Response: '{llm2_raw_output[:200]}...'")
                        repair_strategy_llm2 = "Error: repair_strategy not provided by LLM2"
                    
                    logger.info(f"行 {original_row_index + 1}: LLM2 生成的修复补丁 (Patch Diff 预览): {patch_diff_by_llm2[:200]}...")
                
                except json.JSONDecodeError:
                    logger.warning(f"行 {original_row_index + 1}: Failed to decode LLM2 response as JSON. Response: '{llm2_raw_output[:200]}...'")
                    patch_diff_by_llm2 = f"Error: JSON decode failed. Raw output: {llm2_raw_output[:200]}..."
                    llm2_repaired_code_history.append(patch_diff_by_llm2)
                    current_row_failed = True; break
                except Exception as e:
                    logger.warning(f"行 {original_row_index + 1}: Unexpected error processing LLM2 response: {e}. Response: '{llm2_raw_output[:200]}...'")
                    patch_diff_by_llm2 = f"Error: Processing failed. Raw output: {llm2_raw_output[:200]}..."
                    llm2_repaired_code_history.append(patch_diff_by_llm2)
                    current_row_failed = True; break
            else:
                logger.warning(f"行 {original_row_index + 1}: LLM2 未能生成修复代码。")
                patch_diff_by_llm2 = "Error or empty response from LLM2"
                llm2_repaired_code_history.append(patch_diff_by_llm2)
                current_row_failed = True; break
            
            llm2_repaired_code_history.append({
                "patch_diff": patch_diff_by_llm2,
                "repair_strategy": repair_strategy_llm2
            })
            final_llm2_repaired_code = patch_diff_by_llm2

            logger.info(f"行 {original_row_index + 1}: LLM3: 评估修复结果...")

            prompt_llm3 = f"""CWE Type: {cwe_type}
            Original Vulnerable Code:
            ```
            {code_before}
            ```
            Generated Patch (Unified Diff by LLM2):
            ```diff
            {patch_diff_by_llm2}
            ```
            Reference Ground Truth Patch (Unified Diff):
            ```diff
            {unified_diff_ground_truth}
            ```
            Task: Evaluate if the 'Generated Patch (Unified Diff by LLM2)' is a correct and suitable fix for the vulnerability in the 'Original Vulnerable Code' when compared against the 'Reference Ground Truth Patch (Unified Diff)'.

Consider the following:
1. Does the 'Generated Patch' successfully apply the core idea of the fix present in the 'Reference Ground Truth Patch'?
2. Does it address the vulnerability implied by the CWE type?
3. Is the 'Generated Patch' semantically equivalent or plausible compared to the 'Reference Ground Truth Patch' in terms of fixing the issue?
4. Are there any introduced errors or regressions in the 'Generated Patch'?

Focus on:
- Security effectiveness: Does the generated patch eliminate the vulnerability like the ground truth does?
- Functional correctness: Does the patch maintain the original intended functionality?
- Implementation quality: Is the approach reasonable and safe?
- Compare semantic meaning and security outcomes rather than exact code matching

Output your evaluation STRICTLY in the following format:
Result: [Correct/Incorrect]
Feedback: [Provide detailed feedback if Incorrect, explaining why the repair is not suitable, what's missing, or what's wrong. If Correct, briefly state "The repair appears correct and aligns with the reference." or similar.]"""
            current_api_params_row["prompt"] = prompt_llm3
            llm_response_llm3 = generate_with_OpenAI_model(**current_api_params_row)
            
            evaluation_correct_current = False
            llm1_feedback_for_next_iteration = "Error or empty response from LLM3"

            if llm_response_llm3 and isinstance(llm_response_llm3, list) and llm_response_llm3[0]:
                raw_llm3_output = llm_response_llm3[0].strip()
                logger.info(f"行 {original_row_index + 1}: LLM3 原始输出 (预览): {raw_llm3_output[:150]}...")
                evaluation_correct_current, llm1_feedback_for_next_iteration = parse_llm3_evaluation(raw_llm3_output)
                logger.info(f"行 {original_row_index + 1}: LLM3 评估结果: {'Correct' if evaluation_correct_current else 'Incorrect'}. 反馈: {llm1_feedback_for_next_iteration[:100]}...")
            else:
                logger.warning(f"行 {original_row_index + 1}: LLM3 未能生成评估结果。")
            
            llm3_evaluation_history.append({
                "iteration": current_iteration + 1,
                "strategy_used": parsed_llm1_strategy_parts,
                "repaired_code_generated": final_llm2_repaired_code,
                "evaluation_is_correct": evaluation_correct_current,
                "evaluator_feedback": llm1_feedback_for_next_iteration
            })
            final_llm3_evaluation_correct = evaluation_correct_current
            final_llm3_feedback = llm1_feedback_for_next_iteration

            if evaluation_correct_current:
                logger.info(f"行 {original_row_index + 1}: 迭代 {current_iteration + 1}: LLM3 评估为正确。结束此行处理。")
                break
            
            current_iteration += 1
            if current_iteration > max_iterations:
                logger.info(f"行 {original_row_index + 1}: 已达到最大迭代次数 ({max_iterations + 1} 次尝试)。")
        
        # End of while loop (iterations)
        status = "failed" if current_row_failed else "success"
        if not current_row_failed:
             iterations_attempted = current_iteration + 1 if final_llm3_evaluation_correct else current_iteration
        else: # current_row_failed is True
            iterations_attempted = current_iteration # The iteration it failed on

        result_item = {
            "original_row_index": original_row_index + 1,
            "cwe_type": cwe_type,
            "cve_id": cve_id,
            "code_before": code_before,
            "code_after_ground_truth": code_after, # This remains the full code for ground truth reference
            
            "llm1a_raw_output": llm1a_raw_output,
            "llm1a_parsed_candidates": llm1a_parsed_candidates,
            "candidate_line_numbers_for_slice": candidate_line_numbers_for_slice,
            "focused_vuln_slice_before": focused_vuln_slice_before,
            
            # "sliced_code_before_initial": sliced_old_initial,
            # "sliced_code_after_initial": sliced_new_initial, # Removed
            
            "max_iterations_configured": max_iterations,
            "iterations_attempted": iterations_attempted,
            
            "final_llm1_strategy_raw": final_llm1_strategy_raw, # LLM1b's raw output
            "final_llm1_precise_source": final_llm1_precise_source,
            "final_llm1_precise_sink": final_llm1_precise_sink,
            "final_llm1_vulnerability_path": final_llm1_vulnerability_path,
            "final_llm1_cwe_specific_strategy": final_llm1_cwe_specific_strategy,
            "final_llm1_pre_repair_state": final_llm1_pre_repair_state,
            "final_llm1_abstract_strategy": final_llm1_abstract_strategy,
            "final_llm1_concrete_strategy": final_llm1_concrete_strategy,
            "final_llm1_post_repair_state": final_llm1_post_repair_state,
            "final_llm1_parsing_status": final_llm1_parsing_status,
            
            "final_llm2_generated_patch": patch_diff_by_llm2 if 'patch_diff_by_llm2' in locals() else "Error: patch_diff_by_llm2 not generated",
            "final_llm3_evaluation_correct": final_llm3_evaluation_correct,
            "final_llm3_feedback": final_llm3_feedback,
            
            "llm1_strategy_history": llm1_strategy_history, # Contains history of LLM1b outputs
            "llm2_repaired_code_history": llm2_repaired_code_history,
            "llm3_evaluation_history": llm3_evaluation_history,
            "status": status
        }
        if current_row_failed: # Add error message if loop broke due to failure
            result_item["error"] = "Processing failed mid-iteration."
            if final_llm1_strategy_raw == "N/A" and llm1_strategy_history and isinstance(llm1_strategy_history[-1], str):
                 result_item["error"] = f"LLM1 failed: {llm1_strategy_history[-1]}"
            elif final_llm2_repaired_code == "N/A" and llm2_repaired_code_history and isinstance(llm2_repaired_code_history[-1], str):
                 result_item["error"] = f"LLM2 failed: {llm2_repaired_code_history[-1]}"


        return result_item

    except KeyError as e:
        logger.error(f"行 {original_row_index + 1}: 处理时发生KeyError - 列 '{e}' 未找到。")
        return {
            "original_row_index": original_row_index + 1, "error": f"KeyError: {e}",
            "cwe_type": row.get('cwe_type', 'N/A'), "cve_id": row.get('cve_id', 'N/A'),
            "code_before": row.get('code_before', 'N/A'), "code_after_ground_truth": row.get('code_after', 'N/A'),
            "status": "failed"
        }
    except Exception as e:
        logger.error(f"行 {original_row_index + 1}: 处理时发生意外错误: {e}", exc_info=True)
        return {
            "original_row_index": original_row_index + 1, "error": str(e),
            "cwe_type": row.get('cwe_type', 'N/A'), "cve_id": row.get('cve_id', 'N/A'),
            "code_before": row.get('code_before', 'N/A'), "code_after_ground_truth": row.get('code_after', 'N/A'),
            "max_iterations_configured": max_iterations,
            "iterations_taken": current_iteration if 'current_iteration' in locals() else 0,
            "llm3_evaluation_history": llm3_evaluation_history if 'llm3_evaluation_history' in locals() else [],
            "status": "failed"
        }

def process_csv_and_analyze_strategy(csv_file_path: str, output_base_dir: str, openai_api_params: dict = None,
                                     max_iterations: int = 1, verbose: bool = False,
                                     num_workers: int = None, log_file: str = None):
    """
    处理CSV文件，执行三LLM迭代流程（策略生成、代码修复、修复评估），并将结果保存到JSON文件。
    支持并行处理CSV行。

    Args:
        csv_file_path (str): 输入CSV文件的完整路径。
        output_base_dir (str): 输出的基础目录。
        openai_api_params (dict, optional): 传递给 OpenAI API 的参数。
        max_iterations (int, optional): LLM1-LLM3反馈循环的最大迭代次数。默认为1。
        verbose (bool, optional): 是否启用详细日志记录到文件。默认为 False。
        num_workers (int, optional): 并行处理的工作线程数。默认为CPU核心数。
        log_file (str, optional): 详细日志输出的文件路径。如果未提供且verbose为True, 默认为 'strategy_analysis.log'。
    """
    
    # 增加 CSV 字段大小限制
    csv.field_size_limit(10000000)  # 设置为10MB，足够大以处理大多数代码片段
    
    # --- Logger Setup ---
    logger = logging.getLogger(__name__ + "_strategy_analyzer") # Unique name
    logger.handlers = [] # Clear previous handlers if any (e.g. during re-runs in same session)
    logger.propagate = False # Prevent root logger from handling messages

    if verbose:
        log_level = logging.INFO
        actual_log_file = log_file if log_file else os.path.join(output_base_dir, "strategy_analysis_details.log")
        os.makedirs(os.path.dirname(actual_log_file), exist_ok=True)
        
        file_handler = logging.FileHandler(actual_log_file, mode='w', encoding='utf-8')
        formatter = logging.Formatter('%(asctime)s - %(levelname)s - TID %(thread)d - %(message)s')
        file_handler.setFormatter(formatter)
        logger.addHandler(file_handler)
        logger.setLevel(log_level)
        print(f"详细日志将输出到: {actual_log_file}")
    else:
        logger.addHandler(logging.NullHandler()) # Add NullHandler if not verbose to prevent "No handlers could be found"
        logger.setLevel(logging.WARNING) # Or some level that won't be hit by info logs

    logger.info(f"开始处理CSV文件: {csv_file_path} (最大迭代次数: {max_iterations})")
    logger.info(f"结果将输出到目录: {output_base_dir}")
    print(f"开始处理CSV文件: {csv_file_path}")


    # 1. 初始化和路径处理
    if not os.path.exists(csv_file_path):
        logger.error(f"错误: CSV文件未找到 - {csv_file_path}")
        print(f"错误: CSV文件未找到 - {csv_file_path}")
        return False

    os.makedirs(output_base_dir, exist_ok=True)
    output_json_path = os.path.join(output_base_dir, "strategy_analysis_output.json")
    output_summary_json_path = os.path.join(output_base_dir, "strategy_analysis_summary_output.json")
    
    results_list = []
    
    actual_num_workers = num_workers if num_workers is not None and num_workers > 0 else os.cpu_count()
    logger.info(f"使用 {actual_num_workers} 个工作线程进行并行处理。")
    print(f"使用 {actual_num_workers} 个工作线程进行并行处理。")

    # 2. 读取和处理CSV文件
    all_rows_with_indices = []
    try:
        with open(csv_file_path, mode='r', encoding='utf-8-sig') as file:
            reader = csv.DictReader(file)
            if not reader.fieldnames:
                logger.error(f"错误: CSV文件 {csv_file_path} 为空或无法读取表头。")
                print(f"错误: CSV文件 {csv_file_path} 为空或无法读取表头。")
                return False
            
            logger.info(f"CSV表头: {reader.fieldnames}")
            required_columns = ['code_before', 'code_after', 'cwe_type', 'cve_id']
            for col in required_columns:
                if col not in reader.fieldnames:
                    logger.error(f"错误: CSV文件缺少必需的列 '{col}'。请确保CSV包含 {required_columns} 列。")
                    print(f"错误: CSV文件缺少必需的列 '{col}'。请确保CSV包含 {required_columns} 列。")
                    return False
            all_rows_with_indices = list(enumerate(reader))
            print(f"共读取 {len(all_rows_with_indices)} 行数据进行处理。")
            logger.info(f"共读取 {len(all_rows_with_indices)} 行数据进行处理。")

    except FileNotFoundError:
        logger.error(f"错误: CSV文件 {csv_file_path} 未找到。")
        print(f"错误: CSV文件 {csv_file_path} 未找到。")
        return False
    except Exception as e:
        logger.error(f"错误: 读取或处理CSV文件 {csv_file_path} 时发生错误: {e}", exc_info=True)
        print(f"错误: 读取或处理CSV文件 {csv_file_path} 时发生错误: {e}")
        return False

    if not all_rows_with_indices:
        print("CSV文件中没有数据行可供处理。")
        logger.warning("CSV文件中没有数据行可供处理。")
        return False

    with concurrent.futures.ThreadPoolExecutor(max_workers=actual_num_workers) as executor:
        # Prepare arguments for each call to _process_row
        # The logger object is shared, which is fine as logging is thread-safe.
        futures_to_rows = {
            executor.submit(_process_row, row_tuple, openai_api_params, max_iterations, logger): row_tuple
            for row_tuple in all_rows_with_indices
        }
        
        # 使用tqdm创建进度条
        with tqdm(total=len(all_rows_with_indices), desc="处理CSV行数据", unit="行") as pbar:
            for future in concurrent.futures.as_completed(futures_to_rows):
                row_tuple_original = futures_to_rows[future]
                try:
                    result = future.result()
                    results_list.append(result)
                except Exception as exc:
                    original_idx, original_r = row_tuple_original
                    logger.error(f"行 {original_idx + 1} 在执行期间产生异常: {exc}", exc_info=True)
                    results_list.append({
                        "original_row_index": original_idx + 1, "error": f"Unhandled exception in thread: {exc}",
                        "cwe_type": original_r.get('cwe_type', 'N/A'), "cve_id": original_r.get('cve_id', 'N/A'),
                        "code_before": original_r.get('code_before', 'N/A'), "code_after_ground_truth": original_r.get('code_after', 'N/A'),
                        "status": "failed_exception_in_thread"
                    })
                finally:
                    # 更新进度条
                    pbar.update(1)

    # Sort results by original_row_index to maintain order in the output JSON if needed,
    # though each item already contains its original_row_index.
    # This is important if as_completed was used and order is desired for the final list.
    results_list.sort(key=lambda x: x.get("original_row_index", float('inf')))

    processed_rows = sum(1 for r in results_list if r.get("status") == "success" and "error" not in r)
    failed_rows = len(results_list) - processed_rows
    
    logger.info(f"所有行处理完成。成功: {processed_rows}, 失败/跳过: {failed_rows}")
    print(f"所有行处理完成。成功: {processed_rows}, 失败/跳过: {failed_rows}")


    # 7. 写入JSON输出文件
    if results_list:
        summary_results_list = []
        summary_keys_to_include = [
            "original_row_index", "cwe_type", "cve_id", "code_before", "code_after_ground_truth",
            
            "llm1a_raw_output",
            # "llm1a_parsed_candidates", # This might be too verbose for summary
            "candidate_line_numbers_for_slice",
            # "focused_vuln_slice_before", # This might be too verbose for summary
            
            "sliced_code_before_initial", # "sliced_code_after_initial" removed
            "max_iterations_configured", "iterations_attempted",
            # "iterations_taken", # This key seems to be populated only in exception paths in _process_row.
                                # Ensure it's consistently available if included in summary.
                                # For now, keeping it commented out from summary to avoid KeyErrors if not present.

            "final_llm1_strategy_raw", # LLM1b's raw output
            "final_llm1_precise_source",
            "final_llm1_precise_sink",
            "final_llm1_vulnerability_path",
            "final_llm1_cwe_specific_strategy",
            "final_llm1_pre_repair_state",
            "final_llm1_abstract_strategy",
            "final_llm1_concrete_strategy",
            "final_llm1_post_repair_state",
            "final_llm1_parsing_status",
            
            "final_llm2_generated_patch",
            "final_llm3_evaluation_correct",
            "final_llm3_feedback",
            "error",
            "status"
        ]
        # Notes on potentially verbose fields for summary:
        # - "llm1a_parsed_candidates": Contains structured source/sink candidates, can be large.
        # - "focused_vuln_slice_before": The actual code slice, can be very large.
        # - "iterations_taken": Needs consistent population in result_item.
        # These are excluded or commented out from the summary for brevity but will be in the full JSON output.

        for item in results_list:
            summary_item = {key: item.get(key, "N/A_in_summary") for key in summary_keys_to_include} # Use .get for safety
            summary_results_list.append(summary_item)

        try:
            output_dir = os.path.dirname(output_json_path)
            os.makedirs(output_dir, exist_ok=True)

            with open(output_json_path, mode='w', encoding='utf-8') as outfile:
                json.dump(results_list, outfile, indent=4, ensure_ascii=False)
            logger.info(f"完整处理结果已保存到: {output_json_path}")
            print(f"完整处理结果已保存到: {output_json_path}")

            if summary_results_list:
                with open(output_summary_json_path, mode='w', encoding='utf-8') as outfile_summary:
                    json.dump(summary_results_list, outfile_summary, indent=4, ensure_ascii=False)
                logger.info(f"摘要处理结果已保存到: {output_summary_json_path}")
                print(f"摘要处理结果已保存到: {output_summary_json_path}")
            else:
                logger.warning(f"没有摘要信息可以写入到: {output_summary_json_path}")
                print(f"没有摘要信息可以写入到: {output_summary_json_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"错误: 写入JSON文件时发生错误: {e}", exc_info=True)
            print(f"错误: 写入JSON文件时发生错误: {e}")
            return False
    else:
        logger.warning("没有结果可以写入JSON文件。")
        print("没有结果可以写入JSON文件。")
        if processed_rows == 0 and failed_rows > 0:
             logger.warning("所有行处理均失败或被跳过。")
             print("所有行处理均失败或被跳过。")
        return False


if __name__ == '__main__':
    # 增加 CSV 字段大小限制
    csv.field_size_limit(10000000)  # 设置为10MB
    
    dataset_name = "manual_example"
    sample_csv_path = f"/mnt/projects/unnamed/datasets/{dataset_name}/processed_vulnerabilities.csv" 
    output_csv_path=sample_csv_path.replace('.csv', '_final.csv')
    add_headers_and_ids_to_csv( # This might fail if file is empty or headers are different
        input_csv_path=sample_csv_path,
        output_csv_path=output_csv_path, 
        headers=['code_before', 'code_after', 'cwe_type', 'cve_id'], # Must match CSV
        id_column_name='id',
        id_prefix=''
    )

    try:
        user_openai_params = {"model_ckpt": "ep-20250329153117-pqs5b","temperature": 0.0}
        base_output_dir = f"/mnt/projects/unnamed/datasets/{dataset_name}/outputs"
        os.makedirs(base_output_dir, exist_ok=True)
        
        output_dir_verbose = f"{base_output_dir}/strategy_analysis_verbose"
        log_file_verbose = os.path.join(output_dir_verbose, 'detailed_run.log')
        print(f"\n# 调用 process_csv_and_analyze_strategy 进行处理 (并行, verbose=True, log_file='{log_file_verbose}')...")
        success_verbose = process_csv_and_analyze_strategy(
            csv_file_path=output_csv_path,
            output_base_dir=output_dir_verbose,
            openai_api_params=user_openai_params,
            max_iterations=2,
            verbose=True,
            num_workers=64, # Example: 2 workers
            log_file=log_file_verbose
        )
        if success_verbose:
            print(f"\n详细模式并行处理成功。检查 {os.path.join(output_dir_verbose, 'strategy_analysis_output.json')} 和日志 {log_file_verbose}。")
        else:
            print("\n详细模式并行处理失败。")

        # output_dir_quiet = f"{base_output_dir}/strategy_analysis_quiet"
        # print(f"\n# 调用 process_csv_and_analyze_strategy 进行处理 (并行, verbose=False)...")
        # success_quiet = process_csv_and_analyze_strategy(
        #     csv_file_path=output_csv_path,
        #     output_base_dir=output_dir_quiet,
        #     openai_api_params=user_openai_params,
        #     max_iterations=2,
        #     verbose=False,
        #     num_workers=64,
        #     log_file=log_file_verbose
        # )
        # if success_quiet:
        #     print(f"\n安静模式并行处理成功。检查 {os.path.join(output_dir_quiet, 'strategy_analysis_output.json')}。")
        # else:
        #     print("\n安静模式并行处理失败。")
    except Exception as e:
        print(f"\n错误: 运行示例时发生意外错误: {e}", exc_info=True)