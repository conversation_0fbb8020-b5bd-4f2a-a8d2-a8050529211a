import json
import difflib
import csv
import concurrent.futures
import threading
import enum
import os
from typing import List, Optional, Dict, Tuple
from utils.graphslice_analyzer import analyze_code_diff
from models.OpenAI_API import generate_with_OpenAI_model
csv.field_size_limit(10000000) 
# 评估结果枚举
class EvaluationResult(enum.Enum):
    SYNTACTIC_PATCH_EQUIVALENT = "SynPatchEq"  # 新增：补丁语法等价 (diff内容完全相同)
    SEMANTIC_EQUIVALENT = "SemEq"  # 语义等价
    PLAUSIBLE = "Plausible"        # 合理性
    INCORRECT = "Incorrect"        # 不正确
    UNKNOWN = "Unknown"            # 评估失败或未知

# 默认OpenAI API参数
DEFAULT_OPENAI_PARAMS = {
    "temperature": 0,
    # n is handled by the API call directly, not a default param for all calls.
}

def add_line_numbers_to_code(code_string: str) -> str:
    """为代码字符串的每一行添加行号"""
    if not code_string or not code_string.strip():
        return code_string
    
    lines = code_string.splitlines()
    numbered_lines = [f"{i+1:3d} | {line}" for i, line in enumerate(lines)]
    return "\n".join(numbered_lines)

def generate_simple_vulnerability_repair(
    vulnerable_code: str, 
    cwe_id: str, 
    vulnerable_line_numbers: List[int],
    model_config: dict, # 更改：使用模型配置字典
    verbose: bool = False
) -> Optional[List[dict]]:
    """
    使用单个LLM调用生成5个不同的漏洞修复补丁
    
    Args:
        vulnerable_code: 包含漏洞的原始代码
        cwe_id: CWE漏洞类型ID
        vulnerable_line_numbers: 漏洞所在行号列表
        model_config: 用于Patch生成的LLM模型配置
        verbose: 是否输出详细信息
        
    Returns:
        包含5个修复补丁的列表，如果失败则返回None
    """
    if verbose:
        print(f"开始简单修复处理: CWE ID={cwe_id}, 漏洞行号={vulnerable_line_numbers}")
    
    # 构建修复prompt
    repair_prompt = f"""
You are an expert security code analyst and vulnerability repair specialist. Your task is to analyze the given vulnerable code and generate 5 different but valid patches to fix the identified security vulnerability.

**Vulnerable Code (CWE: {cwe_id}):**
```
{add_line_numbers_to_code(vulnerable_code)}
```

**Vulnerable Line Numbers:** {', '.join(map(str, vulnerable_line_numbers))}

**Task:**
1. Carefully analyze the provided vulnerable code, paying special attention to the lines marked as vulnerable.
2. Identify the specific security vulnerability based on the CWE type and vulnerable lines.
3. Design 5 valid fixes that:
   - Each eliminates the security vulnerability using a different approach
   - Each preserves the original code functionality
   - Each follows secure coding best practices

**Output Requirements:**
For each of the 5 patches, respond with the following structured format (NOT JSON):

=== PATCH 1 ===
PATCH_DIFF_START:
[Your unified diff patch here - this should be in standard unified diff format showing exactly what lines to change]
PATCH_DIFF_END:

=== PATCH 2 ===
[Same format as above with a different repair approach]

=== PATCH 3 ===
[Same format as above with a different repair approach]

=== PATCH 4 ===
[Same format as above with a different repair approach]

=== PATCH 5 ===
[Same format as above with a different repair approach]

**Important Guidelines:**
- Each patch should be a valid unified diff that can be applied with standard patch tools
- Ensure each patch maintains original functionality while addressing the vulnerability differently
- Make each patch immediately applicable to the provided code
"""

    try:
        if verbose:
            print("调用LLM生成5个修复补丁...")
        
        # 调用LLM生成修复
        # llm_params 字典的构建方式需要调整以适应新的 generate_with_OpenAI_model 签名
        # DEFAULT_OPENAI_PARAMS 现在只包含 temperature 等通用参数
        
        api_call_params = {
            "prompt": repair_prompt,
            "model_config": model_config, # 传递完整的模型配置
            "max_tokens": 4000,
            "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0), # 从默认值获取或设为0
            "n": 1,  # 只需要一次调用，因为我们在prompt中要求5个patch
            # top_p is not explicitly in DEFAULT_OPENAI_PARAMS, will use generate_with_OpenAI_model's default
        }
        
        responses = generate_with_OpenAI_model(**api_call_params)
        
        if not responses or not responses[0]:
            if verbose:
                print("错误: LLM未返回有效响应")
            return None
            
        response_text = responses[0].strip()
        
        if verbose:
            print(f"LLM响应长度: {len(response_text)} 字符")
        
        # 解析5个patch
        patches = []
        import re
        
        # 定位并处理LLM响应以正确解析补丁
        actual_patch_contents = []
        first_patch_marker = "=== PATCH 1 ==="
        marker_index = response_text.find(first_patch_marker)

        if marker_index != -1:
            # "=== PATCH 1 ===" 标记存在，从该标记开始处理
            text_to_split = response_text[marker_index:]
            if verbose:
                print(f"DEBUG: Text to split (from '{first_patch_marker}'):\n'{text_to_split[:200].strip()}...'")
            
            # 分割以 "=== PATCH \d+ ===" 为分隔符的文本
            # 由于 text_to_split 以分隔符开头, split_parts[0] 将是空字符串
            split_parts = re.split(r'=== PATCH \d+ ===', text_to_split)
            
            if len(split_parts) > 1:
                actual_patch_contents = split_parts[1:] # 跳过第一个空字符串，获取实际的补丁内容
            elif verbose:
                print(f"DEBUG: Split after '{first_patch_marker}' did not yield multiple parts. Raw split: {split_parts}")
        else:
            # "=== PATCH 1 ===" 标记未找到，回退到原始的分割逻辑（可能LLM未按预期格式响应）
            if verbose:
                print(f"WARNING: Marker '{first_patch_marker}' not found. Using original split logic on full response.")
            
            split_parts = re.split(r'=== PATCH \d+ ===', response_text)
            # 检查原始分割结果的第一个元素是否为空（例如，如果响应以 "=== PATCH 2 ===" 开头）
            if split_parts and not split_parts[0].strip() and len(split_parts) > 1:
                actual_patch_contents = split_parts[1:]
            else:
                # 如果第一个元素非空（可能是前导文本或单个未标记的补丁块）
                actual_patch_contents = split_parts
        
        patch_sections = actual_patch_contents # 将处理后的内容用于后续循环

        if verbose:
            print(f"检测到 {len(patch_sections)} 个待处理的patch段落:")
            for idx_debug, sec_debug in enumerate(patch_sections): # 使用不同的变量名以避免与外层循环的 `i` 和 `section` 混淆
                print(f"  待处理 Section {idx_debug}: '{sec_debug[:100].strip()}...'")
        
        for i, section in enumerate(patch_sections[:5]):  # 最多处理5个patch
            try:
                if verbose:
                    print(f"处理第 {i+1} 个patch...")
                
                # 提取patch_diff
                patch_match = re.search(r'PATCH_DIFF_START:\s*(.*?)\s*PATCH_DIFF_END:', section, re.DOTALL)
                patch_diff = patch_match.group(1).strip() if patch_match else ""
                
                # 验证必需字段
                
                # 构建patch对象，参考inference.py的格式
                patch_obj = {
                    "suggestion_patch": patch_diff,
                    "patch_index": i + 1,
                    # 兼容inference.py的字段
                    "source_root_cause_desc": f"Auto-identified vulnerability in CWE-{cwe_id} (Patch {i+1})",
                    "source_example_distance": -1.0,  # 没有示例距离
                    "llm_score": "未评分"  # 简单推理没有评分机制
                }
                
                patches.append(patch_obj)
                
                
            except Exception as e:
                if verbose:
                    print(f"解析第 {i+1} 个patch时出错: {e}")
                continue
        
        if not patches:
            if verbose:
                print("错误: 未能解析出任何有效的patch")
            return None
        
        if verbose:
            print(f"成功生成 {len(patches)} 个修复补丁")
        
        return patches
            
    except Exception as e:
        if verbose:
            print(f"错误: 修复生成过程中发生异常 - {e}")
        return None

def evaluate_multiple_patches_quality(
    original_vulnerable_code: str,
    patch_list: List[dict],
    ground_truth_patch: str,
    cwe_id: str,
    evaluation_model_config: dict, # 更改：使用模型配置字典
    verbose: bool = False
) -> List[dict]:
    """
    批量评估多个补丁的质量，参考inference.py的批量评估模式
    
    Args:
        original_vulnerable_code: 原始有漏洞的代码
        patch_list: 包含多个补丁的列表，每个元素都有suggestion_patch字段
        ground_truth_patch: 标准答案补丁 (unified diff格式)
        cwe_id: CWE漏洞类型ID
        evaluation_model_config: 用于评估的LLM模型配置
        verbose: 是否输出详细信息
        
    Returns:
        List[dict]: 评估结果列表，每个元素包含patch索引和评估详情
    """
    if verbose:
        print(f"开始批量评估 {len(patch_list)} 个补丁的质量 (CWE: {cwe_id})...")
    
    evaluations = []
    
    for i, patch_info in enumerate(patch_list):
        generated_patch = patch_info.get('suggestion_patch', '')
        
        if not generated_patch:
            if verbose:
                print(f"跳过第 {i+1} 个patch: 没有有效的patch内容")
            evaluations.append({
                "suggestion_index": i,
                "evaluation_result": "Unknown",
                "explanation": "没有有效的patch内容",
                "evaluation_details": {},
                "generated_patch": "",
                "repair_metadata": patch_info
            })
            continue
        
        try:
            eval_result, explanation, eval_data = evaluate_patch_quality(
                original_vulnerable_code=original_vulnerable_code,
                generated_patch=generated_patch,
                ground_truth_patch=ground_truth_patch,
                cwe_id=cwe_id,
                model_config=evaluation_model_config, # 传递评估模型配置
                verbose=verbose
            )
            
            evaluations.append({
                "suggestion_index": i,
                "evaluation_result": eval_result.value,
                "explanation": explanation,
                "evaluation_details": eval_data,
                "generated_patch": generated_patch,
                "repair_metadata": patch_info
            })
            
            if verbose:
                print(f"第 {i+1} 个patch评估结果: {eval_result.value}")
                
        except Exception as e:
            if verbose:
                print(f"评估第 {i+1} 个patch时出错: {e}")
            evaluations.append({
                "suggestion_index": i,
                "evaluation_result": "Unknown",
                "explanation": f"评估过程中发生错误: {str(e)}",
                "evaluation_details": {},
                "generated_patch": generated_patch,
                "repair_metadata": patch_info
            })
    
    if verbose:
        # 统计评估结果
        result_counts = {}
        for evaluation in evaluations:
            result = evaluation["evaluation_result"]
            result_counts[result] = result_counts.get(result, 0) + 1
        
        print(f"批量评估完成，结果统计: {result_counts}")
    
    return evaluations

def evaluate_patch_quality(
    original_vulnerable_code: str,
    generated_patch: str,
    ground_truth_patch: str,
    cwe_id: str,
    model_config: dict, # 更改：使用模型配置字典
    verbose: bool = False
) -> Tuple[EvaluationResult, str, dict]:
    """
    使用LLM自动评估生成的补丁质量
    
    Args:
        original_vulnerable_code: 原始有漏洞的代码
        generated_patch: 生成的修复补丁 (unified diff格式)
        ground_truth_patch: 标准答案补丁 (unified diff格式)
        cwe_id: CWE漏洞类型ID
        model_config: 用于评估的LLM模型配置
        verbose: 是否输出详细信息
        
    Returns:
        Tuple[EvaluationResult, str, dict]: (评估结果, 详细解释, 原始评估数据)
    """
    if verbose:
        print(f"开始LLM自动评估补丁质量 (CWE: {cwe_id})...")
    
    # 构建评估prompt
    evaluation_prompt = f"""
You are an expert security code reviewer evaluating a generated vulnerability fix.

**Original Vulnerable Code:**
```c
{add_line_numbers_to_code(original_vulnerable_code)}
```

**CWE Type:** {cwe_id}

**Ground Truth Patch (Reference Solution - Unified Diff Format):**
```diff
{ground_truth_patch}
```

**Generated Patch (Unified Diff Format):**
```diff
{generated_patch}
```

**Evaluation Task:**
Compare the 'Generated Patch' to the 'Ground Truth Patch' and assess its quality. Follow this evaluation order:

**1. Syntactic Patch Equivalence (SynPatchEq) (Highest Priority):**
   - Is the text content of the 'Generated Patch' **IDENTICAL** to the text content of the 'Ground Truth Patch'?
   If yes, classify as **SynPatchEq**. This is the final evaluation.

**2. Correctness Assessment (If not SynPatchEq):**
   If not SynPatchEq, evaluate if the 'Generated Patch', when applied to the 'Original Vulnerable Code', is correct:
   - Does it fully fix the vulnerability?
   - Does it maintain original functionality without breaking anything?
   - Is it technically sound?
   If it fails any of these, classify as **Incorrect**.

**3. Semantic Equivalence (SemEq) vs. Plausible (If Correct but not SynPatchEq):**
   If the 'Generated Patch' is Correct (and not SynPatchEq), classify its *applied effect*:
   - **SemEq**: The applied 'Generated Patch' achieves the same core security effect and functional behavior as the applied 'Ground Truth Patch'. The fundamental security outcome and functional preservation are equivalent, even if implementation details differ.
   - **Plausible**: The applied 'Generated Patch' is a technically sound alternative solution that fixes the vulnerability and maintains functionality, but uses a different approach than the 'Ground Truth Patch' would.
   - **Incorrect**: If it doesn't meet SemEq or Plausible criteria after passing Correctness.

**Output Format (JSON):**
{{
    "evaluation_result": "SynPatchEq" | "SemEq" | "Plausible" | "Incorrect",
    "detailed_explanation": "Briefly explain your decision, highlighting key reasons.",
    "confidence_level": "High/Medium/Low"
}}
Important: Be strict but fair. Focus on the definitions provided.
"""

    try:
        # 调用LLM进行评估
        # llm_params 字典的构建方式需要调整
        api_call_params = {
            "prompt": evaluation_prompt,
            "model_config": model_config, # 传递完整的模型配置
            "max_tokens": 2000,
            "temperature": DEFAULT_OPENAI_PARAMS.get("temperature", 0),
            "n": 1
            # top_p will use generate_with_OpenAI_model's default
        }
        
        if verbose:
            print("调用LLM进行补丁质量评估...")
        
        llm_responses = generate_with_OpenAI_model(**api_call_params)
        
        if not llm_responses or not llm_responses[0]:
            if verbose:
                print("警告: LLM未返回有效评估响应")
            return EvaluationResult.UNKNOWN, "LLM评估失败：无响应", {}
            
        response_text = llm_responses[0].strip()
        
        # 尝试解析JSON响应
        import re
        json_match = re.search(r'```json\s*([\s\S]*?)\s*```', response_text)
        if not json_match:
            json_match = re.search(r'({[\s\S]*})', response_text)
            
        if json_match:
            json_str = json_match.group(1)
            try:
                evaluation_data = json.loads(json_str)
                
                # 提取评估结果
                result_str = evaluation_data.get("evaluation_result", "")
                detailed_explanation = evaluation_data.get("detailed_explanation", "")
                
                # 映射结果字符串到枚举
                if result_str.lower() == "synpatcheq": # 新增处理
                    evaluation_result = EvaluationResult.SYNTACTIC_PATCH_EQUIVALENT
                elif result_str.lower() == "semeq":
                    evaluation_result = EvaluationResult.SEMANTIC_EQUIVALENT
                elif result_str.lower() == "plausible":
                    evaluation_result = EvaluationResult.PLAUSIBLE
                elif result_str.lower() == "incorrect":
                    evaluation_result = EvaluationResult.INCORRECT
                else:
                    if verbose:
                        print(f"警告: 未知的评估结果 '{result_str}'，标记为UNKNOWN")
                    evaluation_result = EvaluationResult.UNKNOWN
                
                if verbose:
                    print(f"LLM评估结果: {evaluation_result.value}")
                    print(f"置信度: {evaluation_data.get('confidence_level', 'N/A')}")
                    print(f"安全有效性: {evaluation_data.get('security_effectiveness', 'N/A')}")
                    print(f"功能正确性: {evaluation_data.get('functional_correctness', 'N/A')}")
                
                return evaluation_result, detailed_explanation, evaluation_data
                
            except json.JSONDecodeError as e:
                if verbose:
                    print(f"错误: JSON解析失败 - {e}")
                return EvaluationResult.UNKNOWN, f"JSON解析错误: {e}", {"raw_response": response_text}
        else:
            if verbose:
                print("警告: LLM响应中未找到有效JSON格式")
            return EvaluationResult.UNKNOWN, "响应格式错误：未找到JSON", {"raw_response": response_text}
            
    except Exception as e:
        if verbose:
            print(f"错误: LLM评估过程中发生异常 - {e}")
        return EvaluationResult.UNKNOWN, f"评估异常: {e}", {}

def find_insertion_point(original_code: str, fixed_code: str) -> Optional[List[int]]:
    """
    根据代码差异（特别是纯插入）查找上下文相关的行号。
    如果原始代码为空且修复后代码不为空，则返回 [1]。
    对于插入操作，返回插入点之前和之后的原始行号（1-based）。
    """
    if not original_code.strip() and fixed_code.strip(): # 原始代码为空，修复代码不为空
        return [1]

    original_lines = original_code.splitlines()
    fixed_lines = fixed_code.splitlines()
    
    # difflib should already be imported in the file (e.g. import difflib)
    matcher = difflib.SequenceMatcher(None, original_lines, fixed_lines)
    opcodes = matcher.get_opcodes()
    
    context_lines = []
    
    for tag, i1, i2, j1, j2 in opcodes:
        # We are interested in 'insert' or 'replace' that acts like an insert (i1 == i2)
        if tag == 'insert' or (tag == 'replace' and i1 == i2):
            # This block means fixed_lines[j1:j2] is effectively inserted before original_lines[i1]
            
            current_op_context = []
            if i1 == 0: # Insertion at the beginning of the file
                if len(original_lines) > 0: # If original file has content
                    current_op_context.append(1) # Context is the first line of original content
                # If original_lines is empty, it's handled by the initial check of the function
            elif i1 == len(original_lines): # Insertion at the end of the file
                if len(original_lines) > 0:
                    current_op_context.append(len(original_lines)) # Context is the last line of original content
            else: # Insertion in the middle (0 < i1 < len(original_lines))
                # original_lines[i1-1] is the line before, original_lines[i1] is the line at/after insertion point
                current_op_context.append(i1)       # Line *before* insertion (1-based index of original_lines[i1-1])
                current_op_context.append(i1 + 1)   # Line *at/after* insertion (1-based index of original_lines[i1])
            
            if current_op_context:
                context_lines.extend(current_op_context)
                # We focus on the context of the first significant insertion operation.
                break 
                
    if context_lines:
        return sorted(list(set(context_lines))) # Return unique, sorted line numbers
    
    # Fallback: if original_code != fixed_code and not context_lines:
    # This implies a change occurred, but not a simple insertion detected by the logic above.
    # For now, returning None if no specific insertion context is found.
    return None
def _process_single_row(
    row_idx: int,
    original_code: Optional[str],
    fixed_code: Optional[str],
    cwe_id: Optional[str],
    trigger_path_value: Optional[str],
    enable_llm_evaluation: bool,
    output_json_path: str,
    output_lock: threading.Lock,
    generation_model_config: dict, # 更改
    evaluation_model_config: dict, # 更改
    verbose: bool = False
):
    """
    处理单个CSV行，生成修复并评估
    """
    current_entry_identifier = f"CSV Row {row_idx + 2} (CWE: {cwe_id or 'N/A'})"
    
    result_entry = {
        "csv_row": row_idx + 2,
        "original_code": original_code,
        "fixed_code": fixed_code,
        "cwe_id": cwe_id,
        "vulnerable_lines": [],
        "line_source_method": None
    }

    if not all([original_code, fixed_code, cwe_id]):
        error_msg = "Missing one or more required fields (code_before, code_after, cwe_id)."
        if verbose:
            print(f"跳过 {current_entry_identifier}: {error_msg}")
        result_entry.update({
            "status": "error",
            "error_message": error_msg
        })
    else:
        vulnerable_line_numbers: List[int] = []
        line_source_method: Optional[str] = None
        skip_processing_flag: bool = False

        # 尝试从trigger_path获取行号
        if trigger_path_value and trigger_path_value.strip() and trigger_path_value.strip() != "?":
            try:
                parsed_lines = json.loads(trigger_path_value)
                if isinstance(parsed_lines, list) and all(isinstance(line, int) for line in parsed_lines):
                    if parsed_lines:
                        vulnerable_line_numbers = parsed_lines
                        line_source_method = "trigger_path"
                    else:
                        if verbose:
                            print(f"{current_entry_identifier}: trigger_path为空列表，回退到analyze_code_diff")
                else:
                    if verbose:
                        print(f"{current_entry_identifier}: trigger_path格式无效，回退到analyze_code_diff")
            except json.JSONDecodeError:
                if verbose:
                    print(f"{current_entry_identifier}: trigger_path JSON解析失败，回退到analyze_code_diff")

        # 如果trigger_path没有提供行号，使用代码diff分析
        if not vulnerable_line_numbers:
            try:
                diff_lines, _ = analyze_code_diff(original_code, fixed_code)
                if diff_lines:
                    vulnerable_line_numbers = diff_lines
                    line_source_method = "analyze_code_diff"
                else:
                    # 新增逻辑：尝试使用 difflib 查找插入点
                    if verbose:
                        print(f"{current_entry_identifier}: analyze_code_diff 未返回行号，尝试 find_insertion_point...")
                    
                    insertion_points = None
                    # 确保 original_code 和 fixed_code 在这里是有效的字符串
                    if original_code is not None and fixed_code is not None:
                        insertion_points = find_insertion_point(original_code, fixed_code)
                    
                    if insertion_points:
                        vulnerable_line_numbers = insertion_points
                        line_source_method = "diff_insertion_point"
                        if verbose:
                             print(f"{current_entry_identifier}: 使用 find_insertion_point 找到的行号: {vulnerable_line_numbers}")
                    else:
                        # 如果 find_insertion_point 也没有结果，才真正跳过
                        error_msg = "无法从trigger_path、analyze_code_diff或find_insertion_point获取漏洞行号"
                        if verbose:
                            print(f"{current_entry_identifier}: {error_msg}")
                        result_entry.update({
                            "status": "skipped_no_line_numbers",
                            "message": error_msg,
                            "vulnerable_lines": []
                        })
                        skip_processing_flag = True
            except Exception as diff_e: # 更通用的异常捕获
                error_msg = f"代码diff分析或插入点查找失败: {str(diff_e)}"
                if verbose:
                    print(f"错误 {current_entry_identifier}: {error_msg}")
                result_entry.update({
                    "status": "error_diff_analysis",
                    "error_message": error_msg,
                    "vulnerable_lines": []
                })
                skip_processing_flag = True
        
        # 更新行号信息
        if not skip_processing_flag:
            if verbose and line_source_method:
                print(f"{current_entry_identifier}: 使用来自 {line_source_method} 的行号: {vulnerable_line_numbers}")
            result_entry["vulnerable_lines"] = vulnerable_line_numbers
            result_entry["line_source_method"] = line_source_method
        
        # 开始修复处理
        if not skip_processing_flag:
            try:
                if verbose:
                    print(f"{current_entry_identifier}: 开始生成修复...")
                
                # 生成修复
                repair_results = generate_simple_vulnerability_repair(
                    vulnerable_code=original_code,
                    cwe_id=cwe_id,
                    vulnerable_line_numbers=vulnerable_line_numbers,
                    model_config=generation_model_config, # 传递生成模型配置
                    verbose=verbose
                )
 
                if repair_results:
                    if verbose:
                        print(f"{current_entry_identifier}: 成功生成修复补丁")
                    
                    evaluations = []
                    eval_stats = {}
                    ground_truth_diff = ""
                    
                    # LLM自动评估
                    if enable_llm_evaluation:
                        if verbose:
                            print(f"{current_entry_identifier}: 开始LLM批量自动评估...")
                        
                        # 生成ground truth的unified diff
                        ground_truth_diff = '\n'.join(difflib.unified_diff(
                            original_code.splitlines(keepends=True),
                            fixed_code.splitlines(keepends=True),
                            fromfile='original_vulnerable.c',
                            tofile='ground_truth_fixed.c',
                            lineterm=''
                        ))
                        
                        # 使用批量评估函数
                        evaluations = evaluate_multiple_patches_quality(
                            original_vulnerable_code=original_code,
                            patch_list=repair_results,
                            ground_truth_patch=ground_truth_diff,
                            cwe_id=cwe_id,
                            evaluation_model_config=evaluation_model_config, # 传递评估模型配置
                            verbose=verbose
                        )
                        
                        # 计算评估统计信息
                        eval_stats = {
                            "total_suggestions": len(repair_results),
                            "syntactic_patch_equivalent": len([e for e in evaluations if e["evaluation_result"] == "SynPatchEq"]), # 新增
                            "semantic_equivalent": len([e for e in evaluations if e["evaluation_result"] == "SemEq"]),
                            "plausible": len([e for e in evaluations if e["evaluation_result"] == "Plausible"]),
                            "incorrect": len([e for e in evaluations if e["evaluation_result"] == "Incorrect"]),
                            "unknown": len([e for e in evaluations if e["evaluation_result"] == "Unknown"])
                        }
                        
                        if verbose:
                            print(f"{current_entry_identifier}: 批量评估完成，总计 {len(evaluations)} 个结果")
                    
                    # 构建成功结果
                    # 将 repair_results 转换为 visualize_repairs.py 期望的格式
                    repairs_list = []
                    for repair_result in repair_results:
                        # 转换为 visualize_repairs.py 期望的格式
                        repair_item = {
                            "suggestion_patch": repair_result.get("suggestion_patch", ""),
                            "suggestion_code": "",  # simple_inference 没有生成完整代码，只有patch
                            "repair_strategy": repair_result.get("repair_strategy", ""),
                            "key_variables": repair_result.get("key_variables", []),
                            "llm_score": "未评分",  # simple_inference 没有评分机制
                            # 保留原始数据用于调试
                            "original_repair_result": repair_result
                        }
                        repairs_list.append(repair_item)
                    
                    result_update = {
                        "status": "success",
                        "repairs": repairs_list,  # 改为 repairs 而不是 repair_results
                        "repair_results": repair_results  # 保留原始格式以备兼容性
                    }
                    
                    if enable_llm_evaluation:
                        # evaluations已经是正确的格式，直接使用
                        result_update.update({
                            "llm_evaluations": evaluations,
                            "evaluation_statistics": eval_stats,
                            "ground_truth_patch": ground_truth_diff
                        })
                    
                    if verbose:
                        print(f"{current_entry_identifier}: 生成的修复数据格式:")
                        print(f"  - repairs列表包含 {len(repairs_list)} 个修复建议")
                        if repairs_list:
                            print(f"  - 第一个建议的patch长度: {len(repairs_list[0].get('suggestion_patch', ''))}")
                        if enable_llm_evaluation:
                            print(f"  - LLM评估包含 {len(evaluations)} 个评估结果")
                            # 显示评估结果分布
                            result_distribution = {}
                            for eval_item in evaluations:
                                result = eval_item.get("evaluation_result", "Unknown")
                                result_distribution[result] = result_distribution.get(result, 0) + 1
                            print(f"  - 评估结果分布: {result_distribution}")
                    
                    result_entry.update(result_update)
                    
                else:
                    msg = "修复生成失败"
                    if verbose:
                        print(f"{current_entry_identifier}: {msg}")
                    result_entry.update({
                        "vulnerable_lines": vulnerable_line_numbers,
                        "status": "repair_failed",
                        "message": msg
                    })
                    
            except Exception as e:
                error_msg = f"修复生成过程中发生异常: {str(e)}"
                if verbose:
                    print(f"严重错误 {current_entry_identifier}: {error_msg}")
                result_entry.update({
                    "vulnerable_lines": vulnerable_line_numbers if vulnerable_line_numbers else [],
                    "status": "error",
                    "error_message": error_msg
                })

    # 写入结果到JSON Lines文件
    try:
        result_json_str = json.dumps(result_entry, ensure_ascii=False) + '\n'
        with output_lock:
            with open(output_json_path, 'a', encoding='utf-8') as outfile:
                outfile.write(result_json_str)
    except Exception as write_e:
        print(f"写入结果失败 {current_entry_identifier} 到 {output_json_path}: {write_e}")

def process_vulnerabilities_from_csv(
    csv_file_path: str,
    output_json_path: str,
    generation_model_config: dict, # 更改
    evaluation_model_config: dict, # 更改
    enable_llm_evaluation: bool = True,
    max_workers: int = 4,
    verbose: bool = False
) -> None:
    """
    从CSV文件处理漏洞，生成修复并评估
    
    Args:
        csv_file_path: 输入CSV文件路径
        output_json_path: 输出JSON Lines文件路径
        generation_model_config: 用于Patch生成的LLM模型配置
        evaluation_model_config: 用于评估的LLM模型配置
        enable_llm_evaluation: 是否启用LLM自动评估
        max_workers: 最大并行工作线程数
        verbose: 是否输出详细信息
    """
    print(f"开始简单并行漏洞处理，CSV文件: {csv_file_path}")
    print(f"输出将写入: {output_json_path} (JSON Lines格式)")
    print(f"最大工作线程数: {max_workers}")
    print(f"LLM评估: {'启用' if enable_llm_evaluation else '禁用'}")
    # 打印模型配置信息时，可以考虑打印 provider 和 model_name
    gen_provider = generation_model_config.get('provider', 'N/A')
    gen_model_name = generation_model_config.get('model_name', 'N/A')
    print(f"Patch生成模型: Provider={gen_provider}, Model={gen_model_name}")
    if enable_llm_evaluation:
        eval_provider = evaluation_model_config.get('provider', 'N/A')
        eval_model_name = evaluation_model_config.get('model_name', 'N/A')
        print(f"Patch评估模型: Provider={eval_provider}, Model={eval_model_name}")

    output_lock = threading.Lock()
 
    # 初始化输出文件
    try:
        with open(output_json_path, 'w', encoding='utf-8') as outfile:
            pass  # 清空文件或创建文件
        print(f"输出文件 {output_json_path} 已初始化")
    except IOError as e:
        print(f"错误: 初始化输出文件失败 {output_json_path}: {e}")
        return

    tasks_to_submit = []
    try:
        with open(csv_file_path, mode='r', encoding='utf-8', newline='') as infile:
            reader = csv.DictReader(infile)
            if not reader.fieldnames:
                print(f"错误: CSV文件 {csv_file_path} 为空或没有标题行")
                return

            required_columns = ['code_before', 'code_after', 'CWE ID']
            missing_columns = [col for col in required_columns if col not in reader.fieldnames]
            if missing_columns:
                print(f"错误: CSV文件 {csv_file_path} 缺少必需列: {', '.join(missing_columns)}")
                return

            for row_idx, row_data in enumerate(reader):
                tasks_to_submit.append({
                    "row_idx": row_idx,
                    "original_code": row_data.get('code_before'),
                    "fixed_code": row_data.get('code_after'),
                    "cwe_id": row_data.get('CWE ID'),
                    "trigger_path_value": row_data.get('trigger_path'),
                    "enable_llm_evaluation": enable_llm_evaluation,
                    "output_json_path": output_json_path,
                    "output_lock": output_lock,
                    "generation_model_config": generation_model_config, # 传递
                    "evaluation_model_config": evaluation_model_config, # 传递
                    "verbose": verbose
                })
        
        if not tasks_to_submit:
            print(f"从 {csv_file_path} 中没有找到要处理的任务")
            return

        print(f"从CSV准备了 {len(tasks_to_submit)} 个任务，提交到线程池...")

        processed_count = 0
        with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
            future_to_row_idx = {
                executor.submit(
                    _process_single_row,
                    task["row_idx"], task["original_code"], task["fixed_code"],
                    task["cwe_id"], task["trigger_path_value"], task["enable_llm_evaluation"],
                    task["output_json_path"], task["output_lock"],
                    task["generation_model_config"], task["evaluation_model_config"], # 传递
                    task["verbose"]
                ): task["row_idx"] for task in tasks_to_submit
            }
 
            for future in concurrent.futures.as_completed(future_to_row_idx):
                row_idx_completed = future_to_row_idx[future]
                try:
                    future.result()  # 等待任务完成并抛出异常（如果有）
                    processed_count += 1
                    if verbose:
                        print(f"任务 CSV行 {row_idx_completed + 2} 完成 ({processed_count}/{len(tasks_to_submit)})")
                except Exception as e:
                    processed_count += 1
                    print(f"处理 CSV行 {row_idx_completed + 2} 时发生错误: {e} ({processed_count}/{len(tasks_to_submit)})")
        
        print(f"\n所有 {len(tasks_to_submit)} 个任务已提交并完成处理")
        print(f"预期结果总数: {processed_count} (应该与任务数匹配)")

    except FileNotFoundError:
        print(f"错误: 输入CSV文件未找到: {csv_file_path}")
    except csv.Error as csve:
        print(f"错误: CSV解析错误 {csv_file_path}: {csve}")
    except IOError as ioe:
        print(f"错误: 处理 {csv_file_path} 时发生I/O错误: {ioe}")
    except Exception as e_global:
        print(f"CSV处理或任务提交过程中发生意外全局错误: {e_global}")

    print(f"\n处理完成。结果保存在: {output_json_path}")

def test_simple_repair(
    # 更改：测试模型现在是配置字典
    generation_model_config_for_test: dict = {
        "provider": "openrouter",
        "model_name": "anthropic/claude-3.5-sonnet",
        # "api_key": os.getenv("OPENROUTER_API_KEY") # API key should be handled by get_api_key_for_provider
    },
    evaluation_model_config_for_test: dict = {
        "provider": "openrouter",
        "model_name": "anthropic/claude-3.5-sonnet",
        # "api_key": os.getenv("OPENROUTER_API_KEY")
    }
):
    """测试简单修复功能"""
    print("\n--- 开始测试简单修复功能 ---")
    print(f"测试用生成模型配置: {generation_model_config_for_test}")
    print(f"测试用评估模型配置: {evaluation_model_config_for_test}")
    
    # 测试用例：缓冲区溢出漏洞
    sample_code = """static void
xmlDumpElementContent(xmlBufferPtr buf, xmlElementContentPtr content, int glob) {
    if (content == NULL) return;

    if (glob) xmlBufferWriteChar(buf, "(");
    switch (content->type) {
        case XML_ELEMENT_CONTENT_PCDATA:
            xmlBufferWriteChar(buf, "#PCDATA");
        break;
    case XML_ELEMENT_CONTENT_ELEMENT:
        if (content->prefix != NULL) {
        xmlBufferWriteCHAR(buf, content->prefix);
        xmlBufferWriteChar(buf, ":");
        }
        xmlBufferWriteCHAR(buf, content->name);
        break;
    case XML_ELEMENT_CONTENT_SEQ:
        if ((content->c1->type == XML_ELEMENT_CONTENT_OR) ||
            (content->c1->type == XML_ELEMENT_CONTENT_SEQ))
        xmlDumpElementContent(buf, content->c1, 1);
        else
        xmlDumpElementContent(buf, content->c1, 0);
            xmlBufferWriteChar(buf, " , ");
        if (((content->c2->type == XML_ELEMENT_CONTENT_OR) ||
            ((content->c2->type == XML_ELEMENT_CONTENT_SEQ) &&
         (content->c2->ocur != XML_ELEMENT_CONTENT_ONCE))))
        xmlDumpElementContent(buf, content->c2, 1);
        else
        xmlDumpElementContent(buf, content->c2, 0);
        break;
    }
}"""

    ground_truth = """static void
xmlDumpElementContent(xmlBufferPtr buf, xmlElementContentPtr content, int glob) {
    if (content == NULL) return;

    if (glob) xmlBufferWriteChar(buf, "(");
    switch (content->type) {
        case XML_ELEMENT_CONTENT_PCDATA:
            xmlBufferWriteChar(buf, "#PCDATA");
        break;
    case XML_ELEMENT_CONTENT_ELEMENT:
        if (content->prefix != NULL) {
        xmlBufferWriteCHAR(buf, content->prefix);
        xmlBufferWriteChar(buf, ":");
        }
        xmlBufferWriteCHAR(buf, content->name);
        break;
    case XML_ELEMENT_CONTENT_SEQ:
        if ((content->c1 != NULL) &&
               ((content->c1->type == XML_ELEMENT_CONTENT_OR) ||
                (content->c1->type == XML_ELEMENT_CONTENT_SEQ)))
        xmlDumpElementContent(buf, content->c1, 1);
        else
        xmlDumpElementContent(buf, content->c1, 0);
            xmlBufferWriteChar(buf, " , ");
        if ((content->c2 != NULL) &&
               ((content->c2->type == XML_ELEMENT_CONTENT_OR) ||
                ((content->c2->type == XML_ELEMENT_CONTENT_SEQ) &&
                 (content->c2->ocur != XML_ELEMENT_CONTENT_ONCE))))    
            xmlDumpElementContent(buf, content->c2, 1);
        else
        xmlDumpElementContent(buf, content->c2, 0);
        break;
    }
}"""

    cwe_id = "CWE-476"
    
    # 获取漏洞行号
    vuln_lines, _ = analyze_code_diff(sample_code, ground_truth)
    print(f"检测到的漏洞行号: {vuln_lines}")

    # 生成修复
    repair_results = generate_simple_vulnerability_repair(
        vulnerable_code=sample_code,
        cwe_id=cwe_id,
        vulnerable_line_numbers=vuln_lines,
        model_config=generation_model_config_for_test, # 使用测试模型配置
        verbose=True
    )
 
    if repair_results:
        print(f"\n--- 修复成功生成 {len(repair_results)} 个补丁 ---")
        
        # 显示所有生成的补丁
        for i, repair_result in enumerate(repair_results):
            print(f"\n=== 补丁 {i+1} ===")
            print(f"修复策略: {repair_result.get('repair_strategy', '未提供')}")
            print(f"漏洞分析: {repair_result.get('vulnerability_analysis', '未提供')}")
            print(f"关键变量: {repair_result.get('key_variables', [])}")
            
            print(f"\n生成的补丁 (Unified Diff):")
            print("```diff")
            print(repair_result.get('suggestion_patch', ''))
            print("```")
        
        # 批量评估修复质量
        print(f"\n--- 开始批量质量评估 ---")
        ground_truth_diff = '\n'.join(difflib.unified_diff(
            sample_code.splitlines(keepends=True),
            ground_truth.splitlines(keepends=True),
            fromfile='original_vulnerable.c',
            tofile='ground_truth_fixed.c',
            lineterm=''
        ))
        
        evaluations = evaluate_multiple_patches_quality(
            original_vulnerable_code=sample_code,
            patch_list=repair_results,
            ground_truth_patch=ground_truth_diff,
            cwe_id=cwe_id,
            evaluation_model_config=evaluation_model_config_for_test, # 使用测试模型配置
            verbose=True
        )
        
        # 显示评估结果汇总
        print(f"\n--- 评估结果汇总 ---")
        for evaluation in evaluations:
            patch_idx = evaluation["suggestion_index"] + 1
            result = evaluation["evaluation_result"]
            print(f"补丁 {patch_idx}: {result}")
            if evaluation.get("evaluation_details"):
                details = evaluation["evaluation_details"]
                print(f"  - 安全有效性: {details.get('security_effectiveness', 'N/A')}")
                print(f"  - 功能正确性: {details.get('functional_correctness', 'N/A')}")
                print(f"  - 实现质量: {details.get('implementation_quality', 'N/A')}")
            print(f"  - 解释: {evaluation.get('explanation', 'N/A')[:100]}...")
        
        # 显示Ground Truth对比
        print(f"\n--- Ground Truth补丁 ---")
        print("```diff")
        print(ground_truth_diff)
        print("```")
            
    else:
        print("\n--- 修复生成失败 ---")

    print("\n--- 测试结束 ---")

def main():
    """主函数"""
    dataset = "ICVul_2025_hard"

    
    # 在这里指定用于Patch生成和评估的LLM模型配置
    # 确保 API 密钥已在环境变量中设置 (e.g., OPENROUTER_API_KEY, OPENAI_API_KEY, AZURE_OPENAI_API_KEY, VOLCENGINE_API_KEY)
    # 或在 model_config 中直接提供 api_key (不推荐用于生产环境)
    patch_generation_model_config ={
        "provider": "openai",
        "model_name": "ep-20250329153117-pqs5b",
        "base_url": "https://ark.cn-beijing.volces.com/api/v3",
        "api_key": '2c504220-0507-499d-9369-a840379e8e27',
    } 
    # {
    #     "provider": "openrouter", # 例如 "openrouter", "openai", "azure", "volcengine"
    #     "model_name": "meta-llama/llama-3.3-70b-instruct",#"anthropic/claude-3.5-sonnet-20240620",
    #     "api_key": '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', # 通常由 get_api_key_for_provider 处理
    #     "base_url": "https://openrouter.ai/api/v1", # 可选，如果不是默认的
    # }
    patch_evaluation_model_config = {
        "provider": "openai",
        "model_name": "ep-20250329153117-pqs5b",
        "base_url": "https://ark.cn-beijing.volces.com/api/v3",
        "api_key": '2c504220-0507-499d-9369-a840379e8e27',
    }
    
    print(f"主程序配置：")
    print(f"  数据集: {dataset}")
    print(f"  Patch生成模型配置: Provider={patch_generation_model_config.get('provider')}, Model={patch_generation_model_config.get('model_name')}")
    print(f"  Patch评估模型配置: Provider={patch_evaluation_model_config.get('provider')}, Model={patch_evaluation_model_config.get('model_name')}")
    
    # 生成包含提供商和模型名称的文件名部分，替换路径中的非法字符
    gen_provider_name = patch_generation_model_config.get('provider', 'unknown_provider')
    gen_model_file_name = patch_generation_model_config.get('model_name', 'unknown_model').replace('/', '_')
    gen_file_part = f"{gen_model_file_name}"

    eval_provider_name = patch_evaluation_model_config.get('provider', 'unknown_provider')
    eval_model_file_name = patch_evaluation_model_config.get('model_name', 'unknown_model').replace('/', '_')
    eval_file_part = f"{eval_provider_name}_{eval_model_file_name}"

    output_filename = f"simple_{gen_file_part}.json"
    
    process_vulnerabilities_from_csv(
        csv_file_path=f"/mnt/projects/unnamed/datasets/{dataset}/processed_vulnerabilities.csv",
        output_json_path=f"/mnt/projects/unnamed/datasets/{dataset}/{output_filename}",
        generation_model_config=patch_generation_model_config,
        evaluation_model_config=patch_evaluation_model_config,
        enable_llm_evaluation=True,
        max_workers=32,
        verbose=True
    )
 
if __name__ == '__main__':
    main()
